from pathlib import Path, PureWindowsPath

Records = list[dict]

COMPANIES = {
    "migdal": "מגדל",
    "menorah": "מנורה",
    "harel": "הראל",
    "phenix": "פניק<PERSON>",
    "hcshara": "הכשר<PERSON>",
    "klal": "כלל",
    "eylon": "אי<PERSON><PERSON>ו<PERSON>",
}
TRANSLATION = {he: en for en, he in COMPANIES.items()}
DATE_INP_FMT = r"%Y-%m-%dT%H:%M:%S.%fZ"

EXCEL_PATH = Path(PureWindowsPath(r"rates_sources\rates.xlsx"))
CSV_PATH = EXCEL_PATH.with_suffix(".csv")
