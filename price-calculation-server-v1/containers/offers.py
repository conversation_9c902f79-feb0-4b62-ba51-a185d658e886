from functools import cached_property
from typing import Optional, TypedDict, NotRequired

import pandas as pd

from constants import COMPANIES, Records
from containers.discounts import (
    DiscountPlan,
)
from containers.insurance import Customer, Loan
from containers.premium import (
    NonDiscountedPremium,
    DiscountedPremium,
    BlankPremium,
    PremiumInterface,
)


class AgeDict(TypedDict):
    age: pd.Series
    age2: NotRequired[pd.Series]


class PricesDevelopmentTable(TypedDict):
    year: pd.Series
    incremental_cost: pd.Series
    monthly_payment: pd.Series  #  = avg_monthly_payment
    max_monthly_payment: pd.Series
    age: pd.Series
    age2: NotRequired[pd.Series]


# for each company:
class OfferRecord(TypedDict):
    name: str
    monthly1stYear: float
    monthly: float
    total: float
    prices_development: Records
    discountId: Optional[str]  # would be null if none applied


class Offers:
    def __init__(
        self,
        *,
        discounts_plans: Records,
        customers: Records,
        loans: Records,
    ) -> None:
        self.net_loan = Loan.get_net_loan(loans)
        self.loan_years = self.net_loan.index.to_series() + 1
        self.customers = Customer.from_records(customers, self.net_loan)
        self.available_plans = DiscountPlan.get_available_plans(
            discounts_plans,
            self.available_companies,
            self.customers,
            self.net_loan,
            self.total_tarrif,
        )

    @cached_property
    def available_companies(self) -> set[str]:
        companies_with_tarrif = set.intersection(
            *(set(c.tarrif.columns) for c in self.customers)
        )

        if not companies_with_tarrif:
            age_details = "\n".join(
                (f"[{c.age} ,{c._last_age}]" for c in self.customers)
            )

            raise Customer.InvalidCustomerError(
                f"customers do not have any common available companies for their ages:\n{age_details}"
            )
        return companies_with_tarrif

    @cached_property
    def total_tarrif(self):
        intersection = list(self.available_companies)
        total = sum(c.tarrif[intersection] for c in self.customers)
        if isinstance(total, int):
            raise ValueError("no customers data")
        return total

    @property
    def offers(self) -> list[OfferRecord]:
        discounted_companies = set(p.company for p in self.available_plans)
        non_discounted_companies = self.available_companies - discounted_companies
        missing_companies = set(COMPANIES) - self.available_companies

        discounted_premiums = DiscountedPremium.from_discounts(
            self.available_plans, self.net_loan, self.total_tarrif
        )

        non_discounted_premiums = {
            company: NonDiscountedPremium(self.net_loan, self.total_tarrif[company])
            for company in non_discounted_companies
        }

        missing_premiums = dict.fromkeys(missing_companies, BlankPremium(self.net_loan))

        all_premiums = discounted_premiums | non_discounted_premiums | missing_premiums

        return [
            {
                "name": company,
                "monthly": float(premium.avg_monthly.mean()),  # avg of monthly avgs
                "monthly1stYear": float(premium.max_monthly.iat[0]),  # max of 1st year
                "total": premium.total,
                "prices_development": self.get_prices_development(premium),
                "discountId": premium.discount_id,
            }
            for company, premium in all_premiums.items()
        ]

    def get_prices_development(self, premium: PremiumInterface):
        data: PricesDevelopmentTable = {
            "year": self.loan_years,
            "incremental_cost": self.net_loan,
            "monthly_payment": premium.avg_monthly,
            "max_monthly_payment": premium.max_monthly,
            **self.age_dict,
        }
        return pd.DataFrame(data).to_dict("records")

    @property
    def age_dict(self) -> AgeDict:
        match [c.age_series for c in self.customers]:
            case [a]:
                return {"age": a}
            case [a, b]:
                return {"age": a, "age2": b}
            case _:
                raise ValueError("not 1 or 2 customer records.")
