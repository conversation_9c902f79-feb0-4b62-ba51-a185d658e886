# syntax=docker/dockerfile:1

ARG PYTHON_VERSION=3.11.6

FROM python:${PYTHON_VERSION}-slim

LABEL fly_launch_runtime="flask"

# Install system dependencies
RUN apt-get update && apt-get install -y \
    libxml2-dev \
    libxslt-dev \
    gcc \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /code

# Upgrade pip and install setuptools
RUN pip3 install --no-cache-dir --upgrade pip setuptools wheel

COPY requirements.txt requirements.txt
RUN pip3 install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8080

CMD [ "python3", "-m" , "flask", "run", "--host=0.0.0.0", "--port=8080"]
