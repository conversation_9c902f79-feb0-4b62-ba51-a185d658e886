import * as z from "zod";
import { RefinementCtx } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { SafeParseReturnType } from "zod";
import { isIsraeliIdValid } from "@/utils/israeli-id-validator";
import { dt } from "@/utils/i18n-utils";

// noinspection TypeScriptValidateTypes
export const optionalUuidSchema = z.string().uuid().nullish();
// .transform((e) => (e === "" ? undefined : e));

export const InterestType = {
  change: "change",
  fixed: "fixed",
};

export const LinkageType = {
  fixed: "fixed",
  cpi: "cpi",
  dollar: "dollar",
};

export const LoanType = {
  ballon: "ballon",
  shpizer: "shpizer",
  equal: "equal",
};

export const LoanTrack = z.object({
  balance: z.number(),
  // endDate: z.coerce.date(),
  loanTrackYears: z.number(),
  interest_rate: z.number(),
  interest_type: z.nativeEnum(InterestType).nullish(),
  // linkage_type: z.nativeEnum(LinkageType),
  loan_type: z.nativeEnum(LoanType).nullish(),
});

export const LoanTracks = z.array(LoanTrack);

export const HealthDeclaration = z.record(z.any()).nullish();

export const Successor = z.object({
  first_name: z.string(),
  last_name: z.string(),
  id_card_number: z.string(),
  birthDate: z.coerce.date(),
  relationship: z.string(),
  compensationPct: z.number(),
  sameSuccessor: z.boolean(),
});

export const Successors = z.array(Successor).nullish();

export const PriceDevelopment = z.object({
  year: z.number(),
  // insurance_value: z.number(),
  age1: z.number().optional(),
  age2: z.number().optional(),
  monthly_payment: z.number(), // monthly_payment_avg
  max_monthly_payment: z.number().nullish(), // backword compatibility, remove later
  incremental_cost: z.number(),
});

export const PricesDevelopment = z.array(PriceDevelopment).nullish();

export const DiscountItem = z.object({
  discount: z.number(),
  yearStart: z.number().int().nullish(),
  yearEnd: z.number().int().nullish(),
  monthStart: z.number().int().nullish(),
  monthEnd: z.number().int().nullish(),
});

export const Discounts = z.array(DiscountItem).nullish();

export const CreditCardInfo = z.object({
  cardExp: z.coerce.string(),
});
