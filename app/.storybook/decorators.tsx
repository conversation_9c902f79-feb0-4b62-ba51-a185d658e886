import "../src/styles/globals.css";
import "react-datepicker/dist/react-datepicker.css";
import i18n from "./i18next";
import { Suspense, useEffect } from "react";
import { I18nextProvider, useTranslation } from "react-i18next";
import { TooltipProvider } from "@radix-ui/react-tooltip";

import "../src/utils/zod-meta";
import { TrpcProvider } from "@/components/common/msw/TrpcProvider";

// Wrap your stories in the I18nextProvider component
// export decorators for storybook to wrap your stories in

const StoryWrap = ({ children }) => {
  const { i18n } = useTranslation(["common", "zod"]);
  const language = i18n.language;

  useEffect(() => {
    document
      .querySelector("html")
      .setAttribute("dir", language === "he" ? "rtl" : "ltr");
  }, [language]);

  return children;
};

export const withI18next = (Story) => {
  return (
    // This catches the suspense from components not yet ready (still loading translations)
    // Alternative: set useSuspense to false on i18next.options.react when initializing i18next
    <Suspense fallback={<div>loading translations...</div>}>
      <TooltipProvider>
        <I18nextProvider i18n={i18n}>
          <TrpcProvider>
            <StoryWrap>
              <Story />
            </StoryWrap>
          </TrpcProvider>
        </I18nextProvider>
      </TooltipProvider>
    </Suspense>
  );
};
