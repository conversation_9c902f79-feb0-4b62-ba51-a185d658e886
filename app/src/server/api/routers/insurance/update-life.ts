/* eslint-disable */
import { z } from "zod";
import { protectedProcedure } from "../../trpc";
import { customerModel, lifeInsuranceModel } from "../../../../../prisma/zod";
import {
  HealthDeclaration,
  LoanTrack,
  LoanTracks,
  Successor,
  Successors,
} from "../../../../../prisma/zod-add-schema";
import type { InsuranceStatus, PrismaClient } from "@prisma/client";
import { Prisma } from "@prisma/client";
import { TRPCError } from "@trpc/server";
import { checkIsUser } from "@/server/api/utils";
import {
  baseLifeInsuranceInclude,
  baseLifeInsuranceModel,
  BaseLifeInsuranceType,
  Input,
} from "@/server/api/routers/insurance/types";
import { validateUserData } from "@/server/api/routers/insurance/get-life";
import { add } from "date-fns";
import { updateChangeAudit } from "@/server/process/change-audit";
import { maybeSendNotification } from "@/server/process/notifications/wizard-end-notification";
import { env } from "@/env.mjs";
import NullableJsonNullValueInput = Prisma.NullableJsonNullValueInput;
import { castError } from "@/utils/errors";
import { addSystemEvent } from "@/server/process/system-events";
import { sendLinksToCustomers } from "@/trigger/send-links-to-customers";
import { crmSurenseUpdate } from "@/trigger/crm-surense-update";
import { isDev } from "@/utils/nextjs-utils";
import { formatPhoneNumber } from "@/utils/phone";
import { healthDeclarationAdditional } from "@/server/process/health-declaration-additional";
import { getSignedUrl } from "@/server/process/supabase-server-utils";
import { getPDFTable } from "@/server/process/pdf-extract-table/job-call-pdf-extract-table";
import {
  calculateLoanTrack,
  parseLoanTracks,
} from "@/server/api/routers/insurance/loan-tracks";

interface customerUpdateInput {
  successor_count: number | undefined;
  successor: any;
}

const validateUser = async (
  id: string,
  userId: string,
  role: string,
  prisma: PrismaClient
) => {
  const answer = await prisma.lifeInsurance.findFirstOrThrow({
    where: { id },
    select: {
      id: true,
      advisorId: true,
      status: true, // need to change status
      loan_tracks_count: true, // need to cut array
      lifeInsuranceCustomer: {
        select: {
          customer: { select: { id: true, userId: true, card_id: true } },
        },
      },
    },
  });

  await validateUserData(userId, role, answer, prisma);
  return answer;
};

export const createLifeInsurance = protectedProcedure
  .input(lifeInsuranceModel.pick({ number_of_customers: true, source: true }))
  .output(baseLifeInsuranceModel)
  .mutation(async ({ ctx, input }) => {
    const advisorId = checkIsUser(ctx);

    const updated = await ctx.prisma.lifeInsurance.create({
      data: { ...input, advisorId },
      include: baseLifeInsuranceInclude,
    });

    const page = "0-0:create";

    await maybeSendNotification(
      ctx.prisma,
      updated.id,
      "advisor",
      page,
      "admin_advisor_form_started",
      {
        id: updated.id,
        adminUrl: `${env.UI_URL}/admin#/lifeInsurance/${updated.id}`,
      }
    );

    await updateChangeAudit(
      ctx.prisma,
      advisorId,
      "life-insurance",
      "createLifeInsurance",
      page,
      updated.id,
      input,
      updated
    );

    return updated;
  });

export const updateLifeInsurance = protectedProcedure
  .input(
    Input.extend({
      data: lifeInsuranceModel
        .omit({
          id: true,
          advisorId: true,
          createdAt: true,
          loan_tracks: true,
          // prices_development: true,
        })
        .partial(),
    })
  )
  .output(baseLifeInsuranceModel)
  .mutation(async ({ ctx, input }) => {
    let errorMessage: string | undefined = undefined;
    const { id, data, role, page } = input;
    const userId = checkIsUser(ctx);
    const { status } = await validateUser(id, userId, role, ctx.prisma);
    const shouldSendNotifications =
      page?.includes(":sendLinksToComplete") && role !== "customer";

    const approvalUpload = page?.includes(":approvalUpload");
    const customerFormEnd =
      // this sometimes not called, so we also check in insurance.updateCustomer
      page?.includes(":end_askForBuildingInsurance") && role === "customer";

    const checkCanChangeCondition = !customerFormEnd;

    let loanTracksInfo = undefined;

    if (approvalUpload) {
      try {
        const oldData = await ctx.prisma.lifeInsurance.findFirstOrThrow({
          where: { id },
          select: {
            approval_file: true,
            approval_file_parsing: true,
            insurance_start_date: true,
          },
        });
        if (!data.approval_file && oldData.approval_file_parsing) {
          loanTracksInfo = {
            loan_tracks_count: 0,
            loan_tracks: [],
            total: 0,
            maxDate: null,
            mortgage_years: 0,
            approval_file_parsing: false,
          };
        } else if (
          data.approval_file &&
          oldData.approval_file !== data.approval_file
        ) {
          const url = await getSignedUrl(data.approval_file);
          console.log(`muly:update-life:approvalUpload`, {
            input,
            url,
          });
          const loanTracks = await getPDFTable(url);
          const { total, maxYears, maxDate } = calculateLoanTrack(
            loanTracks,
            oldData.insurance_start_date
          );

          loanTracksInfo = {
            loan_tracks_count: loanTracks.length,
            loan_tracks: loanTracks,
            total,
            maxDate,
            mortgage_years: maxYears,
            approval_file_parsing: true,
          };
        }
      } catch (error) {
        const err = castError(error);
        console.error(
          `Failed to parse update-life:approvalUpload ${err.message}`,
          {
            err: err.stack,
            input,
          }
        );

        await addSystemEvent(
          `Failed to analyze approvalUpload ${err.message}`,
          { err: err.stack, input },
          true
        );

        errorMessage = "Failed to parse PDF";
        // throw new TRPCError({
        //   code: "NOT_FOUND",
        //   message: "Failed to parse PDF",
        // });
      }
    }

    try {
      const updated: BaseLifeInsuranceType & {
        advisor?: {
          id: string;
          first_name: string | null;
          last_name: string | null;
        };
      } = await ctx.prisma.lifeInsurance.update({
        where: { id, canChange: checkCanChangeCondition ? true : undefined },
        // @ts-ignore
        data: {
          ...data,
          // canChange,
          // status: changedStatus,
          ...(role === "customer"
            ? { customer_page: page, lastChangeByCustomer: new Date() }
            : { advisor_page: page }),
          ...(loanTracksInfo ? loanTracksInfo : {}),
        },
        include: {
          ...baseLifeInsuranceInclude,
          ...(shouldSendNotifications
            ? {
                advisor: {
                  select: {
                    id: true,
                    first_name: true,
                    last_name: true,
                  },
                },
              }
            : {}),
        },
      });

      if (shouldSendNotifications) {
        try {
          await sendLinksToCustomers.trigger({
            insuranceId: id,
            role,
            page,
            sendLinksMode: updated.canChangeLockedAt ? "reopen" : "end-process",
          });
          await crmSurenseUpdate.trigger({ insuranceId: id });
        } catch (err) {
          const error = castError(err);
          if (isDev()) {
            console.log(
              `muly:update-life:sendLinksToCustomers.invoke not working on dev env`
            );
          } else {
            console.error(
              `sendLinksToCustomers: ${error.message} isDev=${isDev()}`,
              {
                error: error.stack,
                id,
                data,
                page,
              }
            );

            await addSystemEvent(
              `failed to call trigger.dev sendLinksToCustomers`,
              { error: error.stack, id, data, page },
              true
            );

            throw error;
          }
        }
        // await sendNotificationsToCustomers(ctx.prisma, updated, id, role, page);
      }

      if (customerFormEnd) {
        // also trigger lock and end of customer process emails
        await maybeSendNotification(
          ctx.prisma,
          id,
          role,
          "customer-process-end",
          "admin_customer_form_completed",
          {
            id,
            adminUrl: `${env.UI_URL}/admin#/lifeInsurance/${id}`,
          }
        );
      }

      await updateChangeAudit(
        ctx.prisma,
        userId,
        "life-insurance",
        "updateLifeInsurance",
        page,
        id,
        input,
        updated
      );

      return { ...updated, error: errorMessage };
    } catch (err) {
      const error = castError(err);
      console.error(`updateLifeInsurance: ${error.message}`, {
        error: error.stack,
        id,
        data,
        page,
      });

      const isLockedRecord = await ctx.prisma.lifeInsurance.findFirst({
        where: { id },
      });

      await addSystemEvent(
        "updateLifeInsurance",
        {
          input,
          error: error.message,
          id,
          data,
          page,
        },
        true
      );

      if (isLockedRecord && isLockedRecord.canChange === false) {
        throw new TRPCError({
          code: "PRECONDITION_FAILED",
          message: "Record is locked",
        });
      }

      throw error;
    }
  });

export const sendLifeInsuranceLinksToCustomers = protectedProcedure
  .input(z.object({ id: z.string() }))
  .output(z.void())
  .mutation(async ({ ctx, input }) => {
    const { id } = input;
    const userId = checkIsUser(ctx);
    const role = "advisor";
    await validateUser(id, userId, role, ctx.prisma);

    await sendLinksToCustomers.trigger({
      insuranceId: id,
      role,
      page: undefined,
      sendLinksMode: "resend",
    });

    //
    // const shouldSendNotifications = true;
    //
    // const lifeInsurance: BaseLifeInsuranceType & {
    //   advisor?: {
    //     id: string;
    //     first_name: string | null;
    //     last_name: string | null;
    //   };
    // } = await ctx.prisma.lifeInsurance.findFirstOrThrow({
    //   where: { id },
    //   include: {
    //     ...baseLifeInsuranceInclude,
    //     ...(shouldSendNotifications
    //       ? {
    //           advisor: {
    //             select: {
    //               id: true,
    //               first_name: true,
    //               last_name: true,
    //             },
    //           },
    //         }
    //       : {}),
    //   },
    // });
    //
    // await sendNotificationsToCustomers(
    //   ctx.prisma,
    //   lifeInsurance,
    //   id,
    //   role,
    //   undefined
    // );
    //
    // return;
  });

export const updateLoanTracks = protectedProcedure
  .input(Input.extend({ loanTracks: LoanTracks }))
  .output(baseLifeInsuranceModel)
  .mutation(async ({ ctx, input }) => {
    const { id, loanTracks, role, page } = input;
    const userId = checkIsUser(ctx);
    await validateUser(id, userId, role, ctx.prisma);

    const answer = await ctx.prisma.lifeInsurance.findFirstOrThrow({
      where: { id, canChange: true },
    });
    const updatedLoanTracks = loanTracks;

    const { total, maxYears, maxDate } = calculateLoanTrack(
      updatedLoanTracks,
      answer.insurance_start_date
    );

    const updated = await ctx.prisma.lifeInsurance.update({
      where: { id },
      data: {
        total,
        maxDate,
        mortgage_years: maxYears,
        loan_tracks: updatedLoanTracks,
        loan_tracks_count: updatedLoanTracks.length,
        ...(role === "customer"
          ? { customer_page: page }
          : { advisor_page: page }),
      },
      include: baseLifeInsuranceInclude,
    });

    await updateChangeAudit(
      ctx.prisma,
      userId,
      "life-insurance",
      "updateLoanTracks",
      page,
      id,
      input,
      updated
    );
    return updated;
  });

export const updateLoanTrack = protectedProcedure
  .input(Input.extend({ loanTrackIdx: z.number(), data: LoanTrack }))
  .output(baseLifeInsuranceModel)
  .mutation(async ({ ctx, input }) => {
    const { id, data, role, loanTrackIdx, page } = input;
    const userId = checkIsUser(ctx);
    await validateUser(id, userId, role, ctx.prisma);

    const answer = await ctx.prisma.lifeInsurance.findFirstOrThrow({
      where: { id, canChange: true },
    });

    const loan_tracks_count = answer.loan_tracks_count;
    // console.log(`muly:updateLoanTrack A`, {
    //   id,
    //   data,
    //   role,
    //   loanTrackIdx,
    //   input,
    //   loan_tracks_count: answer.loan_tracks_count,
    // });

    if (!loan_tracks_count) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "No loan_tracks_count found",
      });
    }

    let loanTracks = parseLoanTracks(
      answer.loan_tracks,
      answer.loan_tracks_count
    );
    loanTracks[loanTrackIdx] = data;
    const { total, maxYears, maxDate } = calculateLoanTrack(
      loanTracks,
      answer.insurance_start_date
    );

    const updated = await ctx.prisma.lifeInsurance.update({
      where: { id },
      data: {
        total,
        maxDate,
        mortgage_years: maxYears,
        loan_tracks: loanTracks as any,
        ...(role === "customer"
          ? { customer_page: page }
          : { advisor_page: page }),
      },
      include: baseLifeInsuranceInclude,
    });

    await updateChangeAudit(
      ctx.prisma,
      userId,
      "life-insurance",
      "updateLoanTrack",
      page,
      id,
      input,
      updated
    );
    return updated;
  });

export const deleteLoanTrack = protectedProcedure
  .input(Input.extend({ loanTrackIdx: z.number() }))
  .output(baseLifeInsuranceModel)
  .mutation(async ({ ctx, input }) => {
    const { id, role, loanTrackIdx, page } = input;
    const userId = checkIsUser(ctx);
    await validateUser(id, userId, role, ctx.prisma);

    const answer = await ctx.prisma.lifeInsurance.findFirstOrThrow({
      where: { id, canChange: true },
    });

    const old_loan_tracks_count = answer.loan_tracks_count;

    if (!old_loan_tracks_count) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "No loan_tracks_count found",
      });
    }

    const loan_tracks_count = old_loan_tracks_count - 1;

    let loanTracks = parseLoanTracks(
      answer.loan_tracks,
      answer.loan_tracks_count
    );
    loanTracks.splice(loanTrackIdx, 1);
    const { total, maxYears, maxDate } = calculateLoanTrack(
      loanTracks,
      answer.insurance_start_date
    );

    const updated = await ctx.prisma.lifeInsurance.update({
      where: { id },
      data: {
        total,
        maxDate,
        mortgage_years: maxYears,
        loan_tracks_count,
        loan_tracks: loanTracks as any,
        ...(role === "customer"
          ? { customer_page: page }
          : { advisor_page: page }),
      },
      include: baseLifeInsuranceInclude,
    });

    await updateChangeAudit(
      ctx.prisma,
      userId,
      "life-insurance",
      "deleteLoanTrack",
      page,
      id,
      input,
      updated
    );
    return updated;
  });

export const updateCustomer = protectedProcedure
  .input(
    Input.extend({
      customerIdx: z.number(),
      data: customerModel
        .omit({
          healthDeclaration: true,
          successor: true,
        })
        .partial(),
    })
  )
  .output(baseLifeInsuranceModel)
  .mutation(async ({ ctx, input }) => {
    const { id, data, role, customerIdx, page } = input;
    const userId = checkIsUser(ctx);
    await validateUser(id, userId, role, ctx.prisma);
    if (customerIdx < 0 || customerIdx > 1) {
      throw new TRPCError({
        code: "PRECONDITION_FAILED",
        message: `Invalid customer index: ${customerIdx}`,
      });
    }

    // console.log(`muly:updateCustomer`, { data, page, role });

    const { id: customerId, ...updateData } = data;
    const formatedPhone = formatPhoneNumber(updateData.phone);
    try {
      let updated;
      if (!customerId) {
        updated = await ctx.prisma.lifeInsurance.update({
          where: { id, canChange: true },
          data: {
            ...(role === "customer"
              ? { customer_page: page }
              : { advisor_page: page }),
            lifeInsuranceCustomer: {
              create: [
                {
                  sort: customerIdx,
                  customer: { create: { ...updateData, formatedPhone } },
                },
              ],
            },
          },
          include: baseLifeInsuranceInclude,
        });
      } else {
        const answer = await ctx.prisma.customer.update({
          where: {
            id: customerId,
            lifeInsuranceCustomer: {
              some: { lifeInsurance: { canChange: true } },
            },
          },
          // TODO update page
          data: {
            ...updateData,
            formatedPhone,
          },
          select: {
            lifeInsuranceCustomer: {
              where: { lifeInsuranceId: id },
              select: {
                lifeInsurance: {
                  include: baseLifeInsuranceInclude,
                },
              },
            },
          },
        });

        const lifeInsuranceCustomer = answer.lifeInsuranceCustomer[0];
        if (!lifeInsuranceCustomer) {
          throw new Error("Expected to find the lifeInsurance record");
        }
        updated = lifeInsuranceCustomer.lifeInsurance;
      }

      const number_of_customers = updated.number_of_customers;
      if (page?.includes(":customer0_details1") && role === "customer") {
        await maybeSendNotification(
          ctx.prisma,
          id,
          role,
          page,
          "admin_customer_form_started",
          {
            id,
            adminUrl: `${env.UI_URL}/admin#/lifeInsurance/${id}`,
          }
        );
      } else if (
        (number_of_customers === 1 && page?.includes(":customer0_signature")) ||
        page?.includes(":customer1_signature")
      ) {
        // also trigger lock and end of customer process emails
        await maybeSendNotification(
          ctx.prisma,
          id,
          role,
          "customer-process-end",
          "admin_customer_form_completed",
          {
            id,
            adminUrl: `${env.UI_URL}/admin#/lifeInsurance/${id}`,
          }
        );
      }

      await updateChangeAudit(
        ctx.prisma,
        userId,
        "life-insurance",
        "updateCustomer",
        page,
        id,
        input,
        updated
      );
      return updated;
    } catch (err) {
      const error = castError(err);
      console.error(`updateCustomer: ${error.message}`, {
        error: error.stack,
        input,
      });

      const isLockedRecord = await ctx.prisma.lifeInsurance.findFirst({
        where: { id },
      });

      await addSystemEvent(
        "updateCustomer",
        {
          input,
          error: error.message,
          id,
          data,
          page,
        },
        true
      );

      if (isLockedRecord && isLockedRecord.canChange === false) {
        throw new TRPCError({
          code: "PRECONDITION_FAILED",
          message: "Record is locked",
        });
      }

      throw error;
    }
  });

export const updateHealthDeclaration = protectedProcedure
  .input(
    Input.extend({
      customerIdx: z.number(),
      customerId: z.string().uuid(),
      data: HealthDeclaration,
    })
  )
  .output(baseLifeInsuranceModel)
  .mutation(async ({ ctx, input }) => {
    const { id, data, role, customerIdx, customerId, page } = input;
    const userId = checkIsUser(ctx);
    await validateUser(id, userId, role, ctx.prisma);

    const answer = await ctx.prisma.customer.update({
      where: {
        id: customerId,
        lifeInsuranceCustomer: { some: { lifeInsurance: { canChange: true } } },
      },
      // @ts-ignore
      data: { healthDeclaration: data ? data : NullableJsonNullValueInput },
      select: {
        lifeInsuranceCustomer: {
          where: { lifeInsuranceId: id },
          select: {
            lifeInsurance: {
              include: baseLifeInsuranceInclude,
            },
          },
        },
      },
    });

    // @ts-ignore
    const lifeInsuranceCustomer = answer.lifeInsuranceCustomer[0];
    if (!lifeInsuranceCustomer) {
      throw new Error("Expected to find the lifeInsurance record");
    }
    const updated = lifeInsuranceCustomer.lifeInsurance;

    await healthDeclarationAdditional(ctx.prisma, id);

    await updateChangeAudit(
      ctx.prisma,
      userId,
      "life-insurance",
      "updateHealthDeclaration",
      page,
      id,
      input,
      updated
    );
    return updated;
  });

export const updateSuccessor = protectedProcedure
  .input(
    Input.extend({
      customerIdx: z.number(),
      successorIdx: z.number(),
      data: Successor.extend({
        customerId: z.string(),
        successor_count: z.number().optional(),
      }),
    })
  )
  .output(baseLifeInsuranceModel)
  .mutation(async ({ ctx, input }) => {
    const { id, data, role, customerIdx, successorIdx, page } = input;
    const userId = checkIsUser(ctx);
    await validateUser(id, userId, role, ctx.prisma);

    const { customerId, successor_count, ...successor } = data;

    const customer = await ctx.prisma.customer.findFirstOrThrow({
      where: {
        id: customerId,
        lifeInsuranceCustomer: { some: { lifeInsurance: { canChange: true } } },
      },
      select: {
        successor: true,
      },
    });

    let successors = Successors.parse(customer.successor);
    if (!successors) {
      successors = [];
    }
    successors[successorIdx] = successor;

    const customData: customerUpdateInput = {
      successor_count,
      successor: successors as any,
    };

    const answer = await ctx.prisma.customer.update({
      where: { id: customerId },
      data: customData,
      select: {
        lifeInsuranceCustomer: {
          where: { lifeInsuranceId: id },
          select: {
            lifeInsurance: {
              include: baseLifeInsuranceInclude,
            },
          },
        },
      },
    });

    const lifeInsuranceCustomer = answer.lifeInsuranceCustomer[0];
    if (!lifeInsuranceCustomer) {
      throw new Error("Expected to find the lifeInsurance record");
    }

    const lifeInsurance = lifeInsuranceCustomer.lifeInsurance;
    // Copy to 2nd customer, need to copy only count
    const customer1 = lifeInsurance.lifeInsuranceCustomer[1]?.customer;
    if (
      customerIdx === 0 &&
      lifeInsurance.number_of_customers === 2 &&
      successor.sameSuccessor &&
      customer1 &&
      customer1.id
    ) {
      const count = successors.reduce(
        (count, { sameSuccessor }) => count + (sameSuccessor ? 1 : 0),
        0
      );

      if (!customer1.successor_count || count > customer1.successor_count) {
        await ctx.prisma.customer.update({
          where: { id: customer1.id },
          data: { successor_count: count },
        });
      }
    }

    const updated = lifeInsuranceCustomer.lifeInsurance;
    await updateChangeAudit(
      ctx.prisma,
      userId,
      "life-insurance",
      "updateSuccessor",
      page,
      id,
      input,
      updated
    );
    return updated;
  });

export const deleteSuccessor = protectedProcedure
  .input(
    Input.extend({
      customerIdx: z.number(),
      successorIdx: z.number(),
      customerId: z.string(),
    })
  )
  .output(baseLifeInsuranceModel)
  .mutation(
    async ({
      ctx,
      input: { id, role, customerIdx, successorIdx, customerId },
    }) => {
      const userId = checkIsUser(ctx);
      await validateUser(id, userId, role, ctx.prisma);

      if (successorIdx <= 0) {
        throw new TRPCError({
          code: "PRECONDITION_FAILED",
          message: "Cannot delete the first successor",
        });
      }

      const customer = await ctx.prisma.customer.findFirstOrThrow({
        where: {
          id: customerId,
          lifeInsuranceCustomer: {
            some: { lifeInsurance: { canChange: true } },
          },
        },
        select: {
          successor_count: true,
          successor: true,
        },
      });

      const successor_count = customer.successor_count || 1;
      let successors = Successors.parse(customer.successor);

      if (!successors) {
        successors = [];
      }

      if (successorIdx >= successor_count) {
        throw new TRPCError({
          code: "PRECONDITION_FAILED",
          message: "Cannot delete beyond the last successor",
        });
      }

      successors.splice(successorIdx, 1);

      const answer = await ctx.prisma.customer.update({
        where: { id: customerId },
        data: {
          successor_count: successor_count - 1,
          successor: successors as any,
        },
        select: {
          lifeInsuranceCustomer: {
            where: { lifeInsuranceId: id },
            select: {
              lifeInsurance: {
                include: baseLifeInsuranceInclude,
              },
            },
          },
        },
      });

      const lifeInsuranceCustomer = answer.lifeInsuranceCustomer[0];
      if (!lifeInsuranceCustomer) {
        throw new Error("Expected to find the lifeInsurance record");
      }
      return lifeInsuranceCustomer.lifeInsurance;
    }
  );
