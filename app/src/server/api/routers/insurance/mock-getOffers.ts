export const prices_development = [
  {
    year: 1,
    insurance_value: 0,
    age1: 54,
    age2: 1,
    monthly_payment: 219.17,
    incremental_cost: 304.11,
  },
  {
    year: 2,
    insurance_value: 0,
    age1: 55,
    age2: 2,
    monthly_payment: 272.05,
    incremental_cost: 1259.4,
  },
  {
    year: 3,
    insurance_value: 0,
    age1: 56,
    age2: 3,
    monthly_payment: 330.91,
    incremental_cost: 2106.48,
  },
  {
    year: 4,
    insurance_value: 0,
    age1: 57,
    age2: 4,
    monthly_payment: 368.67,
    incremental_cost: 2843,
  },
  {
    year: 5,
    insurance_value: 0,
    age1: 58,
    age2: 5,
    monthly_payment: 435.25,
    incremental_cost: 3462.69,
  },
  {
    year: 6,
    insurance_value: 0,
    age1: 59,
    age2: 6,
    monthly_payment: 454.86,
    incremental_cost: 3911.63,
  },
  {
    year: 7,
    insurance_value: 0,
    age1: 60,
    age2: 7,
    monthly_payment: 569.71,
    incremental_cost: 4209.5,
  },
  {
    year: 8,
    insurance_value: 0,
    age1: 61,
    age2: 8,
    monthly_payment: 600.59,
    incremental_cost: 4317.68,
  },
  {
    year: 9,
    insurance_value: 0,
    age1: 62,
    age2: 9,
    monthly_payment: 633.74,
    incremental_cost: 4909.5,
  },
  {
    year: 10,
    insurance_value: 0,
    age1: 63,
    age2: 10,
    monthly_payment: 664.8,
    incremental_cost: 5409.5,
  },
  {
    year: 11,
    insurance_value: 0,
    age1: 64,
    age2: 11,
    monthly_payment: 6.84,
    incremental_cost: 6109.5,
  },
  {
    year: 12,
    insurance_value: 0,
    age1: 65,
    age2: 12,
    monthly_payment: 6.93,
    incremental_cost: 6200.5,
  },
  {
    year: 13,
    insurance_value: 0,
    age1: 66,
    age2: 13,
    monthly_payment: 6.97,
    incremental_cost: 6300.5,
  },
  {
    year: 14,
    insurance_value: 0,
    age1: 67,
    age2: 14,
    monthly_payment: 6.99,
    incremental_cost: 6400.5,
  },
  {
    year: 15,
    insurance_value: 0,
    age1: 68,
    age2: 15,
    monthly_payment: 6.96,
    incremental_cost: 6500.5,
  },
  {
    year: 16,
    insurance_value: 0,
    age1: 69,
    age2: 16,
    monthly_payment: 6.73,
    incremental_cost: 6600.5,
  },
];

export const mockGetOffers = [
  {
    monthly1stYear: 50.11,
    monthly: 60.12,
    total: 40000.56,
    name: "phenix",
    recommended: true,
    prices_development,
  },
  {
    monthly1stYear: 60,
    monthly: 80,
    total: 51000,
    name: "harel",
    prices_development,
  },
  {
    monthly1stYear: 60,
    monthly: 80,
    total: 50000,
    name: "harel",
    prices_development,
  },
  {
    monthly1stYear: 70,
    monthly: 90,
    total: 60000,
    name: "menorah",
    prices_development,
  },
  {
    monthly1stYear: 80,
    monthly: 100,
    total: 70000,
    name: "klal",
    prices_development,
  },
  {
    monthly1stYear: 90,
    monthly: 110,
    total: 80000,
    name: "migdal",
    prices_development,
  },
  {
    monthly1stYear: 90,
    monthly: 110,
    total: 80000,
    name: "eylon",
    prices_development,
  },
];
