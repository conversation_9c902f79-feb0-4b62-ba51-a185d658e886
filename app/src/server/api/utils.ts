import { TRPCError } from "@trpc/server";
import type { PrismaClient } from "@prisma/client";

export const checkIsAdmin = ({ user }: any) => {
  // eslint-disable-next-line @typescript-eslint/no-unsafe-call
  if (!user?.email?.includes("@smapy.co.il")) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: `Not Smapy admin ${user?.id}`,
    });
  }
};

export const checkIsUser = ({ user }: any): string => {
  const userId = user?.id;
  if (!userId) {
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message: "Missing user.",
    });
  }

  return String(userId);
};

export const checkIsAdvisor = async ({
  prisma,
  user,
}: {
  prisma: PrismaClient;
  user: any;
}): Promise<string> => {
  const userId = user?.id;
  if (!userId) {
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message: "Missing user.",
    });
  }

  const userProfile = await prisma.userProfile.findUniqueOrThrow({
    where: { id: userId },
    select: { roles: true },
  });

  if (
    !userProfile.roles.includes("advisor") &&
    !userProfile.roles.includes("agent")
  ) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: `User ${userId} is not an agent or advisor.`,
    });
  }

  return String(userId);
};
