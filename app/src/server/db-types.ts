import type { z } from "zod";
import type { inferRouterInputs, inferRouterOutputs } from "@trpc/server";
import type { AppRouter } from "@/server/api/root";
import type { pdfFormModel } from "../../prisma/zod";
import { RelatedlifeInsuranceModel } from "../../prisma/zod";
import { api } from "@/utils/api";

type RouterInput = inferRouterInputs<AppRouter>;
type RouterOutput = inferRouterOutputs<AppRouter>;

export type ContactUsType = RouterInput["customer"]["contactUs"];
export type LifeInsuranceType = RouterOutput["insurance"]["getLifeInsurance"];
export type LifeInsuranceCustomerType =
  RouterOutput["insurance"]["getCustomer"];

export type PdfFormDataType = z.infer<typeof pdfFormModel>;

export type UserProfile = RouterOutput["customer"]["getUserProfile"];

export type ItemPriceInfo = RouterOutput["insurance"]["getOffers"][0];
export type CustomerInsuranceType =
  RouterOutput["customer"]["getCustomerInsurance"];
