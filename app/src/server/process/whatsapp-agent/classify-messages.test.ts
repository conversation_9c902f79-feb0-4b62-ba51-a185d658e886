// env-cmd -f .env globstar -- node --import tsx --test "./src/server/process/**/term-by-ai.test.ts"

import { test } from "node:test";
import assert from "node:assert/strict";
import { classifyMessage } from "./classify-message";

test("classifyMessage function", async (t) => {
  await t.test('should classify "Stop" messages correctly', async () => {
    const stopMessages = [
      "תודה, לא צריך יותר",
      "אל תשלחו עוד",
      "נא להפסיק התכתבות",
      "לא רלוונטי עבורי",
      "בקשת ביטול",
      "לא מעוניין בשירות",
      "הוצא אותי מהרשימה",
      "הסר מרשימת התפוצה",
      "בטל מנוי",
      "חסום הודעות",
    ];

    for (const message of stopMessages) {
      const result = await classifyMessage(message);
      assert.equal(
        result,
        "Stop",
        `Message "${message}" should be classified as Stop`
      );
    }
  });

  await t.test('should classify "Call" messages correctly', async () => {
    const callMessages = ["בבקשה התקשר", "תחזור אלי", "צריך עזרה"];

    for (const message of callMessages) {
      const result = await classifyMessage(message);
      assert.equal(
        result,
        "Call",
        `Message "${message}" should be classified as Call`
      );
    }
  });

  await t.test('should classify "Unknown" messages correctly', async () => {
    const unknownMessages = [
      "שלום, מה שלומך?",
      "תודה רבה על השירות",
      "מתי אתם פתוחים?",
      "כמה זה עולה?",
    ];

    for (const message of unknownMessages) {
      const result = await classifyMessage(message);
      assert.equal(
        result,
        "Unknown",
        `Message "${message}" should be classified as Unknown`
      );
    }
  });

  await t.test("should handle empty input", async () => {
    const result = await classifyMessage("");
    assert.equal(
      result,
      "Unknown",
      "Empty message should be classified as Unknown"
    );
  });

  await t.test("should handle non-Hebrew input", async () => {
    const result = await classifyMessage("Hello, how are you?");
    assert.equal(
      result,
      "Unknown",
      "Non-Hebrew message should be classified as Unknown"
    );
  });
}).catch(console.error);
