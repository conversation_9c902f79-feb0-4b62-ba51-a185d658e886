/* eslint-disable
  jsx-a11y/alt-text
*/

import { Page, Image, Text, View, Link } from "@react-pdf/renderer";
import type {
  childrenProp,
  infoArg,
} from "@/server/process/insurance-complete-process/pdf/interfaces";

import { SMAPY_URL_TEXT, SMAPY_URL } from "@/components/misc/urls";
import { imageBuffers } from "@/server/process/insurance-complete-process/pdf/assets/assets";
import { styles } from "@/server/process/insurance-complete-process/pdf/styles";
export const heBool = (answer: any) => (answer ? "כן" : "לא");

export const InfoItem = ({ label, info, wrapLabel, wrapInfo }: infoArg) => {
  const choose = (flag?: boolean) => (flag ? Wrapper : Text);
  const Label = choose(wrapLabel);
  const Info = choose(wrapInfo);
  return (
    <View style={styles.gridItem}>
      <Label style={styles.gridItemLabel}>{label}</Label>
      <Info style={styles.gridItemValue}>{String(info)}</Info>
    </View>
  );
};

export const Wrapper = ({
  children,
  style,
  space = 3.5,
}: {
  children: string;
  style?: Record<string, any>;
  space?: number;
}) => {
  return (
    <View
      style={{
        display: "flex",
        flexDirection: "row-reverse",
        flexWrap: "wrap",
        ...style,
      }}
    >
      {children.split(" ").map((item, index) => (
        <Text key={index} style={{ marginLeft: space }}>
          {item}
        </Text>
      ))}
    </View>
  );
};

export const NewLine = () => <Text>{"\n"}</Text>;

export const PageTitle = ({ children }: childrenProp) => (
  <View style={styles.pageTitle}>
    <Image
      style={{ height: styles.pageTitle.fontSize }}
      src={imageBuffers.house_logo}
    />
    <Text> </Text>
    <Text>{children}</Text>
  </View>
);

export const CustomerBasePage = ({ children }: childrenProp) => (
  <Page size="A4" style={styles.basePage}>
    <Header />
    <View style={styles.pageContent}>{children}</View>
    <Footer />
  </Page>
);

const Header = () => (
  <View
    style={{
      width: "100%",
    }}
  >
    <Image src={imageBuffers.header} />
    <Image
      style={{ position: "absolute", right: "5%", top: "10%", width: "30%" }}
      src={imageBuffers.smapy_logo}
    />
  </View>
);

const Footer = () => {
  const LegalNotes = ({ notes }: Record<"notes", string[]>) => {
    const items = notes.map((note, index) => (
      <View
        key={index}
        style={{
          display: "flex",
          flexDirection: "row-reverse",
        }}
      >
        <Text>*</Text>
        <Text>{note}</Text>
      </View>
    ));
    return <View style={styles.legalNotes}>{items}</View>;
  };
  return (
    <View>
      <LegalNotes
        notes={[
          "הכל בכפוף לפוליסה המקורית.",
          "המחירים המוצגים הינם לפני חיתום בחברת ביטוח.",
        ]}
      ></LegalNotes>

      <View style={{ position: "relative" }}>
        <Image src={imageBuffers.footer} style={{ width: "100%" }}></Image>
        <Image
          src={imageBuffers.footer_logo}
          style={{
            position: "absolute",
            left: "5%",
            bottom: "30%",
            width: "20%",
          }}
        ></Image>
        <View
          style={{
            position: "absolute",
            height: "100%",
            width: "100%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          {
            <Link
              style={{
                textDecoration: "none",
                color: "white",
              }}
              src={SMAPY_URL}
            >
              {SMAPY_URL_TEXT}
            </Link>
          }
        </View>
      </View>
    </View>
  );
};
