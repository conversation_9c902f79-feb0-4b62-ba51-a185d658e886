/* eslint-disable
    @next/next/no-img-element,
    jsx-a11y/alt-text
*/

import {
  CustomerBasePage,
  PageTitle,
} from "@/server/process/insurance-complete-process/pdf/pdf-pages/page-components";
import { Image, Text, View, StyleSheet } from "@react-pdf/renderer";
import { styles } from "@/server/process/insurance-complete-process/pdf/styles";
import { VSpace } from "@/server/process/insurance-complete-process/pdf/VSpace";
import { format } from "date-fns";

interface Props {
  other_insurance_cancel_company: string;
  customerName: string;
  customerId: string;
  other_insurance_cancel_number: string;
  signatureBuffer?: Buffer;
  email?: string;
}

export const AppendixCPage = ({
  signatureBuffer,
  other_insurance_cancel_company,
  customerName,
  customerId,
  other_insurance_cancel_number,
  email,
}: Props) => {
  const date = format(new Date(), "dd MMM yyyy");
  const customStyles = StyleSheet.create({
    textContainer: {
      display: "flex",
      flexDirection: "row-reverse",
      gap: "8px",
    },
  });

  return (
    <CustomerBasePage>
      <Text style={styles.appendixCTitle}>נספח ג'</Text>
      <VSpace size={12} />
      <Text style={styles.appendixCCancelButton}>הודעת ביטול</Text>
      <VSpace size={12} />
      <View style={customStyles.textContainer}>
        <Text>:עבור חברת הביטוח</Text>
        <Text style={styles.fontMedium}>{other_insurance_cancel_company}</Text>
      </View>
      <VSpace size={8} />
      <View style={customStyles.textContainer}>
        <Text>אני</Text>
        <Text style={styles.fontMedium}>{customerName}</Text>
        <Text>ת.ז.</Text>
        <Text style={styles.fontMedium}>{customerId}</Text>
      </View>
      <VSpace size={8} />
      <View style={customStyles.textContainer}>
        <Text>מבקשים לבטל את פוליסת הביטוח חיים שמספרה</Text>
        <Text style={styles.fontMedium}>{other_insurance_cancel_number}</Text>
      </View>
      <VSpace size={8} />
      <View style={customStyles.textContainer}>
        <Text>אודה על קבלת אישור לבקשה זו למייל</Text>
        <Text style={styles.fontMedium}>{email}</Text>
      </View>
      <VSpace size={8} />
      <View style={customStyles.textContainer}>
        <Text>תאריך</Text>
        <Text style={styles.fontMedium}>{date}</Text>
      </View>
      <VSpace size={8} />
      <View style={customStyles.textContainer}>
        <Text>חתימת המבוטח</Text>
        {signatureBuffer && (
          <Image
            style={{ height: styles.pageTitle.fontSize }}
            src={signatureBuffer}
          />
        )}
      </View>
    </CustomerBasePage>
  );
};
