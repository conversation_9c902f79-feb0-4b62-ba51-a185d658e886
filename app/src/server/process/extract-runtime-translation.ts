import {
  AdvisorLifeInsuranceLeadPages,
  AdvisorLifeInsurancePages,
} from "../../components/advisor/advisor-life-insurance/advisor-life-insurance-schema";
import { AdvisorUpdatePages } from "../../components/advisor/advisor-registration-flow/advisor-registration-flow-schema";
import { schemaLogin, schemaOTP } from "../../components/auth/user-auth-schema";
import type { ZodTypeAny } from "zod";
import type { TranslationFn } from "../../utils/i18n-utils";
import { clearCache, translateSchemaInfo } from "../../utils/i18n-utils";
import type { WizardPagesDefinition } from "../../components/common/wizard/useWizardFlow";
import { getPagesZodMetaInfo } from "../../components/common/wizard/useWizardFlow";
import { getZodMetaInfo } from "../../utils/zod-meta";
import { compareFlowPages } from "@/components/customer/compare/compare-flow-schema";
import {
  contactUsEmailPages,
  contactUsPhonePages,
} from "@/components/customer/contact-us/contact-us-schema";
import { CustomerLifeInsurancePages } from "@/components/customer/customer-life-insurance/customer-life-insurance-schema";
import { pause } from "@/utils/pause";
// import { HowDoesItWork } from "@/components/customer/compare/how-does-it-work";

export const extractRuntimeTranslation = async (nsMap: {
  [ns: string]: TranslationFn;
}) => {
  let count = 0;
  let context = "";

  // @ts-ignore
  const translate: TranslationFn = (
    key: string | string[],
    def: string | undefined
  ) => {
    console.log(`translate ${ns} ${key}=${def}`);
    const map = nsMap[ns];
    if (!map) {
      throw new Error(
        `Missing translator for namespace ${ns} key ${key} def:${def} context ${context}`
      );
    }

    count++;
    if (
      key ===
      "lifeInsurance.healthDeclaration.eylon._422_heart_issues._422_423.choices.true"
    ) {
      debugger;
    }

    const skey: string = Array.isArray(key) ? "common" : key;
    if (!skey) {
      throw new Error("Missing key");
    }
    // @ts-ignore
    return map(skey, def);
  };

  const translateSchema = async (schema: ZodTypeAny) => {
    context = `translateSchema ns:${ns} name:${schema.metadata().name}`;
    translateSchemaInfo(getZodMetaInfo(schema), translate, "");
    await pause(1000); // avoid batch limit
    context = "";
  };

  const translateWizard = async (pagesDefinition: WizardPagesDefinition) => {
    context = `translateWizard ns:${ns} name:${pagesDefinition.name}}`;
    const stepsRaw = getPagesZodMetaInfo(pagesDefinition);
    const steps = translateSchemaInfo(
      stepsRaw,
      translate,
      pagesDefinition.name
    );
    await pause(1000); // avoid batch limit
    context = "";
  };

  clearCache();
  // let ns: string;
  // ns = "landing-page";
  const ns = "common";
  for (const schema of [schemaLogin, schemaOTP]) {
    await translateSchema(schema);
  }

  for (const pagesDefinition of [
    CustomerLifeInsurancePages,
    // contactUsPhonePages,
    // contactUsEmailPages,
    // compareFlowPages,
    // AdvisorLifeInsurancePages,
    // AdvisorLifeInsuranceLeadPages,
    // AdvisorUpdatePages,
  ]) {
    await translateWizard(pagesDefinition);
  }

  return { message: `done count:${count}` };
};
