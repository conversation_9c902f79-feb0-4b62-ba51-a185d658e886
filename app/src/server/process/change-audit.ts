import type { PrismaClient } from "@prisma/client";

export const updateChangeAudit = async (
  prisma: PrismaClient,
  userId: string,
  formName: string,
  method: string,
  step: string | undefined,
  itemId: string | undefined,
  input: any,
  results: any
) => {
  await prisma.changeAudit.create({
    data: {
      userId,
      formName,
      method,
      itemId,
      changeName: step || "unknown",
      changeValue: JSON.parse(JSON.stringify(input)),
      results: JSON.parse(JSON.stringify(results)),
    },
  });
};
