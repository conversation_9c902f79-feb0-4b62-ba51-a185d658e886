import type { Root } from "@/server/process/trusty-types";

export const trustySampleData: Root = {
  data: {
    offers: [
      {
        id: 200,
        agent_id: 200,
        provider_id: 25,
        product_id: 6429,
        policy_id: 7,
        mortgage_insurance_request_id: 3966,
        about_us:
          "\u05de\u05ea\u05d7\u05d9\u05d9\u05d1\u05d9\u05dd \u05dc\u05d4\u05e6\u05e2\u05d4 \u05d4\u05e0\u05de\u05d5\u05db\u05d4 \u05d1\u05d9\u05d5\u05ea\u05e8! \u05de\u05e0\u05d4\u05dc \u05ea\u05d9\u05e7 \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d9\u05e9\u05d9 \u05d5\u05de\u05e0\u05d5\u05e1\u05d4!",
        agency_name:
          "\u05d9\u05e1\u05d5\u05d3 \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d5\u05d7\u05d9\u05e1\u05db\u05d5\u05df",
        email: "<EMAIL>",
        phone: null,
        site_phone: "073-3745443",
        listing_image:
          "https://trusty.co.il/uploads/agents/listings/agent-listing6329639e732f9.jpg",
        address_city: "\u05e4\u05ea\u05d7 \u05ea\u05e7\u05d5\u05d5\u05d4",
        company_img_name: null,
        avg_review: "10.00",
        total_reviews: 2,
        agent_areas: [1, 2, 3, 4, 5, 6],
        total_price: null,
        average_month_price: 17.8,
        company_id: 1,
        discount_first_year: "75.0",
        discount_for_all_years: [
          "75.0",
          "65.0",
          "65.0",
          "65.0",
          "65.0",
          "65.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
        ],
        discount_for_rest_of_the_years: "0.0",
        sort_by_period: "first_year_sum",
        benefits: [
          {
            id: 234,
            agent_id: 200,
            name: "\u05de\u05e0\u05d4\u05dc \u05ea\u05d9\u05e7 \u05d0\u05d9\u05e9\u05d9",
            details:
              "\u05e9\u05d9\u05d7\u05ea \u05d9\u05e2\u05d5\u05e5 \u05d8\u05dc\u05e4\u05d5\u05e0\u05d9\u05ea \u05d7\u05d9\u05e0\u05dd!",
            active: 1,
            created_at: "2022-09-20T06:30:53.000000Z",
            updated_at: "2022-09-20T12:00:05.000000Z",
            pivot: { agent_mortgage_bid_id: 6429, benefit_id: 234 },
          },
        ],
        the_user_request_parameters: {
          agent_id: 25,
          provider_id: 25,
          number_of_insured: "individual",
          age1: "35",
          gender1: "female",
          is_smoking1: false,
          date_of_birth1: "1988-07-07T16:22:22.000000Z",
          health_condition1: "excellent",
          tracks:
            '[{"desired_sum":"1000000","interest_type":"temporary","desired_period":"7","interest_rate":"0.001"},{"desired_sum":"1000000","interest_type":"permanent","desired_period":"8","interest_rate":"2"}]',
          total_loan: 2000000,
          user_id: null,
          ip: "**************",
          screen_width: "2034",
          screen_height: "532",
          sort_by_param: "price_asc",
          sort_by_period: "first_year_sum",
          area_id: "0",
          updated_at: "2023-07-03T16:22:22.000000Z",
          created_at: "2023-07-03T16:22:22.000000Z",
          id: 3966,
        },
        results_form_code: "s9879822",
        token: "mc215b5rxl",
        companies_offer: [
          "[84.6405,79.5799,70.5677,61.3765,51.6415,37.4115,24.8225,9.0151]",
        ],
        companies_offer_after_agent_discount: 21.160125,
        bid_price_total_years: 1922.4,
        companies_offer_for_all_years_arr: [
          {
            company_one_year_offer: 84.64,
            agent_discount_for_that_year: 75,
            agent_discount_for_that_year_in_money: 761.76,
            company_offer_after_agent_discount_for_month: 21.16,
            company_offer_after_agent_discount_for_year: 253.92,
            incremental_cost: 253.92,
            incremental_year: 1,
          },
          {
            company_one_year_offer: 79.58,
            agent_discount_for_that_year: 65,
            agent_discount_for_that_year_in_money: 620.72,
            company_offer_after_agent_discount_for_month: 27.85,
            company_offer_after_agent_discount_for_year: 334.24,
            incremental_cost: 588.16,
            incremental_year: 2,
          },
          {
            company_one_year_offer: 70.57,
            agent_discount_for_that_year: 65,
            agent_discount_for_that_year_in_money: 550.43,
            company_offer_after_agent_discount_for_month: 24.7,
            company_offer_after_agent_discount_for_year: 296.38,
            incremental_cost: 884.54,
            incremental_year: 3,
          },
          {
            company_one_year_offer: 61.38,
            agent_discount_for_that_year: 65,
            agent_discount_for_that_year_in_money: 478.74,
            company_offer_after_agent_discount_for_month: 21.48,
            company_offer_after_agent_discount_for_year: 257.78,
            incremental_cost: 1142.32,
            incremental_year: 4,
          },
          {
            company_one_year_offer: 51.64,
            agent_discount_for_that_year: 65,
            agent_discount_for_that_year_in_money: 402.8,
            company_offer_after_agent_discount_for_month: 18.07,
            company_offer_after_agent_discount_for_year: 216.89,
            incremental_cost: 1359.22,
            incremental_year: 5,
          },
          {
            company_one_year_offer: 37.41,
            agent_discount_for_that_year: 65,
            agent_discount_for_that_year_in_money: 291.81,
            company_offer_after_agent_discount_for_month: 13.09,
            company_offer_after_agent_discount_for_year: 157.13,
            incremental_cost: 1516.35,
            incremental_year: 6,
          },
          {
            company_one_year_offer: 24.82,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 24.82,
            company_offer_after_agent_discount_for_year: 297.87,
            incremental_cost: 1814.22,
            incremental_year: 7,
          },
          {
            company_one_year_offer: 9.02,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 9.02,
            company_offer_after_agent_discount_for_year: 108.18,
            incremental_cost: 1922.4,
            incremental_year: 8,
          },
        ],
        offer_price: 21.16,
        b_first_year: 21.16,
        featured_terms: [
          {
            id: 40,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 6,
            text: "<ul>\n<li>\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.</li>\n<li>\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.</li>\n<li>\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.</li></ul>",
            term: "\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.\n\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.\n\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.",
            number: null,
            featured: 1,
            short:
              "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05db\u05d9\u05e1\u05d5\u05d9 \u05d2\u05d5\u05d1\u05d4 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
            note: null,
            created_at: "2020-04-08 11:41:24",
            updated_at: "2023-03-15 17:44:06",
          },
          {
            id: 41,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 7,
            text: '<span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - </span><b style="font-size: 1rem;">78</b><br><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - </span><b style="font-size: 1rem;">74</b><br><span style="font-size: 1rem;">&nbsp;\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - </span><b style="font-size: 1rem;">81</b><br>',
            term: "<div>\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - <b>84</b></div><div>\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - 74&nbsp;</div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - 81</div>",
            number: null,
            featured: 1,
            short:
              "\u05de\u05d3\u05d3 \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 84",
            note: null,
            created_at: "2020-04-08 11:41:39",
            updated_at: "2023-06-13 11:35:56",
          },
        ],
        company_name: "\u05de\u05d2\u05d3\u05dc",
      },
      {
        id: 252,
        agent_id: 252,
        provider_id: 25,
        product_id: 6495,
        policy_id: 7,
        mortgage_insurance_request_id: 3966,
        about_us: "\u05e4\u05d9\u05e0\u05e0\u05e1\u05d9\u05e7\u05dc",
        agency_name: "\u05e4\u05d9\u05e0\u05e0\u05e1\u05d9\u05e7\u05dc",
        email: "<EMAIL>",
        phone: null,
        site_phone: "073-3745415",
        listing_image:
          "https://trusty.co.il/uploads/agents/listings/agent-listing646b944d58026.jpeg",
        address_city: "\u05d9\u05e8\u05d5\u05e9\u05dc\u05d9\u05dd",
        company_img_name: null,
        avg_review: "0.00",
        total_reviews: 0,
        agent_areas: [1, 2, 3, 4, 5, 6],
        total_price: null,
        average_month_price: 39.98,
        company_id: 1,
        discount_first_year: "70.0",
        discount_for_all_years: [
          "70.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
        ],
        discount_for_rest_of_the_years: "0.0",
        sort_by_period: "first_year_sum",
        benefits: [],
        the_user_request_parameters: {
          agent_id: 25,
          provider_id: 25,
          number_of_insured: "individual",
          age1: "35",
          gender1: "female",
          is_smoking1: false,
          date_of_birth1: "1988-07-07T16:22:22.000000Z",
          health_condition1: "excellent",
          tracks:
            '[{"desired_sum":"1000000","interest_type":"temporary","desired_period":"7","interest_rate":"0.001"},{"desired_sum":"1000000","interest_type":"permanent","desired_period":"8","interest_rate":"2"}]',
          total_loan: 2000000,
          user_id: null,
          ip: "**************",
          screen_width: "2034",
          screen_height: "532",
          sort_by_param: "price_asc",
          sort_by_period: "first_year_sum",
          area_id: "0",
          updated_at: "2023-07-03T16:22:22.000000Z",
          created_at: "2023-07-03T16:22:22.000000Z",
          id: 3966,
        },
        results_form_code: "s9879822",
        token: "clpwqngjh8",
        companies_offer: [
          "[84.6405,79.5799,70.5677,61.3765,51.6415,37.4115,24.8225,9.0151]",
        ],
        companies_offer_after_agent_discount: 25.39215,
        bid_price_total_years: 4317.68,
        companies_offer_for_all_years_arr: [
          {
            company_one_year_offer: 84.64,
            agent_discount_for_that_year: 70,
            agent_discount_for_that_year_in_money: 710.98,
            company_offer_after_agent_discount_for_month: 25.39,
            company_offer_after_agent_discount_for_year: 304.71,
            incremental_cost: 304.71,
            incremental_year: 1,
          },
          {
            company_one_year_offer: 79.58,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 79.58,
            company_offer_after_agent_discount_for_year: 954.96,
            incremental_cost: 1259.66,
            incremental_year: 2,
          },
          {
            company_one_year_offer: 70.57,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 70.57,
            company_offer_after_agent_discount_for_year: 846.81,
            incremental_cost: 2106.48,
            incremental_year: 3,
          },
          {
            company_one_year_offer: 61.38,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 61.38,
            company_offer_after_agent_discount_for_year: 736.52,
            incremental_cost: 2843,
            incremental_year: 4,
          },
          {
            company_one_year_offer: 51.64,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 51.64,
            company_offer_after_agent_discount_for_year: 619.7,
            incremental_cost: 3462.69,
            incremental_year: 5,
          },
          {
            company_one_year_offer: 37.41,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 37.41,
            company_offer_after_agent_discount_for_year: 448.94,
            incremental_cost: 3911.63,
            incremental_year: 6,
          },
          {
            company_one_year_offer: 24.82,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 24.82,
            company_offer_after_agent_discount_for_year: 297.87,
            incremental_cost: 4209.5,
            incremental_year: 7,
          },
          {
            company_one_year_offer: 9.02,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 9.02,
            company_offer_after_agent_discount_for_year: 108.18,
            incremental_cost: 4317.68,
            incremental_year: 8,
          },
        ],
        offer_price: 25.39,
        b_first_year: 25.39,
        featured_terms: [
          {
            id: 40,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 6,
            text: "<ul>\n<li>\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.</li>\n<li>\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.</li>\n<li>\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.</li></ul>",
            term: "\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.\n\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.\n\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.",
            number: null,
            featured: 1,
            short:
              "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05db\u05d9\u05e1\u05d5\u05d9 \u05d2\u05d5\u05d1\u05d4 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
            note: null,
            created_at: "2020-04-08 11:41:24",
            updated_at: "2023-03-15 17:44:06",
          },
          {
            id: 41,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 7,
            text: '<span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - </span><b style="font-size: 1rem;">78</b><br><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - </span><b style="font-size: 1rem;">74</b><br><span style="font-size: 1rem;">&nbsp;\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - </span><b style="font-size: 1rem;">81</b><br>',
            term: "<div>\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - <b>84</b></div><div>\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - 74&nbsp;</div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - 81</div>",
            number: null,
            featured: 1,
            short:
              "\u05de\u05d3\u05d3 \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 84",
            note: null,
            created_at: "2020-04-08 11:41:39",
            updated_at: "2023-06-13 11:35:56",
          },
        ],
        company_name: "\u05de\u05d2\u05d3\u05dc",
      },
      {
        id: 166,
        agent_id: 166,
        provider_id: 25,
        product_id: 6497,
        policy_id: 7,
        mortgage_insurance_request_id: 3966,
        about_us:
          "\u05d2\u05d5\u05dc\u05d5\u05d1 \u05e1\u05d5\u05db\u05e0\u05d5\u05ea \u05dc\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e0\u05d9\u05d4\u05d5\u05dc\u05d5 \u05e9\u05dc \u05de\u05d9\u05db\u05d0\u05dc \u05d2\u05d5\u05dc\u05d5\u05d1 \u05e1\u05d5\u05db\u05df \u05d1\u05d9\u05d8\u05d5\u05d7 \u05de\u05d5\u05e8\u05e9\u05d4 \u05de\u05d8\u05e2\u05dd \u05de\u05e9\u05e8\u05d3 \u05d4\u05d0\u05d5\u05e6\u05e8 \u05d5\u05e8\u05e9\u05d5\u05ea \u05e9\u05d5\u05e7 \u05d4\u05d4\u05d5\u05df. \u05d4\u05de\u05ea\u05de\u05d7\u05d4 \u05d1\u05e0\u05d9\u05d4\u05d5\u05dc \u05e1\u05d9\u05db\u05d5\u05e0\u05d9\u05dd \u05d5\u05d1\u05d0\u05e1\u05e4\u05e7\u05ea \u05de\u05e2\u05d8\u05e4\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7\u05d9\u05ea \u05de\u05d9\u05d8\u05d1\u05d9\u05ea \u05dc\u05e4\u05e8\u05d8\u05d9\u05d9\u05dd, \u05de\u05e9\u05e4\u05d7\u05d5\u05ea \u05d5\u05e2\u05e1\u05e7\u05d9\u05dd.",
        agency_name:
          "\u05d2\u05d5\u05dc\u05d5\u05d1 \u05e1\u05d5\u05db\u05e0\u05d5\u05ea \u05dc\u05d1\u05d9\u05d8\u05d5\u05d7",
        email: "<EMAIL>",
        phone: null,
        site_phone: "073-3745420",
        listing_image:
          "https://trusty.co.il/uploads/agents/listings/agent-listing6089398ad3a9e.png",
        address_city: "\u05d8\u05d1\u05e8\u05d9\u05d4",
        company_img_name: null,
        avg_review: "8.92",
        total_reviews: 5,
        agent_areas: [1, 2, 3, 4, 5, 6],
        total_price: null,
        average_month_price: 30.8,
        company_id: 1,
        discount_first_year: "70.0",
        discount_for_all_years: [
          "70.0",
          "35.0",
          "35.0",
          "20.0",
          "20.0",
          "20.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
        ],
        discount_for_rest_of_the_years: "60.0",
        sort_by_period: "first_year_sum",
        benefits: [
          {
            id: 157,
            agent_id: 166,
            name: "\u05dc\u05d1\u05e2\u05dc\u05d9 \u05de\u05e9\u05db\u05e0\u05ea\u05d0\u05d5\u05ea",
            details:
              "\u05d4\u05ea\u05d7\u05d9\u05d9\u05d1\u05d5\u05ea \u05dc\u05de\u05d7\u05d9\u05e8 \u05d4\u05d6\u05d5\u05dc \u05d1\u05d9\u05d5\u05ea\u05e8",
            active: 1,
            created_at: "2021-02-02T11:24:24.000000Z",
            updated_at: "2021-02-02T11:24:24.000000Z",
            pivot: { agent_mortgage_bid_id: 6497, benefit_id: 157 },
          },
        ],
        the_user_request_parameters: {
          agent_id: 25,
          provider_id: 25,
          number_of_insured: "individual",
          age1: "35",
          gender1: "female",
          is_smoking1: false,
          date_of_birth1: "1988-07-07T16:22:22.000000Z",
          health_condition1: "excellent",
          tracks:
            '[{"desired_sum":"1000000","interest_type":"temporary","desired_period":"7","interest_rate":"0.001"},{"desired_sum":"1000000","interest_type":"permanent","desired_period":"8","interest_rate":"2"}]',
          total_loan: 2000000,
          user_id: null,
          ip: "**************",
          screen_width: "2034",
          screen_height: "532",
          sort_by_param: "price_asc",
          sort_by_period: "first_year_sum",
          area_id: "0",
          updated_at: "2023-07-03T16:22:22.000000Z",
          created_at: "2023-07-03T16:22:22.000000Z",
          id: 3966,
        },
        results_form_code: "s9879822",
        token: "sq1q9lyqds",
        companies_offer: [
          "[84.6405,79.5799,70.5677,61.3765,51.6415,37.4115,24.8225,9.0151]",
        ],
        companies_offer_after_agent_discount: 25.39215,
        bid_price_total_years: 3326.03,
        companies_offer_for_all_years_arr: [
          {
            company_one_year_offer: 84.64,
            agent_discount_for_that_year: 70,
            agent_discount_for_that_year_in_money: 710.98,
            company_offer_after_agent_discount_for_month: 25.39,
            company_offer_after_agent_discount_for_year: 304.71,
            incremental_cost: 304.71,
            incremental_year: 1,
          },
          {
            company_one_year_offer: 79.58,
            agent_discount_for_that_year: 35,
            agent_discount_for_that_year_in_money: 334.24,
            company_offer_after_agent_discount_for_month: 51.73,
            company_offer_after_agent_discount_for_year: 620.72,
            incremental_cost: 925.43,
            incremental_year: 2,
          },
          {
            company_one_year_offer: 70.57,
            agent_discount_for_that_year: 35,
            agent_discount_for_that_year_in_money: 296.38,
            company_offer_after_agent_discount_for_month: 45.87,
            company_offer_after_agent_discount_for_year: 550.43,
            incremental_cost: 1475.86,
            incremental_year: 3,
          },
          {
            company_one_year_offer: 61.38,
            agent_discount_for_that_year: 20,
            agent_discount_for_that_year_in_money: 147.3,
            company_offer_after_agent_discount_for_month: 49.1,
            company_offer_after_agent_discount_for_year: 589.21,
            incremental_cost: 2065.07,
            incremental_year: 4,
          },
          {
            company_one_year_offer: 51.64,
            agent_discount_for_that_year: 20,
            agent_discount_for_that_year_in_money: 123.94,
            company_offer_after_agent_discount_for_month: 41.31,
            company_offer_after_agent_discount_for_year: 495.76,
            incremental_cost: 2560.83,
            incremental_year: 5,
          },
          {
            company_one_year_offer: 37.41,
            agent_discount_for_that_year: 20,
            agent_discount_for_that_year_in_money: 89.79,
            company_offer_after_agent_discount_for_month: 29.93,
            company_offer_after_agent_discount_for_year: 359.15,
            incremental_cost: 2919.98,
            incremental_year: 6,
          },
          {
            company_one_year_offer: 24.82,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 24.82,
            company_offer_after_agent_discount_for_year: 297.87,
            incremental_cost: 3217.85,
            incremental_year: 7,
          },
          {
            company_one_year_offer: 9.02,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 9.02,
            company_offer_after_agent_discount_for_year: 108.18,
            incremental_cost: 3326.03,
            incremental_year: 8,
          },
        ],
        offer_price: 25.39,
        b_first_year: 25.39,
        featured_terms: [
          {
            id: 40,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 6,
            text: "<ul>\n<li>\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.</li>\n<li>\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.</li>\n<li>\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.</li></ul>",
            term: "\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.\n\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.\n\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.",
            number: null,
            featured: 1,
            short:
              "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05db\u05d9\u05e1\u05d5\u05d9 \u05d2\u05d5\u05d1\u05d4 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
            note: null,
            created_at: "2020-04-08 11:41:24",
            updated_at: "2023-03-15 17:44:06",
          },
          {
            id: 41,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 7,
            text: '<span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - </span><b style="font-size: 1rem;">78</b><br><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - </span><b style="font-size: 1rem;">74</b><br><span style="font-size: 1rem;">&nbsp;\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - </span><b style="font-size: 1rem;">81</b><br>',
            term: "<div>\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - <b>84</b></div><div>\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - 74&nbsp;</div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - 81</div>",
            number: null,
            featured: 1,
            short:
              "\u05de\u05d3\u05d3 \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 84",
            note: null,
            created_at: "2020-04-08 11:41:39",
            updated_at: "2023-06-13 11:35:56",
          },
        ],
        company_name: "\u05de\u05d2\u05d3\u05dc",
      },
      {
        id: 212,
        agent_id: 212,
        provider_id: 25,
        product_id: 6474,
        policy_id: 7,
        mortgage_insurance_request_id: 3966,
        about_us:
          "Verify \u05de\u05e6\u05d9\u05d1\u05d4 \u05d0\u05d5\u05ea\u05da \u05d1\u05de\u05e8\u05db\u05d6 \u05d5\u05de\u05e2\u05de\u05d9\u05d3\u05d4 \u05dc\u05e8\u05e9\u05d5\u05ea\u05da \u05de\u05e2\u05d8\u05e4\u05ea \u05e4\u05ea\u05e8\u05d5\u05e0\u05d5\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7\u05d9\u05dd, \u05e4\u05e0\u05e1\u05d9\u05d5\u05e0\u05d9\u05d9\u05dd \u05d5\u05e4\u05d9\u05e0\u05e0\u05e1\u05d9\u05d9\u05dd \u05de\u05ea\u05e7\u05d3\u05de\u05d9\u05dd \u05d5\u05d0\u05d9\u05db\u05d5\u05ea\u05d9\u05d9\u05dd \u05d1\u05d4\u05ea\u05d0\u05de\u05d4 \u05d0\u05d9\u05e9\u05d9\u05ea",
        agency_name:
          "\u05d5\u05e8\u05d9\u05e4\u05d9\u05d9 \u05e1\u05d5\u05db\u05e0\u05d5\u05ea \u05dc\u05d1\u05d9\u05d8\u05d5\u05d7",
        email: "<EMAIL>",
        phone: null,
        site_phone: "073-3745452",
        listing_image:
          "https://trusty.co.il/uploads/agents/listings/agent-listing63cff7858e7c4.png",
        address_city:
          "\u05e8\u05d0\u05e9\u05d5\u05df \u05dc\u05e6\u05d9\u05d5\u05df",
        company_img_name: null,
        avg_review: "0.00",
        total_reviews: 0,
        agent_areas: [2, 3],
        total_price: null,
        average_month_price: 40.92,
        company_id: 1,
        discount_first_year: "60.0",
        discount_for_all_years: [
          "60.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
        ],
        discount_for_rest_of_the_years: "0.0",
        sort_by_period: "first_year_sum",
        benefits: [],
        the_user_request_parameters: {
          agent_id: 25,
          provider_id: 25,
          number_of_insured: "individual",
          age1: "35",
          gender1: "female",
          is_smoking1: false,
          date_of_birth1: "1988-07-07T16:22:22.000000Z",
          health_condition1: "excellent",
          tracks:
            '[{"desired_sum":"1000000","interest_type":"temporary","desired_period":"7","interest_rate":"0.001"},{"desired_sum":"1000000","interest_type":"permanent","desired_period":"8","interest_rate":"2"}]',
          total_loan: 2000000,
          user_id: null,
          ip: "**************",
          screen_width: "2034",
          screen_height: "532",
          sort_by_param: "price_asc",
          sort_by_period: "first_year_sum",
          area_id: "0",
          updated_at: "2023-07-03T16:22:22.000000Z",
          created_at: "2023-07-03T16:22:22.000000Z",
          id: 3966,
        },
        results_form_code: "s9879822",
        token: "atqkxade8d",
        companies_offer: [
          "[84.6405,79.5799,70.5677,61.3765,51.6415,37.4115,24.8225,9.0151]",
        ],
        companies_offer_after_agent_discount: 33.8562,
        bid_price_total_years: 4419.25,
        companies_offer_for_all_years_arr: [
          {
            company_one_year_offer: 84.64,
            agent_discount_for_that_year: 60,
            agent_discount_for_that_year_in_money: 609.41,
            company_offer_after_agent_discount_for_month: 33.86,
            company_offer_after_agent_discount_for_year: 406.27,
            incremental_cost: 406.27,
            incremental_year: 1,
          },
          {
            company_one_year_offer: 79.58,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 79.58,
            company_offer_after_agent_discount_for_year: 954.96,
            incremental_cost: 1361.23,
            incremental_year: 2,
          },
          {
            company_one_year_offer: 70.57,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 70.57,
            company_offer_after_agent_discount_for_year: 846.81,
            incremental_cost: 2208.05,
            incremental_year: 3,
          },
          {
            company_one_year_offer: 61.38,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 61.38,
            company_offer_after_agent_discount_for_year: 736.52,
            incremental_cost: 2944.56,
            incremental_year: 4,
          },
          {
            company_one_year_offer: 51.64,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 51.64,
            company_offer_after_agent_discount_for_year: 619.7,
            incremental_cost: 3564.26,
            incremental_year: 5,
          },
          {
            company_one_year_offer: 37.41,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 37.41,
            company_offer_after_agent_discount_for_year: 448.94,
            incremental_cost: 4013.2,
            incremental_year: 6,
          },
          {
            company_one_year_offer: 24.82,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 24.82,
            company_offer_after_agent_discount_for_year: 297.87,
            incremental_cost: 4311.07,
            incremental_year: 7,
          },
          {
            company_one_year_offer: 9.02,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 9.02,
            company_offer_after_agent_discount_for_year: 108.18,
            incremental_cost: 4419.25,
            incremental_year: 8,
          },
        ],
        offer_price: 33.86,
        b_first_year: 33.86,
        featured_terms: [
          {
            id: 40,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 6,
            text: "<ul>\n<li>\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.</li>\n<li>\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.</li>\n<li>\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.</li></ul>",
            term: "\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.\n\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.\n\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.",
            number: null,
            featured: 1,
            short:
              "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05db\u05d9\u05e1\u05d5\u05d9 \u05d2\u05d5\u05d1\u05d4 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
            note: null,
            created_at: "2020-04-08 11:41:24",
            updated_at: "2023-03-15 17:44:06",
          },
          {
            id: 41,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 7,
            text: '<span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - </span><b style="font-size: 1rem;">78</b><br><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - </span><b style="font-size: 1rem;">74</b><br><span style="font-size: 1rem;">&nbsp;\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - </span><b style="font-size: 1rem;">81</b><br>',
            term: "<div>\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - <b>84</b></div><div>\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - 74&nbsp;</div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - 81</div>",
            number: null,
            featured: 1,
            short:
              "\u05de\u05d3\u05d3 \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 84",
            note: null,
            created_at: "2020-04-08 11:41:39",
            updated_at: "2023-06-13 11:35:56",
          },
        ],
        company_name: "\u05de\u05d2\u05d3\u05dc",
      },
      {
        id: 215,
        agent_id: 215,
        provider_id: 25,
        product_id: 6484,
        policy_id: 7,
        mortgage_insurance_request_id: 3966,
        about_us:
          "\u05e2\u05d9\u05d2\u05d5\u05dc\u05d9\u05dd \u05de\u05e6\u05d9\u05e2\u05d4 \u05e4\u05ea\u05e8\u05d5\u05e0\u05d5\u05ea \u05e4\u05e0\u05e1\u05d9\u05d5\u05e0\u05d9\u05d9\u05dd \u05d5\u05e4\u05d9\u05e0\u05e0\u05e1\u05d9\u05dd, \u05db\u05de\u05d5 \u05d2\u05dd \u05e9\u05d9\u05e8\u05d5\u05ea\u05d9 \u05d1\u05d9\u05d8\u05d5\u05d7 \u05de\u05ea\u05e7\u05d3\u05de\u05d9\u05dd \u05d1\u05ea\u05d7\u05d5\u05de\u05d9 \u05d4\u05d7\u05d9\u05d9\u05dd, \u05d4\u05d1\u05e8\u05d9\u05d0\u05d5\u05ea \u05d5\u05d4\u05d0\u05dc\u05de\u05e0\u05d8\u05e8\u05d9.",
        agency_name:
          "\u05e2\u05d9\u05d2\u05d5\u05dc\u05d9\u05dd \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d5\u05e4\u05d9\u05e0\u05e0\u05e1\u05d9\u05dd",
        email: "<EMAIL>",
        phone: null,
        site_phone: "073-3745450",
        listing_image:
          "https://trusty.co.il/uploads/agents/listings/agent-listing63d2cc41758b6.png",
        address_city: "\u05e4\u05ea\u05d7 \u05ea\u05e7\u05d5\u05d5\u05d4",
        company_img_name: null,
        avg_review: "8.80",
        total_reviews: 1,
        agent_areas: [3],
        total_price: null,
        average_month_price: 20.88,
        company_id: 1,
        discount_first_year: "60.0",
        discount_for_all_years: [
          "60.0",
          "60.0",
          "60.0",
          "60.0",
          "60.0",
          "60.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
        ],
        discount_for_rest_of_the_years: "0.0",
        sort_by_period: "first_year_sum",
        benefits: [
          {
            id: 256,
            agent_id: 215,
            name: "\u05e4\u05d2\u05d9\u05e9\u05d4 \u05e4\u05e0\u05e1\u05d9\u05d5\u05e0\u05d9\u05ea \u05d7\u05d9\u05e0\u05dd",
            details:
              "\u05e4\u05d2\u05d9\u05e9\u05d4 \u05e2\u05dc \u05e4\u05e0\u05e1\u05d9\u05d4 \u05e7\u05e8\u05df \u05d4\u05e9\u05ea\u05dc\u05de\u05d5\u05ea",
            active: 1,
            created_at: "2023-01-24T16:50:43.000000Z",
            updated_at: "2023-02-05T16:52:36.000000Z",
            pivot: { agent_mortgage_bid_id: 6484, benefit_id: 256 },
          },
        ],
        the_user_request_parameters: {
          agent_id: 25,
          provider_id: 25,
          number_of_insured: "individual",
          age1: "35",
          gender1: "female",
          is_smoking1: false,
          date_of_birth1: "1988-07-07T16:22:22.000000Z",
          health_condition1: "excellent",
          tracks:
            '[{"desired_sum":"1000000","interest_type":"temporary","desired_period":"7","interest_rate":"0.001"},{"desired_sum":"1000000","interest_type":"permanent","desired_period":"8","interest_rate":"2"}]',
          total_loan: 2000000,
          user_id: null,
          ip: "**************",
          screen_width: "2034",
          screen_height: "532",
          sort_by_param: "price_asc",
          sort_by_period: "first_year_sum",
          area_id: "0",
          updated_at: "2023-07-03T16:22:22.000000Z",
          created_at: "2023-07-03T16:22:22.000000Z",
          id: 3966,
        },
        results_form_code: "s9879822",
        token: "6og7rrlaso",
        companies_offer: [
          "[84.6405,79.5799,70.5677,61.3765,51.6415,37.4115,24.8225,9.0151]",
        ],
        companies_offer_after_agent_discount: 33.8562,
        bid_price_total_years: 2255.1,
        companies_offer_for_all_years_arr: [
          {
            company_one_year_offer: 84.64,
            agent_discount_for_that_year: 60,
            agent_discount_for_that_year_in_money: 609.41,
            company_offer_after_agent_discount_for_month: 33.86,
            company_offer_after_agent_discount_for_year: 406.27,
            incremental_cost: 406.27,
            incremental_year: 1,
          },
          {
            company_one_year_offer: 79.58,
            agent_discount_for_that_year: 60,
            agent_discount_for_that_year_in_money: 572.98,
            company_offer_after_agent_discount_for_month: 31.83,
            company_offer_after_agent_discount_for_year: 381.98,
            incremental_cost: 788.26,
            incremental_year: 2,
          },
          {
            company_one_year_offer: 70.57,
            agent_discount_for_that_year: 60,
            agent_discount_for_that_year_in_money: 508.09,
            company_offer_after_agent_discount_for_month: 28.23,
            company_offer_after_agent_discount_for_year: 338.72,
            incremental_cost: 1126.98,
            incremental_year: 3,
          },
          {
            company_one_year_offer: 61.38,
            agent_discount_for_that_year: 60,
            agent_discount_for_that_year_in_money: 441.91,
            company_offer_after_agent_discount_for_month: 24.55,
            company_offer_after_agent_discount_for_year: 294.61,
            incremental_cost: 1421.59,
            incremental_year: 4,
          },
          {
            company_one_year_offer: 51.64,
            agent_discount_for_that_year: 60,
            agent_discount_for_that_year_in_money: 371.82,
            company_offer_after_agent_discount_for_month: 20.66,
            company_offer_after_agent_discount_for_year: 247.88,
            incremental_cost: 1669.47,
            incremental_year: 5,
          },
          {
            company_one_year_offer: 37.41,
            agent_discount_for_that_year: 60,
            agent_discount_for_that_year_in_money: 269.36,
            company_offer_after_agent_discount_for_month: 14.96,
            company_offer_after_agent_discount_for_year: 179.58,
            incremental_cost: 1849.04,
            incremental_year: 6,
          },
          {
            company_one_year_offer: 24.82,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 24.82,
            company_offer_after_agent_discount_for_year: 297.87,
            incremental_cost: 2146.91,
            incremental_year: 7,
          },
          {
            company_one_year_offer: 9.02,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 9.02,
            company_offer_after_agent_discount_for_year: 108.18,
            incremental_cost: 2255.1,
            incremental_year: 8,
          },
        ],
        offer_price: 33.86,
        b_first_year: 33.86,
        featured_terms: [
          {
            id: 40,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 6,
            text: "<ul>\n<li>\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.</li>\n<li>\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.</li>\n<li>\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.</li></ul>",
            term: "\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.\n\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.\n\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.",
            number: null,
            featured: 1,
            short:
              "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05db\u05d9\u05e1\u05d5\u05d9 \u05d2\u05d5\u05d1\u05d4 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
            note: null,
            created_at: "2020-04-08 11:41:24",
            updated_at: "2023-03-15 17:44:06",
          },
          {
            id: 41,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 7,
            text: '<span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - </span><b style="font-size: 1rem;">78</b><br><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - </span><b style="font-size: 1rem;">74</b><br><span style="font-size: 1rem;">&nbsp;\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - </span><b style="font-size: 1rem;">81</b><br>',
            term: "<div>\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - <b>84</b></div><div>\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - 74&nbsp;</div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - 81</div>",
            number: null,
            featured: 1,
            short:
              "\u05de\u05d3\u05d3 \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 84",
            note: null,
            created_at: "2020-04-08 11:41:39",
            updated_at: "2023-06-13 11:35:56",
          },
        ],
        company_name: "\u05de\u05d2\u05d3\u05dc",
      },
      {
        id: 206,
        agent_id: 206,
        provider_id: 25,
        product_id: 6433,
        policy_id: 7,
        mortgage_insurance_request_id: 3966,
        about_us: "",
        agency_name:
          "\u05d0\u05d9\u05d6\u05d9 \u05e4\u05d9\u05e0\u05e0\u05e1\u05d9\u05dd",
        email: "<EMAIL>",
        phone: null,
        site_phone: "073-3745446",
        listing_image:
          "https://trusty.co.il/uploads/agents/listings/agent-listing63dfe57f57f1a.jpg",
        address_city: "\u05d7\u05d9\u05e4\u05d4",
        company_img_name: null,
        avg_review: "0.00",
        total_reviews: 0,
        agent_areas: [1, 2, 3],
        total_price: null,
        average_month_price: 29.75,
        company_id: 1,
        discount_first_year: "60.0",
        discount_for_all_years: [
          "60.0",
          "50.0",
          "40.0",
          "30.0",
          "20.0",
          "10.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
        ],
        discount_for_rest_of_the_years: "0.0",
        sort_by_period: "first_year_sum",
        benefits: [
          {
            id: 241,
            agent_id: 206,
            name: "\u05e9\u05d9\u05d7\u05ea \u05d9\u05e2\u05d5\u05e5 \u05d7\u05d9\u05e0\u05dd",
            details:
              "\u05e9\u05d9\u05d7\u05ea \u05d9\u05e2\u05d5\u05e5 \u05d8\u05dc\u05e4\u05d5\u05e0\u05d9\u05ea \u05d7\u05d9\u05e0\u05dd!",
            active: 1,
            created_at: "2022-11-16T14:37:37.000000Z",
            updated_at: "2022-11-16T14:37:37.000000Z",
            pivot: { agent_mortgage_bid_id: 6433, benefit_id: 241 },
          },
        ],
        the_user_request_parameters: {
          agent_id: 25,
          provider_id: 25,
          number_of_insured: "individual",
          age1: "35",
          gender1: "female",
          is_smoking1: false,
          date_of_birth1: "1988-07-07T16:22:22.000000Z",
          health_condition1: "excellent",
          tracks:
            '[{"desired_sum":"1000000","interest_type":"temporary","desired_period":"7","interest_rate":"0.001"},{"desired_sum":"1000000","interest_type":"permanent","desired_period":"8","interest_rate":"2"}]',
          total_loan: 2000000,
          user_id: null,
          ip: "**************",
          screen_width: "2034",
          screen_height: "532",
          sort_by_param: "price_asc",
          sort_by_period: "first_year_sum",
          area_id: "0",
          updated_at: "2023-07-03T16:22:22.000000Z",
          created_at: "2023-07-03T16:22:22.000000Z",
          id: 3966,
        },
        results_form_code: "s9879822",
        token: "tyniqleqbw",
        companies_offer: [
          "[84.6405,79.5799,70.5677,61.3765,51.6415,37.4115,24.8225,9.0151]",
        ],
        companies_offer_after_agent_discount: 33.8562,
        bid_price_total_years: 3213.26,
        companies_offer_for_all_years_arr: [
          {
            company_one_year_offer: 84.64,
            agent_discount_for_that_year: 60,
            agent_discount_for_that_year_in_money: 609.41,
            company_offer_after_agent_discount_for_month: 33.86,
            company_offer_after_agent_discount_for_year: 406.27,
            incremental_cost: 406.27,
            incremental_year: 1,
          },
          {
            company_one_year_offer: 79.58,
            agent_discount_for_that_year: 50,
            agent_discount_for_that_year_in_money: 477.48,
            company_offer_after_agent_discount_for_month: 39.79,
            company_offer_after_agent_discount_for_year: 477.48,
            incremental_cost: 883.75,
            incremental_year: 2,
          },
          {
            company_one_year_offer: 70.57,
            agent_discount_for_that_year: 40,
            agent_discount_for_that_year_in_money: 338.72,
            company_offer_after_agent_discount_for_month: 42.34,
            company_offer_after_agent_discount_for_year: 508.09,
            incremental_cost: 1391.84,
            incremental_year: 3,
          },
          {
            company_one_year_offer: 61.38,
            agent_discount_for_that_year: 30,
            agent_discount_for_that_year_in_money: 220.96,
            company_offer_after_agent_discount_for_month: 42.96,
            company_offer_after_agent_discount_for_year: 515.56,
            incremental_cost: 1907.4,
            incremental_year: 4,
          },
          {
            company_one_year_offer: 51.64,
            agent_discount_for_that_year: 20,
            agent_discount_for_that_year_in_money: 123.94,
            company_offer_after_agent_discount_for_month: 41.31,
            company_offer_after_agent_discount_for_year: 495.76,
            incremental_cost: 2403.16,
            incremental_year: 5,
          },
          {
            company_one_year_offer: 37.41,
            agent_discount_for_that_year: 10,
            agent_discount_for_that_year_in_money: 44.89,
            company_offer_after_agent_discount_for_month: 33.67,
            company_offer_after_agent_discount_for_year: 404.04,
            incremental_cost: 2807.21,
            incremental_year: 6,
          },
          {
            company_one_year_offer: 24.82,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 24.82,
            company_offer_after_agent_discount_for_year: 297.87,
            incremental_cost: 3105.08,
            incremental_year: 7,
          },
          {
            company_one_year_offer: 9.02,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 9.02,
            company_offer_after_agent_discount_for_year: 108.18,
            incremental_cost: 3213.26,
            incremental_year: 8,
          },
        ],
        offer_price: 33.86,
        b_first_year: 33.86,
        featured_terms: [
          {
            id: 40,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 6,
            text: "<ul>\n<li>\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.</li>\n<li>\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.</li>\n<li>\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.</li></ul>",
            term: "\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.\n\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.\n\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.",
            number: null,
            featured: 1,
            short:
              "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05db\u05d9\u05e1\u05d5\u05d9 \u05d2\u05d5\u05d1\u05d4 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
            note: null,
            created_at: "2020-04-08 11:41:24",
            updated_at: "2023-03-15 17:44:06",
          },
          {
            id: 41,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 7,
            text: '<span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - </span><b style="font-size: 1rem;">78</b><br><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - </span><b style="font-size: 1rem;">74</b><br><span style="font-size: 1rem;">&nbsp;\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - </span><b style="font-size: 1rem;">81</b><br>',
            term: "<div>\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - <b>84</b></div><div>\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - 74&nbsp;</div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - 81</div>",
            number: null,
            featured: 1,
            short:
              "\u05de\u05d3\u05d3 \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 84",
            note: null,
            created_at: "2020-04-08 11:41:39",
            updated_at: "2023-06-13 11:35:56",
          },
        ],
        company_name: "\u05de\u05d2\u05d3\u05dc",
      },
      {
        id: 207,
        agent_id: 207,
        provider_id: 25,
        product_id: 6444,
        policy_id: 7,
        mortgage_insurance_request_id: 3966,
        about_us:
          "\u05d0\u05e0\u05d7\u05e0\u05d5 \u05de\u05d0\u05de\u05d9\u05e0\u05d9\u05dd \u05d1\u05de\u05e2\u05d8\u05e4\u05ea 360 \u05de\u05e2\u05dc\u05d5\u05ea \u05e2\u05d1\u05d5\u05e8 \u05db\u05dc \u05dc\u05e7\u05d5\u05d7 , \u05d0\u05ea\u05dd \u05ea\u05d1\u05d7\u05e8\u05d5 \u05dc\u05ea\u05ea \u05d1\u05e0\u05d5 \u05d0\u05ea \u05d4\u05d0\u05de\u05d5\u05df \u05d5\u05d0\u05e0\u05d7\u05e0\u05d5 \u05e0\u05d3\u05d0\u05d2 \u05dc\u05e2\u05e9\u05d5\u05ea \u05d0\u05ea \u05de\u05d4 \u05e9\u05d0\u05e0\u05d7\u05e0\u05d5 \u05d9\u05d5\u05d3\u05e2\u05d9\u05dd \u05d4\u05db\u05d9 \u05d8\u05d5\u05d1.",
        agency_name:
          "\u05e4\u05d5\u05d6\u05d9\u05dc\u05d5\u05d1 \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d5\u05e4\u05d9\u05e0\u05e0\u05e1\u05d9\u05dd",
        email: "<EMAIL>",
        phone: null,
        site_phone: "073-3745456",
        listing_image:
          "https://trusty.co.il/uploads/agents/listings/agent-listing64060a8bb5279.png",
        address_city: "\u05e2\u05e8\u05d3",
        company_img_name: null,
        avg_review: "0.00",
        total_reviews: 0,
        agent_areas: [1, 2, 3, 4, 5, 6],
        total_price: null,
        average_month_price: 33.72,
        company_id: 1,
        discount_first_year: "60.0",
        discount_for_all_years: [
          "60.0",
          "35.0",
          "35.0",
          "20.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
        ],
        discount_for_rest_of_the_years: "60.0",
        sort_by_period: "first_year_sum",
        benefits: [
          {
            id: 245,
            agent_id: 207,
            name: "\u05e9\u05d9\u05d7\u05ea \u05d9\u05e2\u05d5\u05e5 \u05d7\u05d9\u05e0\u05dd",
            details:
              "\u05e9\u05d9\u05d7\u05ea \u05d9\u05e2\u05d5\u05e5 \u05d8\u05dc\u05e4\u05d5\u05e0\u05d9\u05ea \u05d7\u05d9\u05e0\u05dd!",
            active: 1,
            created_at: "2022-12-13T13:00:30.000000Z",
            updated_at: "2022-12-13T13:00:30.000000Z",
            pivot: { agent_mortgage_bid_id: 6444, benefit_id: 245 },
          },
        ],
        the_user_request_parameters: {
          agent_id: 25,
          provider_id: 25,
          number_of_insured: "individual",
          age1: "35",
          gender1: "female",
          is_smoking1: false,
          date_of_birth1: "1988-07-07T16:22:22.000000Z",
          health_condition1: "excellent",
          tracks:
            '[{"desired_sum":"1000000","interest_type":"temporary","desired_period":"7","interest_rate":"0.001"},{"desired_sum":"1000000","interest_type":"permanent","desired_period":"8","interest_rate":"2"}]',
          total_loan: 2000000,
          user_id: null,
          ip: "**************",
          screen_width: "2034",
          screen_height: "532",
          sort_by_param: "price_asc",
          sort_by_period: "first_year_sum",
          area_id: "0",
          updated_at: "2023-07-03T16:22:22.000000Z",
          created_at: "2023-07-03T16:22:22.000000Z",
          id: 3966,
        },
        results_form_code: "s9879822",
        token: "i7qaholwod",
        companies_offer: [
          "[84.6405,79.5799,70.5677,61.3765,51.6415,37.4115,24.8225,9.0151]",
        ],
        companies_offer_after_agent_discount: 33.8562,
        bid_price_total_years: 3641.33,
        companies_offer_for_all_years_arr: [
          {
            company_one_year_offer: 84.64,
            agent_discount_for_that_year: 60,
            agent_discount_for_that_year_in_money: 609.41,
            company_offer_after_agent_discount_for_month: 33.86,
            company_offer_after_agent_discount_for_year: 406.27,
            incremental_cost: 406.27,
            incremental_year: 1,
          },
          {
            company_one_year_offer: 79.58,
            agent_discount_for_that_year: 35,
            agent_discount_for_that_year_in_money: 334.24,
            company_offer_after_agent_discount_for_month: 51.73,
            company_offer_after_agent_discount_for_year: 620.72,
            incremental_cost: 1027,
            incremental_year: 2,
          },
          {
            company_one_year_offer: 70.57,
            agent_discount_for_that_year: 35,
            agent_discount_for_that_year_in_money: 296.38,
            company_offer_after_agent_discount_for_month: 45.87,
            company_offer_after_agent_discount_for_year: 550.43,
            incremental_cost: 1577.43,
            incremental_year: 3,
          },
          {
            company_one_year_offer: 61.38,
            agent_discount_for_that_year: 20,
            agent_discount_for_that_year_in_money: 147.3,
            company_offer_after_agent_discount_for_month: 49.1,
            company_offer_after_agent_discount_for_year: 589.21,
            incremental_cost: 2166.64,
            incremental_year: 4,
          },
          {
            company_one_year_offer: 51.64,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 51.64,
            company_offer_after_agent_discount_for_year: 619.7,
            incremental_cost: 2786.34,
            incremental_year: 5,
          },
          {
            company_one_year_offer: 37.41,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 37.41,
            company_offer_after_agent_discount_for_year: 448.94,
            incremental_cost: 3235.28,
            incremental_year: 6,
          },
          {
            company_one_year_offer: 24.82,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 24.82,
            company_offer_after_agent_discount_for_year: 297.87,
            incremental_cost: 3533.15,
            incremental_year: 7,
          },
          {
            company_one_year_offer: 9.02,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 9.02,
            company_offer_after_agent_discount_for_year: 108.18,
            incremental_cost: 3641.33,
            incremental_year: 8,
          },
        ],
        offer_price: 33.86,
        b_first_year: 33.86,
        featured_terms: [
          {
            id: 40,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 6,
            text: "<ul>\n<li>\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.</li>\n<li>\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.</li>\n<li>\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.</li></ul>",
            term: "\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.\n\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.\n\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.",
            number: null,
            featured: 1,
            short:
              "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05db\u05d9\u05e1\u05d5\u05d9 \u05d2\u05d5\u05d1\u05d4 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
            note: null,
            created_at: "2020-04-08 11:41:24",
            updated_at: "2023-03-15 17:44:06",
          },
          {
            id: 41,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 7,
            text: '<span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - </span><b style="font-size: 1rem;">78</b><br><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - </span><b style="font-size: 1rem;">74</b><br><span style="font-size: 1rem;">&nbsp;\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - </span><b style="font-size: 1rem;">81</b><br>',
            term: "<div>\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - <b>84</b></div><div>\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - 74&nbsp;</div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - 81</div>",
            number: null,
            featured: 1,
            short:
              "\u05de\u05d3\u05d3 \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 84",
            note: null,
            created_at: "2020-04-08 11:41:39",
            updated_at: "2023-06-13 11:35:56",
          },
        ],
        company_name: "\u05de\u05d2\u05d3\u05dc",
      },
      {
        id: 175,
        agent_id: 175,
        provider_id: 25,
        product_id: 6374,
        policy_id: 7,
        mortgage_insurance_request_id: 3966,
        about_us:
          "\u05e1\u05d5\u05db\u05df \u05d1\u05d9\u05d8\u05d5\u05d7 \u05de\u05d5\u05e8\u05e9\u05d4! \u05d1\u05d5\u05d2\u05e8 \u05ea\u05d5\u05d0\u05e8 \u05e8\u05d0\u05e9\u05d5\u05df \u05d1\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d5\u05ea\u05d5\u05d0\u05e8 \u05e9\u05e0\u05d9 \u05d1\u05de\u05e0\u05d4\u05dc \u05e2\u05e1\u05e7\u05d9\u05dd! \u05e2\u05d5\u05d1\u05d3 \u05e2\u05dd \u05de\u05e8\u05d1\u05d9\u05ea \u05d7\u05d1\u05e8\u05d5\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d5\u05d1\u05ea\u05d9 \u05d4\u05d4\u05e9\u05e7\u05e2\u05d5\u05ea. \u05de\u05e6\u05d9\u05e2 \u05d4\u05ea\u05d0\u05de\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7\u05d9\u05dd \u05dc\u05e4\u05d9 \u05e6\u05d5\u05e8\u05da \u05e1\u05e4\u05e6\u05d9\u05e4\u05d9 \u05dc\u05db\u05dc \u05dc\u05e7\u05d5\u05d7 \u05d5\u05db\u05de\u05d5\u05d1\u05df \u05dc\u05d9\u05d5\u05d5\u05d9 \u05d0\u05d9\u05e9\u05d9 \u05d5\u05de\u05ea\u05de\u05e9\u05da \u05dc\u05d0\u05d5\u05e8\u05da \u05d4\u05e9\u05e0\u05d9\u05dd .",
        agency_name:
          "\u05de\u05ea\u05df \u05e1\u05d5\u05dc\u05d5\u05de\u05d5\u05df",
        email: "<EMAIL>",
        phone: null,
        site_phone: "073-3745412",
        listing_image:
          "https://trusty.co.il/uploads/agents/listings/agent-listing6124d0a7224f1.png",
        address_city:
          "\u05e7\u05e8\u05d9\u05ea \u05de\u05d5\u05e6\u05e7\u05d9\u05df",
        company_img_name: null,
        avg_review: "0.00",
        total_reviews: 0,
        agent_areas: [1, 2],
        total_price: null,
        average_month_price: 32.52,
        company_id: 1,
        discount_first_year: "60.0",
        discount_for_all_years: [
          "60.0",
          "40.0",
          "30.0",
          "15.0",
          "15.0",
          "15.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
        ],
        discount_for_rest_of_the_years: "55.0",
        sort_by_period: "first_year_sum",
        benefits: [
          {
            id: 178,
            agent_id: 175,
            name: "\u05e0\u05d9\u05ea\u05d5\u05d7 \u05ea\u05d9\u05e7 \u05e4\u05e0\u05e1\u05d9\u05d5\u05e0\u05d9",
            details:
              "\u05e0\u05d9\u05ea\u05d5\u05d7 \u05ea\u05d9\u05e7 \u05e4\u05e0\u05e1\u05d9\u05d5\u05e0\u05d9",
            active: 1,
            created_at: "2021-08-23T09:30:57.000000Z",
            updated_at: "2021-08-23T13:15:57.000000Z",
            pivot: { agent_mortgage_bid_id: 6374, benefit_id: 178 },
          },
          {
            id: 177,
            agent_id: 175,
            name: "\u05e0\u05d9\u05ea\u05d5\u05d7 \u05ea\u05d9\u05e7 \u05dc\u05e7\u05d5\u05d7 \u05d5\u05d4\u05ea\u05d0\u05de\u05d4 \u05d0\u05d9\u05e9\u05d9\u05ea",
            details:
              "\u05d4\u05ea\u05d0\u05de\u05ea \u05d4\u05db\u05d9\u05e1\u05d5\u05d9\u05d9\u05dd \u05dc\u05e4\u05d9 \u05e6\u05d5\u05e8\u05da!",
            active: 1,
            created_at: "2021-08-23T08:55:46.000000Z",
            updated_at: "2021-11-22T10:48:28.000000Z",
            pivot: { agent_mortgage_bid_id: 6374, benefit_id: 177 },
          },
          {
            id: 179,
            agent_id: 175,
            name: "\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
            details:
              "\u05d7\u05d5\u05d3\u05e9\u05d9\u05d9\u05dd \u05d7\u05d9\u05e0\u05dd",
            active: 1,
            created_at: "2021-08-23T13:16:08.000000Z",
            updated_at: "2021-08-23T13:16:08.000000Z",
            pivot: { agent_mortgage_bid_id: 6374, benefit_id: 179 },
          },
        ],
        the_user_request_parameters: {
          agent_id: 25,
          provider_id: 25,
          number_of_insured: "individual",
          age1: "35",
          gender1: "female",
          is_smoking1: false,
          date_of_birth1: "1988-07-07T16:22:22.000000Z",
          health_condition1: "excellent",
          tracks:
            '[{"desired_sum":"1000000","interest_type":"temporary","desired_period":"7","interest_rate":"0.001"},{"desired_sum":"1000000","interest_type":"permanent","desired_period":"8","interest_rate":"2"}]',
          total_loan: 2000000,
          user_id: null,
          ip: "**************",
          screen_width: "2034",
          screen_height: "532",
          sort_by_param: "price_asc",
          sort_by_period: "first_year_sum",
          area_id: "0",
          updated_at: "2023-07-03T16:22:22.000000Z",
          created_at: "2023-07-03T16:22:22.000000Z",
          id: 3966,
        },
        results_form_code: "s9879822",
        token: "hzh1yqtpab",
        companies_offer: [
          "[84.6405,79.5799,70.5677,61.3765,51.6415,37.4115,24.8225,9.0151]",
        ],
        companies_offer_after_agent_discount: 33.8562,
        bid_price_total_years: 3512.45,
        companies_offer_for_all_years_arr: [
          {
            company_one_year_offer: 84.64,
            agent_discount_for_that_year: 60,
            agent_discount_for_that_year_in_money: 609.41,
            company_offer_after_agent_discount_for_month: 33.86,
            company_offer_after_agent_discount_for_year: 406.27,
            incremental_cost: 406.27,
            incremental_year: 1,
          },
          {
            company_one_year_offer: 79.58,
            agent_discount_for_that_year: 40,
            agent_discount_for_that_year_in_money: 381.98,
            company_offer_after_agent_discount_for_month: 47.75,
            company_offer_after_agent_discount_for_year: 572.98,
            incremental_cost: 979.25,
            incremental_year: 2,
          },
          {
            company_one_year_offer: 70.57,
            agent_discount_for_that_year: 30,
            agent_discount_for_that_year_in_money: 254.04,
            company_offer_after_agent_discount_for_month: 49.4,
            company_offer_after_agent_discount_for_year: 592.77,
            incremental_cost: 1572.02,
            incremental_year: 3,
          },
          {
            company_one_year_offer: 61.38,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 110.48,
            company_offer_after_agent_discount_for_month: 52.17,
            company_offer_after_agent_discount_for_year: 626.04,
            incremental_cost: 2198.06,
            incremental_year: 4,
          },
          {
            company_one_year_offer: 51.64,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 92.95,
            company_offer_after_agent_discount_for_month: 43.9,
            company_offer_after_agent_discount_for_year: 526.74,
            incremental_cost: 2724.8,
            incremental_year: 5,
          },
          {
            company_one_year_offer: 37.41,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 67.34,
            company_offer_after_agent_discount_for_month: 31.8,
            company_offer_after_agent_discount_for_year: 381.6,
            incremental_cost: 3106.4,
            incremental_year: 6,
          },
          {
            company_one_year_offer: 24.82,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 24.82,
            company_offer_after_agent_discount_for_year: 297.87,
            incremental_cost: 3404.27,
            incremental_year: 7,
          },
          {
            company_one_year_offer: 9.02,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 9.02,
            company_offer_after_agent_discount_for_year: 108.18,
            incremental_cost: 3512.45,
            incremental_year: 8,
          },
        ],
        offer_price: 33.86,
        b_first_year: 33.86,
        featured_terms: [
          {
            id: 40,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 6,
            text: "<ul>\n<li>\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.</li>\n<li>\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.</li>\n<li>\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.</li></ul>",
            term: "\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.\n\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.\n\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.",
            number: null,
            featured: 1,
            short:
              "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05db\u05d9\u05e1\u05d5\u05d9 \u05d2\u05d5\u05d1\u05d4 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
            note: null,
            created_at: "2020-04-08 11:41:24",
            updated_at: "2023-03-15 17:44:06",
          },
          {
            id: 41,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 7,
            text: '<span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - </span><b style="font-size: 1rem;">78</b><br><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - </span><b style="font-size: 1rem;">74</b><br><span style="font-size: 1rem;">&nbsp;\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - </span><b style="font-size: 1rem;">81</b><br>',
            term: "<div>\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - <b>84</b></div><div>\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - 74&nbsp;</div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - 81</div>",
            number: null,
            featured: 1,
            short:
              "\u05de\u05d3\u05d3 \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 84",
            note: null,
            created_at: "2020-04-08 11:41:39",
            updated_at: "2023-06-13 11:35:56",
          },
        ],
        company_name: "\u05de\u05d2\u05d3\u05dc",
      },
      {
        id: 204,
        agent_id: 204,
        provider_id: 25,
        product_id: 6440,
        policy_id: 7,
        mortgage_insurance_request_id: 3966,
        about_us:
          "\u05de\u05ea\u05df \u05dc\u05d9\u05d5\u05d5\u05d9 \u05de\u05e7\u05d9\u05e3 \u05d5\u05de\u05e7\u05e6\u05d5\u05e2\u05d9 \u05d1\u05db\u05dc \u05e6\u05de\u05ea\u05d9 \u05d4\u05d7\u05d9\u05d9\u05dd \u05d4\u05d7\u05e9\u05d5\u05d1\u05d9\u05dd \u05d1\u05ea\u05d7\u05d5\u05de\u05d9 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d5\u05d4\u05e4\u05e0\u05e1\u05d9\u05d4, \u05e4\u05d9\u05e0\u05e0\u05e1\u05d9\u05dd \u05d5\u05d4\u05e9\u05e7\u05e2\u05d5\u05ea, \u05de\u05d9\u05e1\u05d5\u05d9 \u05d5\u05de\u05e9\u05db\u05e0\u05ea\u05d0\u05d5\u05ea \u05d5\u05d6\u05d0\u05ea \u05ea\u05d5\u05da \u05de\u05ea\u05df \u05de\u05e2\u05e0\u05d4 \u05e9\u05e8\u05d5\u05ea\u05d9, \u05d6\u05de\u05d9\u05e0\u05d5\u05ea \u05d2\u05d1\u05d5\u05d4\u05d4, ",
        agency_name:
          "\u05e6'\u05de\u05e4\u05d9\u05d5\u05df \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d5\u05e4\u05d9\u05e0\u05e0\u05e1\u05d9\u05dd",
        email: "<EMAIL>",
        phone: null,
        site_phone: "073-3745445",
        listing_image:
          "https://trusty.co.il/uploads/agents/listings/agent-listing6395ac2b71858.png",
        address_city: "\u05d0\u05e9\u05e7\u05dc\u05d5\u05df",
        company_img_name: null,
        avg_review: "0.00",
        total_reviews: 0,
        agent_areas: [1, 2, 3, 4, 5, 6],
        total_price: null,
        average_month_price: 33.36,
        company_id: 1,
        discount_first_year: "60.0",
        discount_for_all_years: [
          "60.0",
          "35.0",
          "25.0",
          "15.0",
          "15.0",
          "15.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
        ],
        discount_for_rest_of_the_years: "0.0",
        sort_by_period: "first_year_sum",
        benefits: [],
        the_user_request_parameters: {
          agent_id: 25,
          provider_id: 25,
          number_of_insured: "individual",
          age1: "35",
          gender1: "female",
          is_smoking1: false,
          date_of_birth1: "1988-07-07T16:22:22.000000Z",
          health_condition1: "excellent",
          tracks:
            '[{"desired_sum":"1000000","interest_type":"temporary","desired_period":"7","interest_rate":"0.001"},{"desired_sum":"1000000","interest_type":"permanent","desired_period":"8","interest_rate":"2"}]',
          total_loan: 2000000,
          user_id: null,
          ip: "**************",
          screen_width: "2034",
          screen_height: "532",
          sort_by_param: "price_asc",
          sort_by_period: "first_year_sum",
          area_id: "0",
          updated_at: "2023-07-03T16:22:22.000000Z",
          created_at: "2023-07-03T16:22:22.000000Z",
          id: 3966,
        },
        results_form_code: "s9879822",
        token: "u760zctsyt",
        companies_offer: [
          "[84.6405,79.5799,70.5677,61.3765,51.6415,37.4115,24.8225,9.0151]",
        ],
        companies_offer_after_agent_discount: 33.8562,
        bid_price_total_years: 3602.54,
        companies_offer_for_all_years_arr: [
          {
            company_one_year_offer: 84.64,
            agent_discount_for_that_year: 60,
            agent_discount_for_that_year_in_money: 609.41,
            company_offer_after_agent_discount_for_month: 33.86,
            company_offer_after_agent_discount_for_year: 406.27,
            incremental_cost: 406.27,
            incremental_year: 1,
          },
          {
            company_one_year_offer: 79.58,
            agent_discount_for_that_year: 35,
            agent_discount_for_that_year_in_money: 334.24,
            company_offer_after_agent_discount_for_month: 51.73,
            company_offer_after_agent_discount_for_year: 620.72,
            incremental_cost: 1027,
            incremental_year: 2,
          },
          {
            company_one_year_offer: 70.57,
            agent_discount_for_that_year: 25,
            agent_discount_for_that_year_in_money: 211.7,
            company_offer_after_agent_discount_for_month: 52.93,
            company_offer_after_agent_discount_for_year: 635.11,
            incremental_cost: 1662.11,
            incremental_year: 3,
          },
          {
            company_one_year_offer: 61.38,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 110.48,
            company_offer_after_agent_discount_for_month: 52.17,
            company_offer_after_agent_discount_for_year: 626.04,
            incremental_cost: 2288.15,
            incremental_year: 4,
          },
          {
            company_one_year_offer: 51.64,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 92.95,
            company_offer_after_agent_discount_for_month: 43.9,
            company_offer_after_agent_discount_for_year: 526.74,
            incremental_cost: 2814.89,
            incremental_year: 5,
          },
          {
            company_one_year_offer: 37.41,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 67.34,
            company_offer_after_agent_discount_for_month: 31.8,
            company_offer_after_agent_discount_for_year: 381.6,
            incremental_cost: 3196.49,
            incremental_year: 6,
          },
          {
            company_one_year_offer: 24.82,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 24.82,
            company_offer_after_agent_discount_for_year: 297.87,
            incremental_cost: 3494.36,
            incremental_year: 7,
          },
          {
            company_one_year_offer: 9.02,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 9.02,
            company_offer_after_agent_discount_for_year: 108.18,
            incremental_cost: 3602.54,
            incremental_year: 8,
          },
        ],
        offer_price: 33.86,
        b_first_year: 33.86,
        featured_terms: [
          {
            id: 40,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 6,
            text: "<ul>\n<li>\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.</li>\n<li>\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.</li>\n<li>\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.</li></ul>",
            term: "\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.\n\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.\n\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.",
            number: null,
            featured: 1,
            short:
              "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05db\u05d9\u05e1\u05d5\u05d9 \u05d2\u05d5\u05d1\u05d4 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
            note: null,
            created_at: "2020-04-08 11:41:24",
            updated_at: "2023-03-15 17:44:06",
          },
          {
            id: 41,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 7,
            text: '<span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - </span><b style="font-size: 1rem;">78</b><br><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - </span><b style="font-size: 1rem;">74</b><br><span style="font-size: 1rem;">&nbsp;\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - </span><b style="font-size: 1rem;">81</b><br>',
            term: "<div>\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - <b>84</b></div><div>\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - 74&nbsp;</div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - 81</div>",
            number: null,
            featured: 1,
            short:
              "\u05de\u05d3\u05d3 \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 84",
            note: null,
            created_at: "2020-04-08 11:41:39",
            updated_at: "2023-06-13 11:35:56",
          },
        ],
        company_name: "\u05de\u05d2\u05d3\u05dc",
      },
      {
        id: 187,
        agent_id: 187,
        provider_id: 25,
        product_id: 6388,
        policy_id: 7,
        mortgage_insurance_request_id: 3966,
        about_us:
          "\u05d2\u05e4\u05df \u05e1\u05d5\u05db\u05e0\u05d5\u05ea \u05dc\u05d1\u05d9\u05d8\u05d5\u05d7 , \u05d1\u05e2\u05dc\u05ea \u05e0\u05d9\u05e1\u05d9\u05d5\u05df \u05e9\u05dc \u05de\u05e2\u05dc 50 \u05e9\u05e0\u05d9\u05dd \u05d5\u05de\u05e2\u05dc 30,000 \u05de\u05d1\u05d5\u05d8\u05d7\u05d9\u05dd \u05de\u05e8\u05d5\u05e6\u05d9\u05dd . \u05d9\u05ea\u05e8\u05d5\u05df \u05d6\u05d4 \u05de\u05d0\u05e4\u05e9\u05e8 \u05dc\u05e0\u05d5 \u05dc\u05e1\u05e4\u05e7 \u05dc\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea\u05d9\u05e0\u05d5 \u05d0\u05ea \u05de\u05d5\u05e6\u05e8\u05d9 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d8\u05d5\u05d1\u05d9\u05dd \u05d1\u05d9\u05d5\u05ea\u05e8, \u05ea\u05d5\u05da \u05d4\u05e7\u05e4\u05d3\u05d4 \u05e2\u05dc \u05e9\u05d9\u05e8\u05d5\u05ea \u05d0\u05d9\u05e9\u05d9 \u05d5\u05dc\u05d9\u05d5\u05d5\u05d9 \u05d4\u05dc\u05e7\u05d5\u05d7 \u05dc\u05d0\u05d5\u05e8\u05da \u05d6\u05de\u05df .",
        agency_name:
          "\u05d2\u05e4\u05df \u05e1\u05d5\u05db\u05e0\u05d5\u05d9\u05d5\u05ea \u05dc\u05d1\u05d9\u05d8\u05d5\u05d7",
        email: "<EMAIL>",
        phone: null,
        site_phone: "073-3745433",
        listing_image:
          "https://trusty.co.il/uploads/agents/listings/agent-listing62409756d658c.png",
        address_city: "\u05d2\u05d1\u05e2\u05ea\u05d9\u05d9\u05dd",
        company_img_name: null,
        avg_review: "6.20",
        total_reviews: 2,
        agent_areas: [1, 2, 3, 4, 5, 6],
        total_price: null,
        average_month_price: 33.04,
        company_id: 1,
        discount_first_year: "55.0",
        discount_for_all_years: [
          "55.0",
          "30.0",
          "30.0",
          "20.0",
          "20.0",
          "20.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
        ],
        discount_for_rest_of_the_years: "50.0",
        sort_by_period: "first_year_sum",
        benefits: [
          {
            id: 207,
            agent_id: 187,
            name: "\u05dc\u05d9\u05d5\u05d5\u05d9 \u05e9\u05d5\u05d8\u05e3 \u05e9\u05dc \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7\u05d9\u05dd \u05e9\u05dc\u05da",
            details:
              "\u05dc\u05d9\u05d5\u05d5\u05d9 \u05e9\u05d5\u05d8\u05e3 \u05e9\u05dc \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7\u05d9\u05dd \u05e9\u05dc\u05da",
            active: 1,
            created_at: "2021-12-06T16:15:16.000000Z",
            updated_at: "2021-12-06T16:44:58.000000Z",
            pivot: { agent_mortgage_bid_id: 6388, benefit_id: 207 },
          },
          {
            id: 208,
            agent_id: 187,
            name: "\u05d1\u05d3\u05d9\u05e7\u05ea \u05ea\u05d9\u05e7 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7\u05d9\u05dd \u05d1\u05d7\u05d9\u05e0\u05dd",
            details:
              "\u05d1\u05d3\u05d9\u05e7\u05ea \u05ea\u05d9\u05e7 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7\u05d9\u05dd \u05d1\u05d7\u05d9\u05e0\u05dd",
            active: 1,
            created_at: "2021-12-06T16:16:02.000000Z",
            updated_at: "2021-12-06T16:16:02.000000Z",
            pivot: { agent_mortgage_bid_id: 6388, benefit_id: 208 },
          },
        ],
        the_user_request_parameters: {
          agent_id: 25,
          provider_id: 25,
          number_of_insured: "individual",
          age1: "35",
          gender1: "female",
          is_smoking1: false,
          date_of_birth1: "1988-07-07T16:22:22.000000Z",
          health_condition1: "excellent",
          tracks:
            '[{"desired_sum":"1000000","interest_type":"temporary","desired_period":"7","interest_rate":"0.001"},{"desired_sum":"1000000","interest_type":"permanent","desired_period":"8","interest_rate":"2"}]',
          total_loan: 2000000,
          user_id: null,
          ip: "**************",
          screen_width: "2034",
          screen_height: "532",
          sort_by_param: "price_asc",
          sort_by_period: "first_year_sum",
          area_id: "0",
          updated_at: "2023-07-03T16:22:22.000000Z",
          created_at: "2023-07-03T16:22:22.000000Z",
          id: 3966,
        },
        results_form_code: "s9879822",
        token: "nryafkd4be",
        companies_offer: [
          "[84.6405,79.5799,70.5677,61.3765,51.6415,37.4115,24.8225,9.0151]",
        ],
        companies_offer_after_agent_discount: 38.088225,
        bid_price_total_years: 3568.47,
        companies_offer_for_all_years_arr: [
          {
            company_one_year_offer: 84.64,
            agent_discount_for_that_year: 55,
            agent_discount_for_that_year_in_money: 558.63,
            company_offer_after_agent_discount_for_month: 38.09,
            company_offer_after_agent_discount_for_year: 457.06,
            incremental_cost: 457.06,
            incremental_year: 1,
          },
          {
            company_one_year_offer: 79.58,
            agent_discount_for_that_year: 30,
            agent_discount_for_that_year_in_money: 286.49,
            company_offer_after_agent_discount_for_month: 55.71,
            company_offer_after_agent_discount_for_year: 668.47,
            incremental_cost: 1125.53,
            incremental_year: 2,
          },
          {
            company_one_year_offer: 70.57,
            agent_discount_for_that_year: 30,
            agent_discount_for_that_year_in_money: 254.04,
            company_offer_after_agent_discount_for_month: 49.4,
            company_offer_after_agent_discount_for_year: 592.77,
            incremental_cost: 1718.3,
            incremental_year: 3,
          },
          {
            company_one_year_offer: 61.38,
            agent_discount_for_that_year: 20,
            agent_discount_for_that_year_in_money: 147.3,
            company_offer_after_agent_discount_for_month: 49.1,
            company_offer_after_agent_discount_for_year: 589.21,
            incremental_cost: 2307.51,
            incremental_year: 4,
          },
          {
            company_one_year_offer: 51.64,
            agent_discount_for_that_year: 20,
            agent_discount_for_that_year_in_money: 123.94,
            company_offer_after_agent_discount_for_month: 41.31,
            company_offer_after_agent_discount_for_year: 495.76,
            incremental_cost: 2803.27,
            incremental_year: 5,
          },
          {
            company_one_year_offer: 37.41,
            agent_discount_for_that_year: 20,
            agent_discount_for_that_year_in_money: 89.79,
            company_offer_after_agent_discount_for_month: 29.93,
            company_offer_after_agent_discount_for_year: 359.15,
            incremental_cost: 3162.42,
            incremental_year: 6,
          },
          {
            company_one_year_offer: 24.82,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 24.82,
            company_offer_after_agent_discount_for_year: 297.87,
            incremental_cost: 3460.29,
            incremental_year: 7,
          },
          {
            company_one_year_offer: 9.02,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 9.02,
            company_offer_after_agent_discount_for_year: 108.18,
            incremental_cost: 3568.47,
            incremental_year: 8,
          },
        ],
        offer_price: 38.09,
        b_first_year: 38.09,
        featured_terms: [
          {
            id: 40,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 6,
            text: "<ul>\n<li>\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.</li>\n<li>\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.</li>\n<li>\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.</li></ul>",
            term: "\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.\n\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.\n\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.",
            number: null,
            featured: 1,
            short:
              "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05db\u05d9\u05e1\u05d5\u05d9 \u05d2\u05d5\u05d1\u05d4 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
            note: null,
            created_at: "2020-04-08 11:41:24",
            updated_at: "2023-03-15 17:44:06",
          },
          {
            id: 41,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 7,
            text: '<span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - </span><b style="font-size: 1rem;">78</b><br><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - </span><b style="font-size: 1rem;">74</b><br><span style="font-size: 1rem;">&nbsp;\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - </span><b style="font-size: 1rem;">81</b><br>',
            term: "<div>\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - <b>84</b></div><div>\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - 74&nbsp;</div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - 81</div>",
            number: null,
            featured: 1,
            short:
              "\u05de\u05d3\u05d3 \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 84",
            note: null,
            created_at: "2020-04-08 11:41:39",
            updated_at: "2023-06-13 11:35:56",
          },
        ],
        company_name: "\u05de\u05d2\u05d3\u05dc",
      },
      {
        id: 143,
        agent_id: 143,
        provider_id: 25,
        product_id: 6167,
        policy_id: 7,
        mortgage_insurance_request_id: 3966,
        about_us:
          "\u05e1\u05d5\u05db\u05df \u05d1\u05d9\u05d8\u05d5\u05d7 \u05e4\u05e0\u05e1\u05d9\u05d5\u05e0\u05d9, \u05d1\u05e2\u05dc \u05ea\u05d5\u05d0\u05e8 \u05e8\u05d0\u05e9\u05d5\u05df \u05d5\u05e9\u05e0\u05d9 \u05d1\u05de\u05e0\u05d4\u05dc \u05e2\u05e1\u05e7\u05d9\u05dd \u05d4\u05ea\u05de\u05d7\u05d5\u05ea \u05d1\u05de\u05d9\u05de\u05d5\u05df. \u05e2\u05d5\u05e1\u05e7 \u05d1\u05e4\u05d9\u05e0\u05e0\u05e1\u05d9\u05dd, \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e8\u05d9\u05d0\u05d5\u05ea , \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05d9\u05d9\u05dd, \u05e7\u05e8\u05e0\u05d5\u05ea \u05e4\u05e0\u05e1\u05d9\u05d4, \u05e7\u05d5\u05e4\u05ea \u05d2\u05de\u05dc \u05dc\u05d4\u05e9\u05e7\u05e2\u05d4 \u05d5\u05d7\u05d9\u05e1\u05db\u05d5\u05df \u05de\u05e0\u05d5\u05d4\u05dc .",
        agency_name:
          "\u05d2\u05d9\u05d0 \u05e9\u05d9\u05d9\u05d8\u05dc\u05d1\u05d5\u05d9\u05dd \u05e1\u05d5\u05db\u05df \u05d1\u05d9\u05d8\u05d5\u05d7 \u05e4\u05e0\u05e1\u05d9\u05d5\u05e0\u05d9",
        email: "<EMAIL>",
        phone: null,
        site_phone: "073-3745416",
        listing_image:
          "https://trusty.co.il/uploads/agents/listings/agent-listing60034060ad287.png",
        address_city: "\u05e4\u05ea\u05d7 \u05ea\u05e7\u05d5\u05d5\u05d4",
        company_img_name: null,
        avg_review: "0.00",
        total_reviews: 0,
        agent_areas: [1, 2, 3, 4, 5, 6],
        total_price: null,
        average_month_price: 34.3,
        company_id: 1,
        discount_first_year: "50.0",
        discount_for_all_years: [
          "50.0",
          "35.0",
          "25.0",
          "15.0",
          "15.0",
          "15.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
        ],
        discount_for_rest_of_the_years: "0.0",
        sort_by_period: "first_year_sum",
        benefits: [
          {
            id: 125,
            agent_id: 143,
            name: "\u05e9\u05d9\u05d7\u05ea \u05d9\u05e2\u05d5\u05e5 \u05d7\u05d9\u05e0\u05dd",
            details:
              "\u05d1\u05d3\u05d9\u05e7\u05ea \u05ea\u05d9\u05e7 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05d9\u05e0\u05dd",
            active: 1,
            created_at: "2020-04-19T12:16:29.000000Z",
            updated_at: "2020-12-30T14:31:58.000000Z",
            pivot: { agent_mortgage_bid_id: 6167, benefit_id: 125 },
          },
        ],
        the_user_request_parameters: {
          agent_id: 25,
          provider_id: 25,
          number_of_insured: "individual",
          age1: "35",
          gender1: "female",
          is_smoking1: false,
          date_of_birth1: "1988-07-07T16:22:22.000000Z",
          health_condition1: "excellent",
          tracks:
            '[{"desired_sum":"1000000","interest_type":"temporary","desired_period":"7","interest_rate":"0.001"},{"desired_sum":"1000000","interest_type":"permanent","desired_period":"8","interest_rate":"2"}]',
          total_loan: 2000000,
          user_id: null,
          ip: "**************",
          screen_width: "2034",
          screen_height: "532",
          sort_by_param: "price_asc",
          sort_by_period: "first_year_sum",
          area_id: "0",
          updated_at: "2023-07-03T16:22:22.000000Z",
          created_at: "2023-07-03T16:22:22.000000Z",
          id: 3966,
        },
        results_form_code: "s9879822",
        token: "0sujwdxdqt",
        companies_offer: [
          "[84.6405,79.5799,70.5677,61.3765,51.6415,37.4115,24.8225,9.0151]",
        ],
        companies_offer_after_agent_discount: 42.32025,
        bid_price_total_years: 3704.11,
        companies_offer_for_all_years_arr: [
          {
            company_one_year_offer: 84.64,
            agent_discount_for_that_year: 50,
            agent_discount_for_that_year_in_money: 507.84,
            company_offer_after_agent_discount_for_month: 42.32,
            company_offer_after_agent_discount_for_year: 507.84,
            incremental_cost: 507.84,
            incremental_year: 1,
          },
          {
            company_one_year_offer: 79.58,
            agent_discount_for_that_year: 35,
            agent_discount_for_that_year_in_money: 334.24,
            company_offer_after_agent_discount_for_month: 51.73,
            company_offer_after_agent_discount_for_year: 620.72,
            incremental_cost: 1128.57,
            incremental_year: 2,
          },
          {
            company_one_year_offer: 70.57,
            agent_discount_for_that_year: 25,
            agent_discount_for_that_year_in_money: 211.7,
            company_offer_after_agent_discount_for_month: 52.93,
            company_offer_after_agent_discount_for_year: 635.11,
            incremental_cost: 1763.68,
            incremental_year: 3,
          },
          {
            company_one_year_offer: 61.38,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 110.48,
            company_offer_after_agent_discount_for_month: 52.17,
            company_offer_after_agent_discount_for_year: 626.04,
            incremental_cost: 2389.72,
            incremental_year: 4,
          },
          {
            company_one_year_offer: 51.64,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 92.95,
            company_offer_after_agent_discount_for_month: 43.9,
            company_offer_after_agent_discount_for_year: 526.74,
            incremental_cost: 2916.46,
            incremental_year: 5,
          },
          {
            company_one_year_offer: 37.41,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 67.34,
            company_offer_after_agent_discount_for_month: 31.8,
            company_offer_after_agent_discount_for_year: 381.6,
            incremental_cost: 3298.06,
            incremental_year: 6,
          },
          {
            company_one_year_offer: 24.82,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 24.82,
            company_offer_after_agent_discount_for_year: 297.87,
            incremental_cost: 3595.93,
            incremental_year: 7,
          },
          {
            company_one_year_offer: 9.02,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 9.02,
            company_offer_after_agent_discount_for_year: 108.18,
            incremental_cost: 3704.11,
            incremental_year: 8,
          },
        ],
        offer_price: 42.32,
        b_first_year: 42.32,
        featured_terms: [
          {
            id: 40,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 6,
            text: "<ul>\n<li>\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.</li>\n<li>\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.</li>\n<li>\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.</li></ul>",
            term: "\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.\n\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.\n\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.",
            number: null,
            featured: 1,
            short:
              "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05db\u05d9\u05e1\u05d5\u05d9 \u05d2\u05d5\u05d1\u05d4 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
            note: null,
            created_at: "2020-04-08 11:41:24",
            updated_at: "2023-03-15 17:44:06",
          },
          {
            id: 41,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 7,
            text: '<span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - </span><b style="font-size: 1rem;">78</b><br><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - </span><b style="font-size: 1rem;">74</b><br><span style="font-size: 1rem;">&nbsp;\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - </span><b style="font-size: 1rem;">81</b><br>',
            term: "<div>\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - <b>84</b></div><div>\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - 74&nbsp;</div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - 81</div>",
            number: null,
            featured: 1,
            short:
              "\u05de\u05d3\u05d3 \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 84",
            note: null,
            created_at: "2020-04-08 11:41:39",
            updated_at: "2023-06-13 11:35:56",
          },
        ],
        company_name: "\u05de\u05d2\u05d3\u05dc",
      },
      {
        id: 182,
        agent_id: 182,
        provider_id: 25,
        product_id: 6381,
        policy_id: 7,
        mortgage_insurance_request_id: 3966,
        about_us:
          '\u05d1\u05e8\u05e7 \u05de\u05e8\u05e7\u05d5\u05d1 \u2013 \u05de\u05e0\u05db"\u05dc \u05d5\u05de\u05d9\u05d9\u05e1\u05d3 Markov \u2013 Financial Planning \u05d1\u05e2\u05dc \u05e0\u05e1\u05d9\u05d5\u05df \u05e9\u05dc \u05de\u05e2\u05dc 12 \u05e9\u05e0\u05d4 \u05d1\u05e9\u05d5\u05e7 \u05d4\u05d4\u05d5\u05df, \u05d1\u05d7\u05d1\u05e8\u05d5\u05ea \u05d4\u05de\u05d5\u05d1\u05d9\u05dc\u05d5\u05ea \u05d1\u05d0\u05e8\u05e5 \u05d1\u05ea\u05d7\u05d5\u05dd \u05d4\u05e4\u05d9\u05e0\u05e0\u05e1\u05d9\u05dd \u05d5\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7.',
        agency_name:
          "\u05de\u05e8\u05e7\u05d5\u05d1 \u05ea\u05db\u05e0\u05d5\u05df \u05e4\u05d9\u05e0\u05e0\u05e1\u05d9",
        email: "<EMAIL>",
        phone: null,
        site_phone: "073-3745426",
        listing_image:
          "https://trusty.co.il/uploads/agents/listings/agent-listing6190dcd9a312c.png",
        address_city: "\u05d7\u05d5\u05dc\u05d5\u05df",
        company_img_name: null,
        avg_review: "0.00",
        total_reviews: 0,
        agent_areas: [1, 2, 3, 4, 5, 6],
        total_price: null,
        average_month_price: 35.08,
        company_id: 1,
        discount_first_year: "50.0",
        discount_for_all_years: [
          "50.0",
          "35.0",
          "15.0",
          "15.0",
          "15.0",
          "15.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
        ],
        discount_for_rest_of_the_years: "50.0",
        sort_by_period: "first_year_sum",
        benefits: [
          {
            id: 194,
            agent_id: 182,
            name: "\u05dc\u05d9\u05d5\u05d5\u05d9 \u05d0\u05d9\u05e9\u05d9",
            details:
              "\u05dc\u05d9\u05d5\u05d5\u05d9 \u05e2\u05dc \u05d9\u05d3\u05d9 \u05de\u05e0\u05d4\u05dc \u05ea\u05d9\u05e7 \u05d0\u05d9\u05e9\u05d9",
            active: 1,
            created_at: "2021-11-11T20:25:14.000000Z",
            updated_at: "2021-11-11T20:25:14.000000Z",
            pivot: { agent_mortgage_bid_id: 6381, benefit_id: 194 },
          },
        ],
        the_user_request_parameters: {
          agent_id: 25,
          provider_id: 25,
          number_of_insured: "individual",
          age1: "35",
          gender1: "female",
          is_smoking1: false,
          date_of_birth1: "1988-07-07T16:22:22.000000Z",
          health_condition1: "excellent",
          tracks:
            '[{"desired_sum":"1000000","interest_type":"temporary","desired_period":"7","interest_rate":"0.001"},{"desired_sum":"1000000","interest_type":"permanent","desired_period":"8","interest_rate":"2"}]',
          total_loan: 2000000,
          user_id: null,
          ip: "**************",
          screen_width: "2034",
          screen_height: "532",
          sort_by_param: "price_asc",
          sort_by_period: "first_year_sum",
          area_id: "0",
          updated_at: "2023-07-03T16:22:22.000000Z",
          created_at: "2023-07-03T16:22:22.000000Z",
          id: 3966,
        },
        results_form_code: "s9879822",
        token: "jvmbbj1lu2",
        companies_offer: [
          "[84.6405,79.5799,70.5677,61.3765,51.6415,37.4115,24.8225,9.0151]",
        ],
        companies_offer_after_agent_discount: 42.32025,
        bid_price_total_years: 3788.79,
        companies_offer_for_all_years_arr: [
          {
            company_one_year_offer: 84.64,
            agent_discount_for_that_year: 50,
            agent_discount_for_that_year_in_money: 507.84,
            company_offer_after_agent_discount_for_month: 42.32,
            company_offer_after_agent_discount_for_year: 507.84,
            incremental_cost: 507.84,
            incremental_year: 1,
          },
          {
            company_one_year_offer: 79.58,
            agent_discount_for_that_year: 35,
            agent_discount_for_that_year_in_money: 334.24,
            company_offer_after_agent_discount_for_month: 51.73,
            company_offer_after_agent_discount_for_year: 620.72,
            incremental_cost: 1128.57,
            incremental_year: 2,
          },
          {
            company_one_year_offer: 70.57,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 127.02,
            company_offer_after_agent_discount_for_month: 59.98,
            company_offer_after_agent_discount_for_year: 719.79,
            incremental_cost: 1848.36,
            incremental_year: 3,
          },
          {
            company_one_year_offer: 61.38,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 110.48,
            company_offer_after_agent_discount_for_month: 52.17,
            company_offer_after_agent_discount_for_year: 626.04,
            incremental_cost: 2474.4,
            incremental_year: 4,
          },
          {
            company_one_year_offer: 51.64,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 92.95,
            company_offer_after_agent_discount_for_month: 43.9,
            company_offer_after_agent_discount_for_year: 526.74,
            incremental_cost: 3001.14,
            incremental_year: 5,
          },
          {
            company_one_year_offer: 37.41,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 67.34,
            company_offer_after_agent_discount_for_month: 31.8,
            company_offer_after_agent_discount_for_year: 381.6,
            incremental_cost: 3382.74,
            incremental_year: 6,
          },
          {
            company_one_year_offer: 24.82,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 24.82,
            company_offer_after_agent_discount_for_year: 297.87,
            incremental_cost: 3680.61,
            incremental_year: 7,
          },
          {
            company_one_year_offer: 9.02,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 9.02,
            company_offer_after_agent_discount_for_year: 108.18,
            incremental_cost: 3788.79,
            incremental_year: 8,
          },
        ],
        offer_price: 42.32,
        b_first_year: 42.32,
        featured_terms: [
          {
            id: 40,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 6,
            text: "<ul>\n<li>\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.</li>\n<li>\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.</li>\n<li>\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.</li></ul>",
            term: "\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.\n\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.\n\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.",
            number: null,
            featured: 1,
            short:
              "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05db\u05d9\u05e1\u05d5\u05d9 \u05d2\u05d5\u05d1\u05d4 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
            note: null,
            created_at: "2020-04-08 11:41:24",
            updated_at: "2023-03-15 17:44:06",
          },
          {
            id: 41,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 7,
            text: '<span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - </span><b style="font-size: 1rem;">78</b><br><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - </span><b style="font-size: 1rem;">74</b><br><span style="font-size: 1rem;">&nbsp;\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - </span><b style="font-size: 1rem;">81</b><br>',
            term: "<div>\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - <b>84</b></div><div>\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - 74&nbsp;</div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - 81</div>",
            number: null,
            featured: 1,
            short:
              "\u05de\u05d3\u05d3 \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 84",
            note: null,
            created_at: "2020-04-08 11:41:39",
            updated_at: "2023-06-13 11:35:56",
          },
        ],
        company_name: "\u05de\u05d2\u05d3\u05dc",
      },
      {
        id: 246,
        agent_id: 246,
        provider_id: 25,
        product_id: 6486,
        policy_id: 7,
        mortgage_insurance_request_id: 3966,
        about_us: "copo",
        agency_name: "copo",
        email: "<EMAIL>",
        phone: null,
        site_phone: "073-3745442",
        listing_image:
          "https://trusty.co.il/uploads/agents/listings/default_agent_image.png",
        address_city: "\u05d0\u05d5\u05e8 \u05d9\u05d4\u05d5\u05d3\u05d4",
        company_img_name: null,
        avg_review: "0.00",
        total_reviews: 0,
        agent_areas: [1, 2, 3, 4, 5, 6],
        total_price: null,
        average_month_price: 33.85,
        company_id: 1,
        discount_first_year: "50.0",
        discount_for_all_years: [
          "50.0",
          "35.0",
          "20.0",
          "20.0",
          "20.0",
          "20.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
        ],
        discount_for_rest_of_the_years: "0.0",
        sort_by_period: "first_year_sum",
        benefits: [],
        the_user_request_parameters: {
          agent_id: 25,
          provider_id: 25,
          number_of_insured: "individual",
          age1: "35",
          gender1: "female",
          is_smoking1: false,
          date_of_birth1: "1988-07-07T16:22:22.000000Z",
          health_condition1: "excellent",
          tracks:
            '[{"desired_sum":"1000000","interest_type":"temporary","desired_period":"7","interest_rate":"0.001"},{"desired_sum":"1000000","interest_type":"permanent","desired_period":"8","interest_rate":"2"}]',
          total_loan: 2000000,
          user_id: null,
          ip: "**************",
          screen_width: "2034",
          screen_height: "532",
          sort_by_param: "price_asc",
          sort_by_period: "first_year_sum",
          area_id: "0",
          updated_at: "2023-07-03T16:22:22.000000Z",
          created_at: "2023-07-03T16:22:22.000000Z",
          id: 3966,
        },
        results_form_code: "s9879822",
        token: "s2jauu6vey",
        companies_offer: [
          "[84.6405,79.5799,70.5677,61.3765,51.6415,37.4115,24.8225,9.0151]",
        ],
        companies_offer_after_agent_discount: 42.32025,
        bid_price_total_years: 3656.19,
        companies_offer_for_all_years_arr: [
          {
            company_one_year_offer: 84.64,
            agent_discount_for_that_year: 50,
            agent_discount_for_that_year_in_money: 507.84,
            company_offer_after_agent_discount_for_month: 42.32,
            company_offer_after_agent_discount_for_year: 507.84,
            incremental_cost: 507.84,
            incremental_year: 1,
          },
          {
            company_one_year_offer: 79.58,
            agent_discount_for_that_year: 35,
            agent_discount_for_that_year_in_money: 334.24,
            company_offer_after_agent_discount_for_month: 51.73,
            company_offer_after_agent_discount_for_year: 620.72,
            incremental_cost: 1128.57,
            incremental_year: 2,
          },
          {
            company_one_year_offer: 70.57,
            agent_discount_for_that_year: 20,
            agent_discount_for_that_year_in_money: 169.36,
            company_offer_after_agent_discount_for_month: 56.45,
            company_offer_after_agent_discount_for_year: 677.45,
            incremental_cost: 1806.02,
            incremental_year: 3,
          },
          {
            company_one_year_offer: 61.38,
            agent_discount_for_that_year: 20,
            agent_discount_for_that_year_in_money: 147.3,
            company_offer_after_agent_discount_for_month: 49.1,
            company_offer_after_agent_discount_for_year: 589.21,
            incremental_cost: 2395.23,
            incremental_year: 4,
          },
          {
            company_one_year_offer: 51.64,
            agent_discount_for_that_year: 20,
            agent_discount_for_that_year_in_money: 123.94,
            company_offer_after_agent_discount_for_month: 41.31,
            company_offer_after_agent_discount_for_year: 495.76,
            incremental_cost: 2890.99,
            incremental_year: 5,
          },
          {
            company_one_year_offer: 37.41,
            agent_discount_for_that_year: 20,
            agent_discount_for_that_year_in_money: 89.79,
            company_offer_after_agent_discount_for_month: 29.93,
            company_offer_after_agent_discount_for_year: 359.15,
            incremental_cost: 3250.14,
            incremental_year: 6,
          },
          {
            company_one_year_offer: 24.82,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 24.82,
            company_offer_after_agent_discount_for_year: 297.87,
            incremental_cost: 3548.01,
            incremental_year: 7,
          },
          {
            company_one_year_offer: 9.02,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 9.02,
            company_offer_after_agent_discount_for_year: 108.18,
            incremental_cost: 3656.19,
            incremental_year: 8,
          },
        ],
        offer_price: 42.32,
        b_first_year: 42.32,
        featured_terms: [
          {
            id: 40,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 6,
            text: "<ul>\n<li>\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.</li>\n<li>\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.</li>\n<li>\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.</li></ul>",
            term: "\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.\n\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.\n\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.",
            number: null,
            featured: 1,
            short:
              "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05db\u05d9\u05e1\u05d5\u05d9 \u05d2\u05d5\u05d1\u05d4 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
            note: null,
            created_at: "2020-04-08 11:41:24",
            updated_at: "2023-03-15 17:44:06",
          },
          {
            id: 41,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 7,
            text: '<span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - </span><b style="font-size: 1rem;">78</b><br><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - </span><b style="font-size: 1rem;">74</b><br><span style="font-size: 1rem;">&nbsp;\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - </span><b style="font-size: 1rem;">81</b><br>',
            term: "<div>\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - <b>84</b></div><div>\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - 74&nbsp;</div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - 81</div>",
            number: null,
            featured: 1,
            short:
              "\u05de\u05d3\u05d3 \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 84",
            note: null,
            created_at: "2020-04-08 11:41:39",
            updated_at: "2023-06-13 11:35:56",
          },
        ],
        company_name: "\u05de\u05d2\u05d3\u05dc",
      },
      {
        id: 26,
        agent_id: 26,
        provider_id: 25,
        product_id: 6367,
        policy_id: 7,
        mortgage_insurance_request_id: 3966,
        about_us:
          "\u05e0\u05e1\u05d9\u05dd \u05d2\u05d9\u05e1\u05e4\u05df - \u05e1\u05d5\u05db\u05df \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d5\u05e4\u05d9\u05e0\u05e0\u05e1\u05d9\u05dd.\r\n\u05d4\u05e9\u05e7\u05e4\u05ea\u05d9 \u05d4\u05d9\u05d0 \u05e9\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea \u05de\u05e8\u05d5\u05e6\u05d9\u05dd \u05d4\u05dd \u05d4\u05de\u05e0\u05d5\u05e2 \u05d4\u05e2\u05e6\u05de\u05ea\u05d9 \u05d1\u05d9\u05d5\u05ea\u05e8 \u05dc\u05d4\u05e6\u05dc\u05d7\u05d4 \u05de\u05e9\u05d5\u05ea\u05e4\u05ea \u05d5\u05dc\u05db\u05df \u05d0\u05ea\u05dd, \u05d4\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea \u05e9\u05dc\u05e0\u05d5, \u05ea\u05d4\u05d9\u05e0\u05d5 \u05de\u05e8\u05de\u05ea \u05e9\u05d9\u05e8\u05d5\u05ea \u05d2\u05d1\u05d5\u05d4\u05d4, \u05d9\u05d7\u05e1 \u05d0\u05d9\u05e9\u05d9 \u05d5\u05d6\u05de\u05d9\u05e0\u05d5\u05ea \u05de\u05e7\u05e1\u05d9\u05de\u05dc\u05d9\u05ea \u05dc\u05db\u05dc \u05d0\u05d5\u05e8\u05da \u05d4\u05d3\u05e8\u05da.",
        agency_name:
          "\u05e0\u05e1\u05d9\u05dd \u05d2\u05d9\u05e1\u05e4\u05df \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d5\u05e4\u05d9\u05e0\u05e0\u05e1\u05d9\u05dd",
        email: "<EMAIL>",
        phone: "03-7200360",
        site_phone: "073-3745411",
        listing_image:
          "https://trusty.co.il/uploads/agents/listings/agent-listing602d55bc26da3.png",
        address_city: "\u05d4\u05e8\u05e6\u05dc\u05d9\u05d4",
        company_img_name: null,
        avg_review: "9.20",
        total_reviews: 1,
        agent_areas: [2, 3, 4, 5, 6],
        total_price: null,
        average_month_price: 35.08,
        company_id: 1,
        discount_first_year: "50.0",
        discount_for_all_years: [
          "50.0",
          "35.0",
          "15.0",
          "15.0",
          "15.0",
          "15.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
        ],
        discount_for_rest_of_the_years: "50.0",
        sort_by_period: "first_year_sum",
        benefits: [
          {
            id: 82,
            agent_id: 26,
            name: "\u05d7\u05d5\u05d3\u05e9\u05d9\u05d9\u05dd \u05d7\u05d9\u05e0\u05dd \u05d7\u05d9\u05d9\u05dd/\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
            details:
              "\u05d7\u05d5\u05d3\u05e9\u05d9\u05d9\u05dd \u05e8\u05d0\u05e9\u05d5\u05e0\u05d9\u05dd \u05d7\u05d9\u05e0\u05dd",
            active: 1,
            created_at: "2019-12-03T09:32:42.000000Z",
            updated_at: "2021-03-03T13:08:29.000000Z",
            pivot: { agent_mortgage_bid_id: 6367, benefit_id: 82 },
          },
        ],
        the_user_request_parameters: {
          agent_id: 25,
          provider_id: 25,
          number_of_insured: "individual",
          age1: "35",
          gender1: "female",
          is_smoking1: false,
          date_of_birth1: "1988-07-07T16:22:22.000000Z",
          health_condition1: "excellent",
          tracks:
            '[{"desired_sum":"1000000","interest_type":"temporary","desired_period":"7","interest_rate":"0.001"},{"desired_sum":"1000000","interest_type":"permanent","desired_period":"8","interest_rate":"2"}]',
          total_loan: 2000000,
          user_id: null,
          ip: "**************",
          screen_width: "2034",
          screen_height: "532",
          sort_by_param: "price_asc",
          sort_by_period: "first_year_sum",
          area_id: "0",
          updated_at: "2023-07-03T16:22:22.000000Z",
          created_at: "2023-07-03T16:22:22.000000Z",
          id: 3966,
        },
        results_form_code: "s9879822",
        token: "w5zbyxj7d6",
        companies_offer: [
          "[84.6405,79.5799,70.5677,61.3765,51.6415,37.4115,24.8225,9.0151]",
        ],
        companies_offer_after_agent_discount: 42.32025,
        bid_price_total_years: 3788.79,
        companies_offer_for_all_years_arr: [
          {
            company_one_year_offer: 84.64,
            agent_discount_for_that_year: 50,
            agent_discount_for_that_year_in_money: 507.84,
            company_offer_after_agent_discount_for_month: 42.32,
            company_offer_after_agent_discount_for_year: 507.84,
            incremental_cost: 507.84,
            incremental_year: 1,
          },
          {
            company_one_year_offer: 79.58,
            agent_discount_for_that_year: 35,
            agent_discount_for_that_year_in_money: 334.24,
            company_offer_after_agent_discount_for_month: 51.73,
            company_offer_after_agent_discount_for_year: 620.72,
            incremental_cost: 1128.57,
            incremental_year: 2,
          },
          {
            company_one_year_offer: 70.57,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 127.02,
            company_offer_after_agent_discount_for_month: 59.98,
            company_offer_after_agent_discount_for_year: 719.79,
            incremental_cost: 1848.36,
            incremental_year: 3,
          },
          {
            company_one_year_offer: 61.38,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 110.48,
            company_offer_after_agent_discount_for_month: 52.17,
            company_offer_after_agent_discount_for_year: 626.04,
            incremental_cost: 2474.4,
            incremental_year: 4,
          },
          {
            company_one_year_offer: 51.64,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 92.95,
            company_offer_after_agent_discount_for_month: 43.9,
            company_offer_after_agent_discount_for_year: 526.74,
            incremental_cost: 3001.14,
            incremental_year: 5,
          },
          {
            company_one_year_offer: 37.41,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 67.34,
            company_offer_after_agent_discount_for_month: 31.8,
            company_offer_after_agent_discount_for_year: 381.6,
            incremental_cost: 3382.74,
            incremental_year: 6,
          },
          {
            company_one_year_offer: 24.82,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 24.82,
            company_offer_after_agent_discount_for_year: 297.87,
            incremental_cost: 3680.61,
            incremental_year: 7,
          },
          {
            company_one_year_offer: 9.02,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 9.02,
            company_offer_after_agent_discount_for_year: 108.18,
            incremental_cost: 3788.79,
            incremental_year: 8,
          },
        ],
        offer_price: 42.32,
        b_first_year: 42.32,
        featured_terms: [
          {
            id: 40,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 6,
            text: "<ul>\n<li>\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.</li>\n<li>\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.</li>\n<li>\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.</li></ul>",
            term: "\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.\n\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.\n\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.",
            number: null,
            featured: 1,
            short:
              "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05db\u05d9\u05e1\u05d5\u05d9 \u05d2\u05d5\u05d1\u05d4 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
            note: null,
            created_at: "2020-04-08 11:41:24",
            updated_at: "2023-03-15 17:44:06",
          },
          {
            id: 41,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 7,
            text: '<span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - </span><b style="font-size: 1rem;">78</b><br><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - </span><b style="font-size: 1rem;">74</b><br><span style="font-size: 1rem;">&nbsp;\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - </span><b style="font-size: 1rem;">81</b><br>',
            term: "<div>\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - <b>84</b></div><div>\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - 74&nbsp;</div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - 81</div>",
            number: null,
            featured: 1,
            short:
              "\u05de\u05d3\u05d3 \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 84",
            note: null,
            created_at: "2020-04-08 11:41:39",
            updated_at: "2023-06-13 11:35:56",
          },
        ],
        company_name: "\u05de\u05d2\u05d3\u05dc",
      },
      {
        id: 165,
        agent_id: 165,
        provider_id: 25,
        product_id: 6360,
        policy_id: 7,
        mortgage_insurance_request_id: 3966,
        about_us:
          '"\u05e8\u05d5\u05d6\u05df \u05e2\u05e0\u05e7 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7" (\u05d0\u05e8\u05d9\u05d4 \u05e8\u05d5\u05d6\u05df) \u05d4\u05d9\u05e0\u05d4 \u05de\u05e1\u05d5\u05db\u05e0\u05d5\u05d9\u05d5\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d2\u05d3\u05d5\u05dc\u05d5\u05ea \u05d1\u05d9\u05e9\u05e8\u05d0\u05dc! \u05de\u05d0\u05d6 1967 \u05d4\u05e1\u05d5\u05db\u05e0\u05d5\u05ea \u05e4\u05e2\u05d9\u05dc\u05d4 \u05d4\u05df \u05d1\u05e2\u05e0\u05e3 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d0\u05dc\u05de\u05e0\u05d8\u05e8\u05d9 \u05d5\u05d1\u05e2\u05e0\u05e3 \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d7\u05d9\u05d9\u05dd, \u05d4\u05d1\u05e8\u05d9\u05d0\u05d5\u05ea \u05d5\u05d4\u05e4\u05e0\u05e1\u05d9\u05d4. \u05e9\u05d9\u05e8\u05d5\u05ea \u05e8\u05d0\u05e9\u05d5\u05e0\u05d9 \u05d1\u05d4\u05d2\u05e9\u05ea \u05ea\u05d1\u05d9\u05e2\u05d4 \u05dc\u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7.',
        agency_name:
          "\u05e8\u05d5\u05d6\u05df \u05e2\u05e0\u05e7 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7",
        email: "<EMAIL>",
        phone: null,
        site_phone: "073-3745406",
        listing_image:
          "https://trusty.co.il/uploads/agents/listings/agent-listing6009a2eabbb2d.png",
        address_city: "\u05e0\u05d4\u05e8\u05d9\u05d4",
        company_img_name: null,
        avg_review: "8.67",
        total_reviews: 3,
        agent_areas: [1, 3, 6],
        total_price: null,
        average_month_price: 35.08,
        company_id: 1,
        discount_first_year: "50.0",
        discount_for_all_years: [
          "50.0",
          "35.0",
          "15.0",
          "15.0",
          "15.0",
          "15.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
        ],
        discount_for_rest_of_the_years: "50.0",
        sort_by_period: "first_year_sum",
        benefits: [],
        the_user_request_parameters: {
          agent_id: 25,
          provider_id: 25,
          number_of_insured: "individual",
          age1: "35",
          gender1: "female",
          is_smoking1: false,
          date_of_birth1: "1988-07-07T16:22:22.000000Z",
          health_condition1: "excellent",
          tracks:
            '[{"desired_sum":"1000000","interest_type":"temporary","desired_period":"7","interest_rate":"0.001"},{"desired_sum":"1000000","interest_type":"permanent","desired_period":"8","interest_rate":"2"}]',
          total_loan: 2000000,
          user_id: null,
          ip: "**************",
          screen_width: "2034",
          screen_height: "532",
          sort_by_param: "price_asc",
          sort_by_period: "first_year_sum",
          area_id: "0",
          updated_at: "2023-07-03T16:22:22.000000Z",
          created_at: "2023-07-03T16:22:22.000000Z",
          id: 3966,
        },
        results_form_code: "s9879822",
        token: "odtrysmcr0",
        companies_offer: [
          "[84.6405,79.5799,70.5677,61.3765,51.6415,37.4115,24.8225,9.0151]",
        ],
        companies_offer_after_agent_discount: 42.32025,
        bid_price_total_years: 3788.79,
        companies_offer_for_all_years_arr: [
          {
            company_one_year_offer: 84.64,
            agent_discount_for_that_year: 50,
            agent_discount_for_that_year_in_money: 507.84,
            company_offer_after_agent_discount_for_month: 42.32,
            company_offer_after_agent_discount_for_year: 507.84,
            incremental_cost: 507.84,
            incremental_year: 1,
          },
          {
            company_one_year_offer: 79.58,
            agent_discount_for_that_year: 35,
            agent_discount_for_that_year_in_money: 334.24,
            company_offer_after_agent_discount_for_month: 51.73,
            company_offer_after_agent_discount_for_year: 620.72,
            incremental_cost: 1128.57,
            incremental_year: 2,
          },
          {
            company_one_year_offer: 70.57,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 127.02,
            company_offer_after_agent_discount_for_month: 59.98,
            company_offer_after_agent_discount_for_year: 719.79,
            incremental_cost: 1848.36,
            incremental_year: 3,
          },
          {
            company_one_year_offer: 61.38,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 110.48,
            company_offer_after_agent_discount_for_month: 52.17,
            company_offer_after_agent_discount_for_year: 626.04,
            incremental_cost: 2474.4,
            incremental_year: 4,
          },
          {
            company_one_year_offer: 51.64,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 92.95,
            company_offer_after_agent_discount_for_month: 43.9,
            company_offer_after_agent_discount_for_year: 526.74,
            incremental_cost: 3001.14,
            incremental_year: 5,
          },
          {
            company_one_year_offer: 37.41,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 67.34,
            company_offer_after_agent_discount_for_month: 31.8,
            company_offer_after_agent_discount_for_year: 381.6,
            incremental_cost: 3382.74,
            incremental_year: 6,
          },
          {
            company_one_year_offer: 24.82,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 24.82,
            company_offer_after_agent_discount_for_year: 297.87,
            incremental_cost: 3680.61,
            incremental_year: 7,
          },
          {
            company_one_year_offer: 9.02,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 9.02,
            company_offer_after_agent_discount_for_year: 108.18,
            incremental_cost: 3788.79,
            incremental_year: 8,
          },
        ],
        offer_price: 42.32,
        b_first_year: 42.32,
        featured_terms: [
          {
            id: 40,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 6,
            text: "<ul>\n<li>\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.</li>\n<li>\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.</li>\n<li>\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.</li></ul>",
            term: "\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.\n\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.\n\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.",
            number: null,
            featured: 1,
            short:
              "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05db\u05d9\u05e1\u05d5\u05d9 \u05d2\u05d5\u05d1\u05d4 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
            note: null,
            created_at: "2020-04-08 11:41:24",
            updated_at: "2023-03-15 17:44:06",
          },
          {
            id: 41,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 7,
            text: '<span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - </span><b style="font-size: 1rem;">78</b><br><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - </span><b style="font-size: 1rem;">74</b><br><span style="font-size: 1rem;">&nbsp;\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - </span><b style="font-size: 1rem;">81</b><br>',
            term: "<div>\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - <b>84</b></div><div>\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - 74&nbsp;</div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - 81</div>",
            number: null,
            featured: 1,
            short:
              "\u05de\u05d3\u05d3 \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 84",
            note: null,
            created_at: "2020-04-08 11:41:39",
            updated_at: "2023-06-13 11:35:56",
          },
        ],
        company_name: "\u05de\u05d2\u05d3\u05dc",
      },
      {
        id: 189,
        agent_id: 189,
        provider_id: 25,
        product_id: 6401,
        policy_id: 7,
        mortgage_insurance_request_id: 3966,
        about_us:
          "\u05e8\u05d5\u05d6\u05d9\u05df \u05e1\u05d5\u05db\u05e0\u05d5\u05ea \u05dc\u05d1\u05d9\u05d8\u05d5\u05d7, \u05e2\u05d5\u05e1\u05e7\u05ea \u05d1\u05de\u05e8\u05d1\u05d9\u05ea \u05e1\u05d5\u05d2\u05d9 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05de\u05e9\u05e0\u05ea 1997. \u05de\u05e0\u05d4\u05dc \u05d4\u05d7\u05d1\u05e8\u05d4 \u05d4\u05d5\u05d0 \u05de\u05e8 \u05e8\u05d0\u05d5\u05d1\u05df \u05e8\u05d5\u05d6\u05d9\u05df, \u05e1\u05d5\u05db\u05df \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e2\u05dc \u05e0\u05e1\u05d9\u05d5\u05df \u05e9\u05dc 45 \u05e9\u05e0\u05d4 \u05d1\u05e2\u05e0\u05e3 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7, \u05d1\u05e2\u05dc \u05e8\u05d9\u05e9\u05d9\u05d5\u05df \u05e4\u05e0\u05e1\u05d9\u05d5\u05e0\u05d9 \u05d5\u05db\u05dc\u05dc\u05d9 \u05d5\u05d7\u05d1\u05e8 \u05d1\u05dc\u05e9\u05db\u05ea \u05e1\u05d5\u05db\u05e0\u05d9 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7.",
        agency_name:
          '\u05e8\u05d5\u05d6\u05d9\u05df \u05e1\u05d5\u05db\u05e0\u05d5\u05ea \u05dc\u05d1\u05d9\u05d8\u05d5\u05d7 (1997) \u05d1\u05e2"\u05de',
        email: "<EMAIL>",
        phone: null,
        site_phone: "073-3745431",
        listing_image:
          "https://trusty.co.il/uploads/agents/listings/agent-listing61c1ae36e8bf2.png",
        address_city: "\u05db\u05e4\u05e8 \u05e1\u05d1\u05d0",
        company_img_name: null,
        avg_review: "0.00",
        total_reviews: 0,
        agent_areas: [1, 2, 3, 4, 5, 6],
        total_price: null,
        average_month_price: 35.08,
        company_id: 1,
        discount_first_year: "50.0",
        discount_for_all_years: [
          "50.0",
          "35.0",
          "15.0",
          "15.0",
          "15.0",
          "15.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
        ],
        discount_for_rest_of_the_years: "0.0",
        sort_by_period: "first_year_sum",
        benefits: [
          {
            id: 215,
            agent_id: 189,
            name: "\u05d7\u05d5\u05d3\u05e9\u05d9\u05d9\u05dd \u05d7\u05d9\u05e0\u05dd",
            details:
              "\u05d7\u05d5\u05d3\u05e9\u05d9\u05d9\u05dd \u05e8\u05d0\u05e9\u05d5\u05e0\u05d9\u05dd \u05d7\u05d9\u05e0\u05dd",
            active: 1,
            created_at: "2021-12-30T08:46:24.000000Z",
            updated_at: "2021-12-30T08:46:24.000000Z",
            pivot: { agent_mortgage_bid_id: 6401, benefit_id: 215 },
          },
        ],
        the_user_request_parameters: {
          agent_id: 25,
          provider_id: 25,
          number_of_insured: "individual",
          age1: "35",
          gender1: "female",
          is_smoking1: false,
          date_of_birth1: "1988-07-07T16:22:22.000000Z",
          health_condition1: "excellent",
          tracks:
            '[{"desired_sum":"1000000","interest_type":"temporary","desired_period":"7","interest_rate":"0.001"},{"desired_sum":"1000000","interest_type":"permanent","desired_period":"8","interest_rate":"2"}]',
          total_loan: 2000000,
          user_id: null,
          ip: "**************",
          screen_width: "2034",
          screen_height: "532",
          sort_by_param: "price_asc",
          sort_by_period: "first_year_sum",
          area_id: "0",
          updated_at: "2023-07-03T16:22:22.000000Z",
          created_at: "2023-07-03T16:22:22.000000Z",
          id: 3966,
        },
        results_form_code: "s9879822",
        token: "ry63vdr93b",
        companies_offer: [
          "[84.6405,79.5799,70.5677,61.3765,51.6415,37.4115,24.8225,9.0151]",
        ],
        companies_offer_after_agent_discount: 42.32025,
        bid_price_total_years: 3788.79,
        companies_offer_for_all_years_arr: [
          {
            company_one_year_offer: 84.64,
            agent_discount_for_that_year: 50,
            agent_discount_for_that_year_in_money: 507.84,
            company_offer_after_agent_discount_for_month: 42.32,
            company_offer_after_agent_discount_for_year: 507.84,
            incremental_cost: 507.84,
            incremental_year: 1,
          },
          {
            company_one_year_offer: 79.58,
            agent_discount_for_that_year: 35,
            agent_discount_for_that_year_in_money: 334.24,
            company_offer_after_agent_discount_for_month: 51.73,
            company_offer_after_agent_discount_for_year: 620.72,
            incremental_cost: 1128.57,
            incremental_year: 2,
          },
          {
            company_one_year_offer: 70.57,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 127.02,
            company_offer_after_agent_discount_for_month: 59.98,
            company_offer_after_agent_discount_for_year: 719.79,
            incremental_cost: 1848.36,
            incremental_year: 3,
          },
          {
            company_one_year_offer: 61.38,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 110.48,
            company_offer_after_agent_discount_for_month: 52.17,
            company_offer_after_agent_discount_for_year: 626.04,
            incremental_cost: 2474.4,
            incremental_year: 4,
          },
          {
            company_one_year_offer: 51.64,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 92.95,
            company_offer_after_agent_discount_for_month: 43.9,
            company_offer_after_agent_discount_for_year: 526.74,
            incremental_cost: 3001.14,
            incremental_year: 5,
          },
          {
            company_one_year_offer: 37.41,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 67.34,
            company_offer_after_agent_discount_for_month: 31.8,
            company_offer_after_agent_discount_for_year: 381.6,
            incremental_cost: 3382.74,
            incremental_year: 6,
          },
          {
            company_one_year_offer: 24.82,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 24.82,
            company_offer_after_agent_discount_for_year: 297.87,
            incremental_cost: 3680.61,
            incremental_year: 7,
          },
          {
            company_one_year_offer: 9.02,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 9.02,
            company_offer_after_agent_discount_for_year: 108.18,
            incremental_cost: 3788.79,
            incremental_year: 8,
          },
        ],
        offer_price: 42.32,
        b_first_year: 42.32,
        featured_terms: [
          {
            id: 40,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 6,
            text: "<ul>\n<li>\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.</li>\n<li>\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.</li>\n<li>\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.</li></ul>",
            term: "\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.\n\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.\n\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.",
            number: null,
            featured: 1,
            short:
              "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05db\u05d9\u05e1\u05d5\u05d9 \u05d2\u05d5\u05d1\u05d4 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
            note: null,
            created_at: "2020-04-08 11:41:24",
            updated_at: "2023-03-15 17:44:06",
          },
          {
            id: 41,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 7,
            text: '<span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - </span><b style="font-size: 1rem;">78</b><br><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - </span><b style="font-size: 1rem;">74</b><br><span style="font-size: 1rem;">&nbsp;\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - </span><b style="font-size: 1rem;">81</b><br>',
            term: "<div>\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - <b>84</b></div><div>\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - 74&nbsp;</div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - 81</div>",
            number: null,
            featured: 1,
            short:
              "\u05de\u05d3\u05d3 \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 84",
            note: null,
            created_at: "2020-04-08 11:41:39",
            updated_at: "2023-06-13 11:35:56",
          },
        ],
        company_name: "\u05de\u05d2\u05d3\u05dc",
      },
      {
        id: 49,
        agent_id: 49,
        provider_id: 25,
        product_id: 2250,
        policy_id: 7,
        mortgage_insurance_request_id: 3966,
        about_us:
          '\u05de\u05d9\u05dc\u05e8 \u05e1\u05d5\u05db\u05e0\u05d5\u05ea \u05dc\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d4 \u05e1\u05d5\u05db\u05e0\u05d5\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d5\u05ea\u05d9\u05e7\u05d4 \u05d4\u05e0\u05d5\u05ea\u05e0\u05ea \u05e4\u05ea\u05e8\u05d5\u05e0\u05d5\u05ea \u05d1\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e8\u05d9\u05d0\u05d5\u05ea , \u05d1\u05d9\u05d8\u05d5\u05d7 \u05e0\u05e1\u05d9\u05e2\u05d5\u05ea \u05dc\u05d7\u05d5"\u05dc, \u05d1\u05d9\u05d8\u05d5\u05d7 \u05ea\u05d9\u05d9\u05e8\u05d9\u05dd \u05d5\u05e2\u05d5\u05d1\u05d3\u05d9\u05dd \u05d6\u05e8\u05d9\u05dd, \u05ea\u05d0\u05d5\u05e0\u05d5\u05ea , \u05d7\u05d9\u05d9\u05dd , \u05e4\u05e0\u05e1\u05d9\u05d4, \u05d2\u05de\u05dc, \u05d3\u05d9\u05e8\u05d4, \u05e8\u05db\u05d1 , \u05e2\u05e1\u05e7 .',
        agency_name:
          '\u05de\u05d9\u05dc\u05e8 \u05e1\u05d5\u05db\u05e0\u05d5\u05ea \u05dc\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e2"\u05de',
        email: "<EMAIL>",
        phone: "*********",
        site_phone: "073-3745434",
        listing_image:
          "https://trusty.co.il/uploads/agents/listings/agent-listing61ceefa3ea6cd.png",
        address_city: "\u05e8\u05de\u05ea \u05d2\u05df",
        company_img_name: null,
        avg_review: "0.00",
        total_reviews: 0,
        agent_areas: [1, 2, 3, 4, 5, 6],
        total_price: null,
        average_month_price: 35.08,
        company_id: 1,
        discount_first_year: "50.0",
        discount_for_all_years: [
          "50.0",
          "35.0",
          "15.0",
          "15.0",
          "15.0",
          "15.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
        ],
        discount_for_rest_of_the_years: "0.0",
        sort_by_period: "first_year_sum",
        benefits: [
          {
            id: 22,
            agent_id: 49,
            name: "\u05e9\u05d9\u05d7\u05ea \u05d9\u05e2\u05d5\u05e5 \u05d7\u05d9\u05e0\u05dd",
            details:
              "\u05d1\u05d3\u05d9\u05e7\u05ea \u05ea\u05d9\u05e7 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05d9\u05e0\u05dd",
            active: 1,
            created_at: "2019-09-30T12:29:14.000000Z",
            updated_at: "2020-12-30T14:31:57.000000Z",
            pivot: { agent_mortgage_bid_id: 2250, benefit_id: 22 },
          },
        ],
        the_user_request_parameters: {
          agent_id: 25,
          provider_id: 25,
          number_of_insured: "individual",
          age1: "35",
          gender1: "female",
          is_smoking1: false,
          date_of_birth1: "1988-07-07T16:22:22.000000Z",
          health_condition1: "excellent",
          tracks:
            '[{"desired_sum":"1000000","interest_type":"temporary","desired_period":"7","interest_rate":"0.001"},{"desired_sum":"1000000","interest_type":"permanent","desired_period":"8","interest_rate":"2"}]',
          total_loan: 2000000,
          user_id: null,
          ip: "**************",
          screen_width: "2034",
          screen_height: "532",
          sort_by_param: "price_asc",
          sort_by_period: "first_year_sum",
          area_id: "0",
          updated_at: "2023-07-03T16:22:22.000000Z",
          created_at: "2023-07-03T16:22:22.000000Z",
          id: 3966,
        },
        results_form_code: "s9879822",
        token: "wubddgqhny",
        companies_offer: [
          "[84.6405,79.5799,70.5677,61.3765,51.6415,37.4115,24.8225,9.0151]",
        ],
        companies_offer_after_agent_discount: 42.32025,
        bid_price_total_years: 3788.79,
        companies_offer_for_all_years_arr: [
          {
            company_one_year_offer: 84.64,
            agent_discount_for_that_year: 50,
            agent_discount_for_that_year_in_money: 507.84,
            company_offer_after_agent_discount_for_month: 42.32,
            company_offer_after_agent_discount_for_year: 507.84,
            incremental_cost: 507.84,
            incremental_year: 1,
          },
          {
            company_one_year_offer: 79.58,
            agent_discount_for_that_year: 35,
            agent_discount_for_that_year_in_money: 334.24,
            company_offer_after_agent_discount_for_month: 51.73,
            company_offer_after_agent_discount_for_year: 620.72,
            incremental_cost: 1128.57,
            incremental_year: 2,
          },
          {
            company_one_year_offer: 70.57,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 127.02,
            company_offer_after_agent_discount_for_month: 59.98,
            company_offer_after_agent_discount_for_year: 719.79,
            incremental_cost: 1848.36,
            incremental_year: 3,
          },
          {
            company_one_year_offer: 61.38,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 110.48,
            company_offer_after_agent_discount_for_month: 52.17,
            company_offer_after_agent_discount_for_year: 626.04,
            incremental_cost: 2474.4,
            incremental_year: 4,
          },
          {
            company_one_year_offer: 51.64,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 92.95,
            company_offer_after_agent_discount_for_month: 43.9,
            company_offer_after_agent_discount_for_year: 526.74,
            incremental_cost: 3001.14,
            incremental_year: 5,
          },
          {
            company_one_year_offer: 37.41,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 67.34,
            company_offer_after_agent_discount_for_month: 31.8,
            company_offer_after_agent_discount_for_year: 381.6,
            incremental_cost: 3382.74,
            incremental_year: 6,
          },
          {
            company_one_year_offer: 24.82,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 24.82,
            company_offer_after_agent_discount_for_year: 297.87,
            incremental_cost: 3680.61,
            incremental_year: 7,
          },
          {
            company_one_year_offer: 9.02,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 9.02,
            company_offer_after_agent_discount_for_year: 108.18,
            incremental_cost: 3788.79,
            incremental_year: 8,
          },
        ],
        offer_price: 42.32,
        b_first_year: 42.32,
        featured_terms: [
          {
            id: 40,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 6,
            text: "<ul>\n<li>\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.</li>\n<li>\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.</li>\n<li>\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.</li></ul>",
            term: "\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.\n\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.\n\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.",
            number: null,
            featured: 1,
            short:
              "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05db\u05d9\u05e1\u05d5\u05d9 \u05d2\u05d5\u05d1\u05d4 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
            note: null,
            created_at: "2020-04-08 11:41:24",
            updated_at: "2023-03-15 17:44:06",
          },
          {
            id: 41,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 7,
            text: '<span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - </span><b style="font-size: 1rem;">78</b><br><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - </span><b style="font-size: 1rem;">74</b><br><span style="font-size: 1rem;">&nbsp;\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - </span><b style="font-size: 1rem;">81</b><br>',
            term: "<div>\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - <b>84</b></div><div>\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - 74&nbsp;</div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - 81</div>",
            number: null,
            featured: 1,
            short:
              "\u05de\u05d3\u05d3 \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 84",
            note: null,
            created_at: "2020-04-08 11:41:39",
            updated_at: "2023-06-13 11:35:56",
          },
        ],
        company_name: "\u05de\u05d2\u05d3\u05dc",
      },
      {
        id: 216,
        agent_id: 216,
        provider_id: 25,
        product_id: 6483,
        policy_id: 7,
        mortgage_insurance_request_id: 3966,
        about_us:
          "\u05e1\u05d5\u05db\u05e0\u05d5\u05ea \u05d1\u05d5\u05d8\u05d9\u05e7 \u05d0\u05dd \u05d9\u05d7\u05e1 \u05d0\u05d9\u05e9\u05d9 \u05dc\u05db\u05dc \u05dc\u05e7\u05d5\u05d7 , \u05e4\u05d5\u05e2\u05dc\u05d9\u05dd \u05d1\u05e9\u05d5\u05e7 \u05de\u05e2\u05dc 30 \u05e9\u05e0\u05d9\u05dd , \u05dc\u05e4\u05d9 \u05d4\u05e1\u05d8\u05d8\u05d9\u05e1\u05d8\u05d9\u05e7\u05d4 \u05dc\u05e7\u05d5\u05d7 \u05e9\u05de\u05d2\u05d9\u05e9 \u05d3\u05e8\u05db\u05d9\u05e0\u05d5 \u05ea\u05d1\u05d9\u05e2\u05d4 \u05de\u05e7\u05d1\u05dc \u05db\u05e1\u05e3 \u05d1 98.3% \u05de\u05d4\u05de\u05e7\u05e8\u05d9\u05dd",
        agency_name: "\u05e7\u05e8\u05d3\u05d5 \u05d1\u05d9\u05d8\u05d5\u05d7",
        email: "<EMAIL>",
        phone: null,
        site_phone: "073-3745459",
        listing_image:
          "https://trusty.co.il/uploads/agents/listings/agent-listing63ea846243d5a.png",
        address_city: "\u05d8\u05d1\u05e8\u05d9\u05d4",
        company_img_name: null,
        avg_review: "0.00",
        total_reviews: 0,
        agent_areas: [1],
        total_price: null,
        average_month_price: 35.21,
        company_id: 5,
        discount_first_year: "60.0",
        discount_for_all_years: [
          "60.0",
          "60.0",
          "40.0",
          "40.0",
          "40.0",
          "40.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
        ],
        discount_for_rest_of_the_years: "0.0",
        sort_by_period: "first_year_sum",
        benefits: [],
        the_user_request_parameters: {
          agent_id: 25,
          provider_id: 25,
          number_of_insured: "individual",
          age1: "35",
          gender1: "female",
          is_smoking1: false,
          date_of_birth1: "1988-07-07T16:22:22.000000Z",
          health_condition1: "excellent",
          tracks:
            '[{"desired_sum":"1000000","interest_type":"temporary","desired_period":"7","interest_rate":"0.001"},{"desired_sum":"1000000","interest_type":"permanent","desired_period":"8","interest_rate":"2"}]',
          total_loan: 2000000,
          user_id: null,
          ip: "**************",
          screen_width: "2034",
          screen_height: "532",
          sort_by_param: "price_asc",
          sort_by_period: "first_year_sum",
          area_id: "0",
          updated_at: "2023-07-03T16:22:22.000000Z",
          created_at: "2023-07-03T16:22:22.000000Z",
          id: 3966,
        },
        results_form_code: "s9879822",
        token: "ptifq2mvv2",
        companies_offer: [
          "[120,108.5367,96.8673,83.355,69.1336,52.3228,33.3666,11.1004]",
        ],
        companies_offer_after_agent_discount: 48,
        bid_price_total_years: 3802.67,
        companies_offer_for_all_years_arr: [
          {
            company_one_year_offer: 120,
            agent_discount_for_that_year: 60,
            agent_discount_for_that_year_in_money: 864,
            company_offer_after_agent_discount_for_month: 48,
            company_offer_after_agent_discount_for_year: 576,
            incremental_cost: 576,
            incremental_year: 1,
          },
          {
            company_one_year_offer: 108.54,
            agent_discount_for_that_year: 60,
            agent_discount_for_that_year_in_money: 781.46,
            company_offer_after_agent_discount_for_month: 43.41,
            company_offer_after_agent_discount_for_year: 520.98,
            incremental_cost: 1096.98,
            incremental_year: 2,
          },
          {
            company_one_year_offer: 96.87,
            agent_discount_for_that_year: 40,
            agent_discount_for_that_year_in_money: 464.96,
            company_offer_after_agent_discount_for_month: 58.12,
            company_offer_after_agent_discount_for_year: 697.44,
            incremental_cost: 1794.42,
            incremental_year: 3,
          },
          {
            company_one_year_offer: 83.36,
            agent_discount_for_that_year: 40,
            agent_discount_for_that_year_in_money: 400.1,
            company_offer_after_agent_discount_for_month: 50.01,
            company_offer_after_agent_discount_for_year: 600.16,
            incremental_cost: 2394.58,
            incremental_year: 4,
          },
          {
            company_one_year_offer: 69.13,
            agent_discount_for_that_year: 40,
            agent_discount_for_that_year_in_money: 331.84,
            company_offer_after_agent_discount_for_month: 41.48,
            company_offer_after_agent_discount_for_year: 497.76,
            incremental_cost: 2892.34,
            incremental_year: 5,
          },
          {
            company_one_year_offer: 52.32,
            agent_discount_for_that_year: 40,
            agent_discount_for_that_year_in_money: 251.15,
            company_offer_after_agent_discount_for_month: 31.39,
            company_offer_after_agent_discount_for_year: 376.72,
            incremental_cost: 3269.06,
            incremental_year: 6,
          },
          {
            company_one_year_offer: 33.37,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 33.37,
            company_offer_after_agent_discount_for_year: 400.4,
            incremental_cost: 3669.46,
            incremental_year: 7,
          },
          {
            company_one_year_offer: 11.1,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 11.1,
            company_offer_after_agent_discount_for_year: 133.2,
            incremental_cost: 3802.67,
            incremental_year: 8,
          },
        ],
        offer_price: 48,
        b_first_year: 48,
        featured_terms: [],
        company_name: "\u05db\u05dc\u05dc",
      },
      {
        id: 191,
        agent_id: 191,
        provider_id: 25,
        product_id: 6412,
        policy_id: 7,
        mortgage_insurance_request_id: 3966,
        about_us:
          "\u05d1\u05e8\u05d8\u05d4 \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d5\u05e4\u05d9\u05e0\u05e0\u05e1\u05d9\u05dd \u05de\u05e0\u05d4\u05d5\u05dc\u05ea \u05e2''\u05d9 \u05d1\u05e8\u05d8\u05d4 \u05e8\u05e9\u05ea\u05d9 \u05e1\u05d5\u05db\u05e0\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05e2\u05dd \u05e0\u05d9\u05e1\u05d9\u05d5\u05df \u05e9\u05dc \u05dc\u05de\u05e2\u05dc\u05d4 \u05de\u05e2\u05e9\u05d5\u05ea \u05d1\u05ea\u05d7\u05d5\u05dd \u05d5\u05d0\u05dc\u05e4\u05d9 \u05dc\u05e7\u05d5\u05d7\u05d5\u05ea \u05d1\"\u05d1\u05e8\u05d8\u05d4 \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d5\u05e4\u05d9\u05e0\u05e0\u05e1\u05d9\u05dd\" \u05d0\u05ea\u05dd \u05d9\u05db\u05d5\u05dc\u05d9\u05dd \u05dc\u05e7\u05d1\u05dc \u05de\u05d2\u05d5\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea\u05d9 \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d5\u05d1\u05d4\u05dd: \u05d1\u05d9\u05d8\u05d5\u05d7\u05d9 \u05d7\u05d9\u05d9\u05dd, \u05d1\u05e8\u05d9\u05d0\u05d5\u05ea, \u05d5\u05e2\u05d5\u05d3.",
        agency_name:
          "\u05d1\u05e8\u05d8\u05d4 \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d5\u05e4\u05d9\u05e0\u05e0\u05e1\u05d9\u05dd",
        email: "<EMAIL>",
        phone: null,
        site_phone: "073-3745436",
        listing_image:
          "https://trusty.co.il/uploads/agents/listings/agent-listing61d5e66e15ab9.png",
        address_city: "\u05e8\u05de\u05ea \u05d2\u05df",
        company_img_name: null,
        avg_review: "0.00",
        total_reviews: 0,
        agent_areas: [1, 2, 3, 4, 5, 6],
        total_price: null,
        average_month_price: 47.15,
        company_id: 6,
        discount_first_year: "47.0",
        discount_for_all_years: [
          "47.0",
          "40.0",
          "30.0",
          "20.0",
          "15.0",
          "15.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
        ],
        discount_for_rest_of_the_years: "0.0",
        sort_by_period: "first_year_sum",
        benefits: [
          {
            id: 217,
            agent_id: 191,
            name: "\u05e9\u05d9\u05d7\u05ea \u05d9\u05e2\u05d5\u05e5 \u05d7\u05d9\u05e0\u05dd",
            details:
              "\u05e9\u05d9\u05d7\u05ea \u05d9\u05e2\u05d5\u05e5 \u05d8\u05dc\u05e4\u05d5\u05e0\u05d9\u05ea \u05d7\u05d9\u05e0\u05dd!",
            active: 1,
            created_at: "2022-01-05T17:54:54.000000Z",
            updated_at: "2022-01-05T17:54:54.000000Z",
            pivot: { agent_mortgage_bid_id: 6412, benefit_id: 217 },
          },
        ],
        the_user_request_parameters: {
          agent_id: 25,
          provider_id: 25,
          number_of_insured: "individual",
          age1: "35",
          gender1: "female",
          is_smoking1: false,
          date_of_birth1: "1988-07-07T16:22:22.000000Z",
          health_condition1: "excellent",
          tracks:
            '[{"desired_sum":"1000000","interest_type":"temporary","desired_period":"7","interest_rate":"0.001"},{"desired_sum":"1000000","interest_type":"permanent","desired_period":"8","interest_rate":"2"}]',
          total_loan: 2000000,
          user_id: null,
          ip: "**************",
          screen_width: "2034",
          screen_height: "532",
          sort_by_param: "price_asc",
          sort_by_period: "first_year_sum",
          area_id: "0",
          updated_at: "2023-07-03T16:22:22.000000Z",
          created_at: "2023-07-03T16:22:22.000000Z",
          id: 3966,
        },
        results_form_code: "s9879822",
        token: "xyyoool02a",
        companies_offer: [
          "[122.8069,111.8234,99.1919,87.5525,70.9755,54.1864,34.8184,11.5024]",
        ],
        companies_offer_after_agent_discount: 65.087657,
        bid_price_total_years: 5092.4,
        companies_offer_for_all_years_arr: [
          {
            company_one_year_offer: 122.81,
            agent_discount_for_that_year: 47,
            agent_discount_for_that_year_in_money: 692.63,
            company_offer_after_agent_discount_for_month: 65.09,
            company_offer_after_agent_discount_for_year: 781.05,
            incremental_cost: 781.05,
            incremental_year: 1,
          },
          {
            company_one_year_offer: 111.82,
            agent_discount_for_that_year: 40,
            agent_discount_for_that_year_in_money: 536.75,
            company_offer_after_agent_discount_for_month: 67.09,
            company_offer_after_agent_discount_for_year: 805.13,
            incremental_cost: 1586.18,
            incremental_year: 2,
          },
          {
            company_one_year_offer: 99.19,
            agent_discount_for_that_year: 30,
            agent_discount_for_that_year_in_money: 357.09,
            company_offer_after_agent_discount_for_month: 69.43,
            company_offer_after_agent_discount_for_year: 833.21,
            incremental_cost: 2419.39,
            incremental_year: 3,
          },
          {
            company_one_year_offer: 87.55,
            agent_discount_for_that_year: 20,
            agent_discount_for_that_year_in_money: 210.13,
            company_offer_after_agent_discount_for_month: 70.04,
            company_offer_after_agent_discount_for_year: 840.5,
            incremental_cost: 3259.9,
            incremental_year: 4,
          },
          {
            company_one_year_offer: 70.98,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 127.76,
            company_offer_after_agent_discount_for_month: 60.33,
            company_offer_after_agent_discount_for_year: 723.95,
            incremental_cost: 3983.85,
            incremental_year: 5,
          },
          {
            company_one_year_offer: 54.19,
            agent_discount_for_that_year: 15,
            agent_discount_for_that_year_in_money: 97.54,
            company_offer_after_agent_discount_for_month: 46.06,
            company_offer_after_agent_discount_for_year: 552.7,
            incremental_cost: 4536.55,
            incremental_year: 6,
          },
          {
            company_one_year_offer: 34.82,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 34.82,
            company_offer_after_agent_discount_for_year: 417.82,
            incremental_cost: 4954.37,
            incremental_year: 7,
          },
          {
            company_one_year_offer: 11.5,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 11.5,
            company_offer_after_agent_discount_for_year: 138.03,
            incremental_cost: 5092.4,
            incremental_year: 8,
          },
        ],
        offer_price: 65.09,
        b_first_year: 65.09,
        featured_terms: [],
        company_name: "\u05de\u05e0\u05d5\u05e8\u05d4",
      },
      {
        id: 44,
        agent_id: 44,
        provider_id: 25,
        product_id: 3132,
        policy_id: 7,
        mortgage_insurance_request_id: 3966,
        about_us:
          "\u05e1\u05d5\u05db\u05e0\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05d2\u05d9\u05e9\u05d4 \u05d0\u05d9\u05e9\u05d9\u05ea \u05d4\u05e0\u05d5\u05ea\u05e0\u05ea \u05de\u05e2\u05e0\u05d4 \u05dc\u05db\u05dc \u05e6\u05d5\u05e8\u05db\u05d9 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05e7\u05d9\u05d9\u05de\u05d9\u05dd,\u05de\u05ea\u05db\u05e0\u05e0\u05ea \u05e4\u05d9\u05e0\u05e0\u05e1\u05d9\u05ea. \r\n\u05de\u05e2\u05e0\u05d9\u05e7\u05d4 \u05d7\u05d1\u05d9\u05dc\u05ea \u05e9\u05d9\u05e8\u05d5\u05ea\u05d9\u05dd \u05de\u05e7\u05d9\u05e4\u05d4 \u05d1\u05db\u05dc \u05d4\u05e7\u05e9\u05d5\u05e8 \u05d1\u05e0\u05d9\u05d4\u05d5\u05dc \u05e4\u05d9\u05e0\u05e0\u05e1\u05d9 \u05e0\u05db\u05d5\u05df. \r\n\u05db\u05dc\u05db\u05dc\u05ea \u05d4\u05de\u05e9\u05e4\u05d7\u05d4,\u05d9\u05e2\u05d5\u05e5 \u05de\u05e9\u05db\u05e0\u05ea\u05d0\u05d5\u05ea,\u05ea\u05db\u05e0\u05d5\u05df \u05e4\u05e8\u05d9\u05e9\u05d4 .",
        agency_name:
          "\u05de\u05d9\u05dc\u05de\u05df \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d5\u05ea\u05db\u05e0\u05d5\u05df \u05e4\u05d9\u05e0\u05e0\u05e1\u05d9",
        email: "<EMAIL>",
        phone: "054-8141972",
        site_phone: "073-3745402",
        listing_image:
          "https://trusty.co.il/uploads/agents/listings/agent-listing60093d815ceec.png",
        address_city: "\u05d2\u05df \u05d9\u05d1\u05e0\u05d4",
        company_img_name: null,
        avg_review: "0.00",
        total_reviews: 0,
        agent_areas: [2, 3, 4, 5, 6],
        total_price: null,
        average_month_price: 38,
        company_id: 1,
        discount_first_year: "20.0",
        discount_for_all_years: [
          "20.0",
          "20.0",
          "20.0",
          "20.0",
          "20.0",
          "20.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
        ],
        discount_for_rest_of_the_years: "0.0",
        sort_by_period: "first_year_sum",
        benefits: [
          {
            id: 19,
            agent_id: 44,
            name: "\u05e9\u05d9\u05d7\u05ea \u05d9\u05e2\u05d5\u05e5 \u05d7\u05d9\u05e0\u05dd",
            details:
              "\u05d1\u05d3\u05d9\u05e7\u05ea \u05ea\u05d9\u05e7 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05d9\u05e0\u05dd",
            active: 1,
            created_at: "2019-09-30T12:29:14.000000Z",
            updated_at: "2020-12-30T14:31:57.000000Z",
            pivot: { agent_mortgage_bid_id: 3132, benefit_id: 19 },
          },
        ],
        the_user_request_parameters: {
          agent_id: 25,
          provider_id: 25,
          number_of_insured: "individual",
          age1: "35",
          gender1: "female",
          is_smoking1: false,
          date_of_birth1: "1988-07-07T16:22:22.000000Z",
          health_condition1: "excellent",
          tracks:
            '[{"desired_sum":"1000000","interest_type":"temporary","desired_period":"7","interest_rate":"0.001"},{"desired_sum":"1000000","interest_type":"permanent","desired_period":"8","interest_rate":"2"}]',
          total_loan: 2000000,
          user_id: null,
          ip: "**************",
          screen_width: "2034",
          screen_height: "532",
          sort_by_param: "price_asc",
          sort_by_period: "first_year_sum",
          area_id: "0",
          updated_at: "2023-07-03T16:22:22.000000Z",
          created_at: "2023-07-03T16:22:22.000000Z",
          id: 3966,
        },
        results_form_code: "s9879822",
        token: "uvxwc6uzok",
        companies_offer: [
          "[84.6405,79.5799,70.5677,61.3765,51.6415,37.4115,24.8225,9.0151]",
        ],
        companies_offer_after_agent_discount: 67.7124,
        bid_price_total_years: 4104.14,
        companies_offer_for_all_years_arr: [
          {
            company_one_year_offer: 84.64,
            agent_discount_for_that_year: 20,
            agent_discount_for_that_year_in_money: 203.14,
            company_offer_after_agent_discount_for_month: 67.71,
            company_offer_after_agent_discount_for_year: 812.55,
            incremental_cost: 812.55,
            incremental_year: 1,
          },
          {
            company_one_year_offer: 79.58,
            agent_discount_for_that_year: 20,
            agent_discount_for_that_year_in_money: 190.99,
            company_offer_after_agent_discount_for_month: 63.66,
            company_offer_after_agent_discount_for_year: 763.97,
            incremental_cost: 1576.52,
            incremental_year: 2,
          },
          {
            company_one_year_offer: 70.57,
            agent_discount_for_that_year: 20,
            agent_discount_for_that_year_in_money: 169.36,
            company_offer_after_agent_discount_for_month: 56.45,
            company_offer_after_agent_discount_for_year: 677.45,
            incremental_cost: 2253.97,
            incremental_year: 3,
          },
          {
            company_one_year_offer: 61.38,
            agent_discount_for_that_year: 20,
            agent_discount_for_that_year_in_money: 147.3,
            company_offer_after_agent_discount_for_month: 49.1,
            company_offer_after_agent_discount_for_year: 589.21,
            incremental_cost: 2843.18,
            incremental_year: 4,
          },
          {
            company_one_year_offer: 51.64,
            agent_discount_for_that_year: 20,
            agent_discount_for_that_year_in_money: 123.94,
            company_offer_after_agent_discount_for_month: 41.31,
            company_offer_after_agent_discount_for_year: 495.76,
            incremental_cost: 3338.94,
            incremental_year: 5,
          },
          {
            company_one_year_offer: 37.41,
            agent_discount_for_that_year: 20,
            agent_discount_for_that_year_in_money: 89.79,
            company_offer_after_agent_discount_for_month: 29.93,
            company_offer_after_agent_discount_for_year: 359.15,
            incremental_cost: 3698.09,
            incremental_year: 6,
          },
          {
            company_one_year_offer: 24.82,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 24.82,
            company_offer_after_agent_discount_for_year: 297.87,
            incremental_cost: 3995.96,
            incremental_year: 7,
          },
          {
            company_one_year_offer: 9.02,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 9.02,
            company_offer_after_agent_discount_for_year: 108.18,
            incremental_cost: 4104.14,
            incremental_year: 8,
          },
        ],
        offer_price: 67.71,
        b_first_year: 67.71,
        featured_terms: [
          {
            id: 40,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 6,
            text: "<ul>\n<li>\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.</li>\n<li>\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.</li>\n<li>\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.</li></ul>",
            term: "\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.\n\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.\n\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.",
            number: null,
            featured: 1,
            short:
              "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05db\u05d9\u05e1\u05d5\u05d9 \u05d2\u05d5\u05d1\u05d4 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
            note: null,
            created_at: "2020-04-08 11:41:24",
            updated_at: "2023-03-15 17:44:06",
          },
          {
            id: 41,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 7,
            text: '<span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - </span><b style="font-size: 1rem;">78</b><br><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - </span><b style="font-size: 1rem;">74</b><br><span style="font-size: 1rem;">&nbsp;\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - </span><b style="font-size: 1rem;">81</b><br>',
            term: "<div>\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - <b>84</b></div><div>\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - 74&nbsp;</div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - 81</div>",
            number: null,
            featured: 1,
            short:
              "\u05de\u05d3\u05d3 \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 84",
            note: null,
            created_at: "2020-04-08 11:41:39",
            updated_at: "2023-06-13 11:35:56",
          },
        ],
        company_name: "\u05de\u05d2\u05d3\u05dc",
      },
      {
        id: 28,
        agent_id: 28,
        provider_id: 25,
        product_id: 5198,
        policy_id: 7,
        mortgage_insurance_request_id: 3966,
        about_us:
          "\u05de\u05d7\u05e4\u05e9 \u05d0\u05d7\u05e8 \u05e4\u05ea\u05e8\u05d5\u05e0\u05d5\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d5\u05e4\u05d9\u05e0\u05e0\u05e1\u05d9\u05dd \u05d1\u05d4\u05ea\u05d0\u05de\u05d4 \u05d0\u05d9\u05e9\u05d9\u05ea? \u05e8\u05d5\u05e6\u05d9\u05dd \u05dc\u05d9\u05d4\u05e0\u05d5\u05ea \u05de\u05de\u05d2\u05d5\u05d5\u05df \u05e8\u05d7\u05d1 \u05e9\u05dc \u05e9\u05d9\u05e8\u05d5\u05ea\u05d9\u05dd \u05e4\u05d9\u05e0\u05e0\u05e1\u05d9\u05d9\u05dd \u05d5\u05de\u05d5\u05e6\u05e8\u05d9\u05dd \u05d1\u05d9\u05d8\u05d5\u05d7\u05d9\u05d9\u05dd \u05ea\u05d7\u05ea \u05e7\u05d5\u05e8\u05ea \u05d2\u05d2 \u05d0\u05d7\u05ea?\r\n\u05d1\u05e1\u05d5\u05db\u05e0\u05d5\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7-\u05d9\u05e8\u05d5\u05df \u05d0\u05d4\u05e8\u05d5\u05e0\u05d9, \u05d4\u05db\u05e1\u05e3 \u05e9\u05dc\u05db\u05dd \u05e9\u05d5\u05d5\u05d4 \u05dc\u05db\u05dd \u05d9\u05d5\u05ea\u05e8, \u05d4\u05e8\u05d1\u05d4 \u05d9\u05d5\u05ea\u05e8!",
        agency_name:
          "\u05d9\u05e8\u05d5\u05df \u05d0\u05d4\u05e8\u05d5\u05e0\u05d9 \u05e1\u05d5\u05db\u05e0\u05d5\u05ea \u05dc\u05d1\u05d9\u05d8\u05d5\u05d7 \u05ea\u05db\u05e0\u05d5\u05df \u05e4\u05d9\u05e0\u05e0\u05e1\u05d9 \u05d5\u05e4\u05e8\u05d9\u05e9\u05d4",
        email: "<EMAIL>",
        phone: "052-3136700",
        site_phone: "073-3745410",
        listing_image:
          "https://trusty.co.il/uploads/agents/listings/agent-listing6000b1137e090.png",
        address_city: "\u05e4\u05ea\u05d7 \u05ea\u05e7\u05d5\u05d5\u05d4",
        company_img_name: null,
        avg_review: "10.00",
        total_reviews: 1,
        agent_areas: [3],
        total_price: null,
        average_month_price: 38,
        company_id: 1,
        discount_first_year: "20.0",
        discount_for_all_years: [
          "20.0",
          "20.0",
          "20.0",
          "20.0",
          "20.0",
          "20.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
        ],
        discount_for_rest_of_the_years: "0.0",
        sort_by_period: "first_year_sum",
        benefits: [
          {
            id: 8,
            agent_id: 28,
            name: "\u05e9\u05d9\u05d7\u05ea \u05d9\u05e2\u05d5\u05e5 \u05d7\u05d9\u05e0\u05dd",
            details:
              "\u05d1\u05d3\u05d9\u05e7\u05ea \u05ea\u05d9\u05e7 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05d9\u05e0\u05dd",
            active: 1,
            created_at: "2019-09-30T12:29:14.000000Z",
            updated_at: "2020-12-30T14:31:57.000000Z",
            pivot: { agent_mortgage_bid_id: 5198, benefit_id: 8 },
          },
        ],
        the_user_request_parameters: {
          agent_id: 25,
          provider_id: 25,
          number_of_insured: "individual",
          age1: "35",
          gender1: "female",
          is_smoking1: false,
          date_of_birth1: "1988-07-07T16:22:22.000000Z",
          health_condition1: "excellent",
          tracks:
            '[{"desired_sum":"1000000","interest_type":"temporary","desired_period":"7","interest_rate":"0.001"},{"desired_sum":"1000000","interest_type":"permanent","desired_period":"8","interest_rate":"2"}]',
          total_loan: 2000000,
          user_id: null,
          ip: "**************",
          screen_width: "2034",
          screen_height: "532",
          sort_by_param: "price_asc",
          sort_by_period: "first_year_sum",
          area_id: "0",
          updated_at: "2023-07-03T16:22:22.000000Z",
          created_at: "2023-07-03T16:22:22.000000Z",
          id: 3966,
        },
        results_form_code: "s9879822",
        token: "lqpkn2zvyr",
        companies_offer: [
          "[84.6405,79.5799,70.5677,61.3765,51.6415,37.4115,24.8225,9.0151]",
        ],
        companies_offer_after_agent_discount: 67.7124,
        bid_price_total_years: 4104.14,
        companies_offer_for_all_years_arr: [
          {
            company_one_year_offer: 84.64,
            agent_discount_for_that_year: 20,
            agent_discount_for_that_year_in_money: 203.14,
            company_offer_after_agent_discount_for_month: 67.71,
            company_offer_after_agent_discount_for_year: 812.55,
            incremental_cost: 812.55,
            incremental_year: 1,
          },
          {
            company_one_year_offer: 79.58,
            agent_discount_for_that_year: 20,
            agent_discount_for_that_year_in_money: 190.99,
            company_offer_after_agent_discount_for_month: 63.66,
            company_offer_after_agent_discount_for_year: 763.97,
            incremental_cost: 1576.52,
            incremental_year: 2,
          },
          {
            company_one_year_offer: 70.57,
            agent_discount_for_that_year: 20,
            agent_discount_for_that_year_in_money: 169.36,
            company_offer_after_agent_discount_for_month: 56.45,
            company_offer_after_agent_discount_for_year: 677.45,
            incremental_cost: 2253.97,
            incremental_year: 3,
          },
          {
            company_one_year_offer: 61.38,
            agent_discount_for_that_year: 20,
            agent_discount_for_that_year_in_money: 147.3,
            company_offer_after_agent_discount_for_month: 49.1,
            company_offer_after_agent_discount_for_year: 589.21,
            incremental_cost: 2843.18,
            incremental_year: 4,
          },
          {
            company_one_year_offer: 51.64,
            agent_discount_for_that_year: 20,
            agent_discount_for_that_year_in_money: 123.94,
            company_offer_after_agent_discount_for_month: 41.31,
            company_offer_after_agent_discount_for_year: 495.76,
            incremental_cost: 3338.94,
            incremental_year: 5,
          },
          {
            company_one_year_offer: 37.41,
            agent_discount_for_that_year: 20,
            agent_discount_for_that_year_in_money: 89.79,
            company_offer_after_agent_discount_for_month: 29.93,
            company_offer_after_agent_discount_for_year: 359.15,
            incremental_cost: 3698.09,
            incremental_year: 6,
          },
          {
            company_one_year_offer: 24.82,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 24.82,
            company_offer_after_agent_discount_for_year: 297.87,
            incremental_cost: 3995.96,
            incremental_year: 7,
          },
          {
            company_one_year_offer: 9.02,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 9.02,
            company_offer_after_agent_discount_for_year: 108.18,
            incremental_cost: 4104.14,
            incremental_year: 8,
          },
        ],
        offer_price: 67.71,
        b_first_year: 67.71,
        featured_terms: [
          {
            id: 40,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 6,
            text: "<ul>\n<li>\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.</li>\n<li>\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.</li>\n<li>\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.</li></ul>",
            term: "\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.\n\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.\n\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.",
            number: null,
            featured: 1,
            short:
              "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05db\u05d9\u05e1\u05d5\u05d9 \u05d2\u05d5\u05d1\u05d4 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
            note: null,
            created_at: "2020-04-08 11:41:24",
            updated_at: "2023-03-15 17:44:06",
          },
          {
            id: 41,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 7,
            text: '<span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - </span><b style="font-size: 1rem;">78</b><br><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - </span><b style="font-size: 1rem;">74</b><br><span style="font-size: 1rem;">&nbsp;\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - </span><b style="font-size: 1rem;">81</b><br>',
            term: "<div>\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - <b>84</b></div><div>\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - 74&nbsp;</div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - 81</div>",
            number: null,
            featured: 1,
            short:
              "\u05de\u05d3\u05d3 \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 84",
            note: null,
            created_at: "2020-04-08 11:41:39",
            updated_at: "2023-06-13 11:35:56",
          },
        ],
        company_name: "\u05de\u05d2\u05d3\u05dc",
      },
      {
        id: 23,
        agent_id: 23,
        provider_id: 25,
        product_id: 6037,
        policy_id: 7,
        mortgage_insurance_request_id: 3966,
        about_us:
          "\u05de\u05d5\u05de\u05d7\u05d4 \u05dc\u05ea\u05db\u05e0\u05d5\u05df \u05e4\u05e8\u05d9\u05e9\u05d4 \u05d5\u05d4\u05d2\u05d9\u05dc \u05d4\u05e9\u05dc\u05d9\u05e9\u05d9.\r\n\u05de\u05d5\u05de\u05d7\u05d9\u05d5\u05ea \u05d1\u05e0\u05d9\u05ea\u05d5\u05d7 \u05d5\u05d1\u05e0\u05d9\u05d9\u05ea \u05ea\u05db\u05e0\u05d9\u05d5\u05ea \u05e4\u05e0\u05e1\u05d9\u05d5\u05e0\u05d9\u05d5\u05ea \u05de\u05d5\u05ea\u05d0\u05de\u05d5\u05ea \u05d0\u05d9\u05e9\u05d9\u05ea, \u05d4\u05ea\u05d0\u05de\u05ea \u05ea\u05db\u05e0\u05d9\u05d5\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05e2\u05d5\u05d1\u05d3\u05d9\u05dd, \u05dc\u05e2\u05e6\u05de\u05d0\u05d9\u05d9\u05dd \u05d5\u05dc\u05ea\u05d0 \u05d4\u05de\u05e9\u05e4\u05d7\u05ea\u05d9 .",
        agency_name:
          "\u05e4\u05d0\u05e8\u05d8\u05d5 - \u05d0\u05d5\u05de\u05e0\u05d5\u05ea \u05d4\u05d4\u05e9\u05e7\u05e2\u05d5\u05ea \u05d5\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05e4\u05e0\u05e1\u05d9\u05d5\u05e0\u05d9",
        email: "<EMAIL>",
        phone: "**********",
        site_phone: "073-3745403",
        listing_image:
          "https://trusty.co.il/uploads/agents/listings/agent-listing6000ba1b224ce.png",
        address_city: "\u05e4\u05ea\u05d7 \u05ea\u05e7\u05d5\u05d5\u05d4",
        company_img_name: null,
        avg_review: "10.00",
        total_reviews: 1,
        agent_areas: [3, 4, 5],
        total_price: null,
        average_month_price: 43.51,
        company_id: 4,
        discount_first_year: "35.0",
        discount_for_all_years: [
          "35.0",
          "35.0",
          "35.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
        ],
        discount_for_rest_of_the_years: "0.0",
        sort_by_period: "first_year_sum",
        benefits: [
          {
            id: 5,
            agent_id: 23,
            name: "\u05e9\u05d9\u05d7\u05ea \u05d9\u05e2\u05d5\u05e5 \u05d7\u05d9\u05e0\u05dd",
            details:
              "\u05d1\u05d3\u05d9\u05e7\u05ea \u05ea\u05d9\u05e7 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05d9\u05e0\u05dd",
            active: 1,
            created_at: "2019-09-30T12:29:14.000000Z",
            updated_at: "2020-12-30T14:31:57.000000Z",
            pivot: { agent_mortgage_bid_id: 6037, benefit_id: 5 },
          },
          {
            id: 41,
            agent_id: 23,
            name: "\u05d1\u05d3\u05d9\u05e7\u05ea \u05ea\u05d9\u05e7 \u05e4\u05e0\u05e1\u05d9\u05d5\u05e0\u05d9",
            details:
              "\u05d1\u05d3\u05d9\u05e7\u05ea \u05ea\u05d9\u05e7 \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05de\u05e9\u05e8\u05d3 \u05d4\u05e1\u05d5\u05db\u05df",
            active: 1,
            created_at: "2019-11-14T11:50:27.000000Z",
            updated_at: "2019-11-14T11:50:27.000000Z",
            pivot: { agent_mortgage_bid_id: 6037, benefit_id: 41 },
          },
        ],
        the_user_request_parameters: {
          agent_id: 25,
          provider_id: 25,
          number_of_insured: "individual",
          age1: "35",
          gender1: "female",
          is_smoking1: false,
          date_of_birth1: "1988-07-07T16:22:22.000000Z",
          health_condition1: "excellent",
          tracks:
            '[{"desired_sum":"1000000","interest_type":"temporary","desired_period":"7","interest_rate":"0.001"},{"desired_sum":"1000000","interest_type":"permanent","desired_period":"8","interest_rate":"2"}]',
          total_loan: 2000000,
          user_id: null,
          ip: "**************",
          screen_width: "2034",
          screen_height: "532",
          sort_by_param: "price_asc",
          sort_by_period: "first_year_sum",
          area_id: "0",
          updated_at: "2023-07-03T16:22:22.000000Z",
          created_at: "2023-07-03T16:22:22.000000Z",
          id: 3966,
        },
        results_form_code: "s9879822",
        token: "lyjes2jxht",
        companies_offer: [
          "[116.6667,101.0422,85.4175,69.7927,54.1678,38.5426,23.8995,8.1253]",
        ],
        companies_offer_after_agent_discount: 75.83335500000001,
        bid_price_total_years: 4698.72,
        companies_offer_for_all_years_arr: [
          {
            company_one_year_offer: 116.67,
            agent_discount_for_that_year: 35,
            agent_discount_for_that_year_in_money: 490,
            company_offer_after_agent_discount_for_month: 75.83,
            company_offer_after_agent_discount_for_year: 910,
            incremental_cost: 910,
            incremental_year: 1,
          },
          {
            company_one_year_offer: 101.04,
            agent_discount_for_that_year: 35,
            agent_discount_for_that_year_in_money: 424.38,
            company_offer_after_agent_discount_for_month: 65.68,
            company_offer_after_agent_discount_for_year: 788.13,
            incremental_cost: 1698.13,
            incremental_year: 2,
          },
          {
            company_one_year_offer: 85.42,
            agent_discount_for_that_year: 35,
            agent_discount_for_that_year_in_money: 358.75,
            company_offer_after_agent_discount_for_month: 55.52,
            company_offer_after_agent_discount_for_year: 666.26,
            incremental_cost: 2364.39,
            incremental_year: 3,
          },
          {
            company_one_year_offer: 69.79,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 69.79,
            company_offer_after_agent_discount_for_year: 837.51,
            incremental_cost: 3201.9,
            incremental_year: 4,
          },
          {
            company_one_year_offer: 54.17,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 54.17,
            company_offer_after_agent_discount_for_year: 650.01,
            incremental_cost: 3851.91,
            incremental_year: 5,
          },
          {
            company_one_year_offer: 38.54,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 38.54,
            company_offer_after_agent_discount_for_year: 462.51,
            incremental_cost: 4314.42,
            incremental_year: 6,
          },
          {
            company_one_year_offer: 23.9,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 23.9,
            company_offer_after_agent_discount_for_year: 286.79,
            incremental_cost: 4601.22,
            incremental_year: 7,
          },
          {
            company_one_year_offer: 8.13,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 8.13,
            company_offer_after_agent_discount_for_year: 97.5,
            incremental_cost: 4698.72,
            incremental_year: 8,
          },
        ],
        offer_price: 75.83,
        b_first_year: 75.83,
        featured_terms: {
          "6": {
            id: 51,
            policy_id: 7,
            company_id: 4,
            condition_section_id: 6,
            text: "<ul><li>\u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d9\u05e7\u05d8\u05df \u05de\u05d9\u05d3\u05d9 \u05e9\u05e0\u05d4, \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d4\u05d7\u05d6\u05e8\u05d9 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05e9\u05dc \u05d4\u05dc\u05d5\u05d5\u05d4 \u05dc\u05d1\u05e0\u05e7. </li><li>\u05d1\u05db\u05da \u05e7\u05d9\u05d9\u05de\u05ea \u05d4\u05ea\u05d0\u05de\u05d4 \u05de\u05dc\u05d0\u05d4 \u05dc\u05de\u05d9\u05d3\u05ea \u05d4\u05e1\u05d9\u05db\u05d5\u05df \u05d4\u05d4\u05d5\u05dc\u05db\u05ea \u05d5\u05e7\u05d8\u05e0\u05d4, \u05d0\u05dc\u05d9\u05d4 \u05e0\u05d7\u05e9\u05e3 \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7. </li><li>\u05e0\u05d9\u05ea\u05df \u05d2\u05dd \u05dc\u05d1\u05d7\u05d5\u05e8 \u05d1\u05de\u05e1\u05dc\u05d5\u05dc \u05e9\u05d1\u05d5 \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05e0\u05d5\u05ea\u05e8 \u05e7\u05d1\u05d5\u05e2, \u05d4\u05de\u05ea\u05d0\u05d9\u05dd \u05dc\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d1\u05d4 \u05de\u05d5\u05d7\u05d6\u05e8\u05ea \u05d4\u05e7\u05e8\u05df \u05e8\u05e7 \u05d1\u05e1\u05d5\u05e3 \u05d4\u05ea\u05e7\u05d5\u05e4\u05d4 \u05d5\u05d4\u05e8\u05d9\u05d1\u05d9\u05ea \u05de\u05d5\u05d7\u05d6\u05e8\u05ea \u05d1\u05d0\u05d5\u05e4\u05df \u05e9\u05d5\u05d8\u05e3. </li><li>\u05e0\u05d9\u05ea\u05df \u05dc\u05d1\u05d7\u05d5\u05e8 \u05d0\u05ea \u05de\u05e1\u05dc\u05d5\u05dc\u05d9 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05e4\u05d9 \u05e1\u05d5\u05d2\u05d9 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d5\u05ea \u05e9\u05e0\u05dc\u05e7\u05d7\u05d5\u05ea \u05de\u05d4\u05d1\u05e0\u05e7: </li><li>\u05de\u05e1\u05dc\u05d5\u05dc \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05de\u05d5\u05ea\u05d0\u05dd \u05dc\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d1\u05d4 \u05d4\u05e7\u05e8\u05df \u05d5\u05d4\u05e8\u05d9\u05d1\u05d9\u05ea \u05de\u05d5\u05d7\u05d6\u05e8\u05d5\u05ea \u05d9\u05d7\u05d3\u05d9\u05d5, \u05d1\u05ea\u05e9\u05dc\u05d5\u05de\u05d9\u05dd \u05e9\u05d5\u05d5\u05d9\u05dd \u05d5\u05e8\u05e6\u05d5\u05e4\u05d9\u05dd (\u05d4\u05dc\u05d5\u05d5\u05d0\u05ea \u05e9\u05e4\u05d9\u05e6\u05e8). </li><li>\u05de\u05e1\u05dc\u05d5\u05dc \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05de\u05ea\u05d0\u05d9\u05dd \u05dc\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d1\u05d4 \u05d4\u05e7\u05e8\u05df \u05de\u05d5\u05d7\u05d6\u05e8\u05ea \u05d1\u05e1\u05d5\u05e3 \u05d4\u05ea\u05e7\u05d5\u05e4\u05d4, \u05d5\u05d4\u05e8\u05d9\u05d1\u05d9\u05ea \u05de\u05d5\u05d7\u05d6\u05e8\u05ea \u05d1\u05d0\u05d5\u05e4\u05df \u05e9\u05d5\u05d8\u05e3 (\u05d4\u05dc\u05d5\u05d5\u05d0\u05ea \u05d1\u05dc\u05d5\u05df). </li><li>\u05e0\u05d9\u05ea\u05df \u05dc\u05d1\u05d7\u05d5\u05e8 \u05d4\u05d0\u05dd \u05d4\u05ea\u05e9\u05dc\u05d5\u05dd \u05d4\u05d7\u05d5\u05d3\u05e9\u05d9 \u05d9\u05d4\u05d9\u05d4 \u05d1\u05ea\u05e2\u05e8\u05d9\u05e3 \u05e7\u05d1\u05d5\u05e2, \u05d0\u05d5 \u05d1\u05ea\u05e2\u05e8\u05d9\u05e3 \u05de\u05e9\u05ea\u05e0\u05d4.&nbsp; \u05e0\u05d9\u05ea\u05df \u05dc\u05d4\u05e2\u05d1\u05d9\u05e8 \u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05de\u05d7\u05d1\u05e8\u05d4 \u05dc\u05d7\u05d1\u05e8\u05d4 \u05d2\u05dd \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7.</li><li> \u05d4\u05d9\u05d4 \u05d5\u05d4\u05d7\u05dc\u05d8\u05ea\u05dd \u05dc\u05d4\u05e2\u05d1\u05d9\u05e8 \u05d0\u05ea \u05e4\u05d5\u05dc\u05d9\u05e1\u05ea \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0 \u05d0\u05dc\u05d9\u05e0\u05d5 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05d4, \u05d4\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05d5\u05e1\u05d1 \u05dc\u05dc\u05d0 \u05e2\u05dc\u05d5\u05d9\u05d5\u05ea \u05d4\u05d7\u05dc\u05e4\u05d4.</li></ul>",
            term: "\u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d9\u05e7\u05d8\u05df \u05de\u05d9\u05d3\u05d9 \u05e9\u05e0\u05d4, \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d4\u05d7\u05d6\u05e8\u05d9 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05e9\u05dc \u05d4\u05dc\u05d5\u05d5\u05d4 \u05dc\u05d1\u05e0\u05e7. \u05d1\u05db\u05da \u05e7\u05d9\u05d9\u05de\u05ea \u05d4\u05ea\u05d0\u05de\u05d4 \u05de\u05dc\u05d0\u05d4 \u05dc\u05de\u05d9\u05d3\u05ea \u05d4\u05e1\u05d9\u05db\u05d5\u05df \u05d4\u05d4\u05d5\u05dc\u05db\u05ea \u05d5\u05e7\u05d8\u05e0\u05d4, \u05d0\u05dc\u05d9\u05d4 \u05e0\u05d7\u05e9\u05e3 \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7. \u05e0\u05d9\u05ea\u05df \u05d2\u05dd \u05dc\u05d1\u05d7\u05d5\u05e8 \u05d1\u05de\u05e1\u05dc\u05d5\u05dc \u05e9\u05d1\u05d5 \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05e0\u05d5\u05ea\u05e8 \u05e7\u05d1\u05d5\u05e2, \u05d4\u05de\u05ea\u05d0\u05d9\u05dd \u05dc\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d1\u05d4 \u05de\u05d5\u05d7\u05d6\u05e8\u05ea \u05d4\u05e7\u05e8\u05df \u05e8\u05e7 \u05d1\u05e1\u05d5\u05e3 \u05d4\u05ea\u05e7\u05d5\u05e4\u05d4 \u05d5\u05d4\u05e8\u05d9\u05d1\u05d9\u05ea \u05de\u05d5\u05d7\u05d6\u05e8\u05ea \u05d1\u05d0\u05d5\u05e4\u05df \u05e9\u05d5\u05d8\u05e3. \u05e0\u05d9\u05ea\u05df \u05dc\u05d1\u05d7\u05d5\u05e8 \u05d0\u05ea \u05de\u05e1\u05dc\u05d5\u05dc\u05d9 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05e4\u05d9 \u05e1\u05d5\u05d2\u05d9 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d5\u05ea \u05e9\u05e0\u05dc\u05e7\u05d7\u05d5\u05ea \u05de\u05d4\u05d1\u05e0\u05e7: \u05de\u05e1\u05dc\u05d5\u05dc \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05de\u05d5\u05ea\u05d0\u05dd \u05dc\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d1\u05d4 \u05d4\u05e7\u05e8\u05df \u05d5\u05d4\u05e8\u05d9\u05d1\u05d9\u05ea \u05de\u05d5\u05d7\u05d6\u05e8\u05d5\u05ea \u05d9\u05d7\u05d3\u05d9\u05d5, \u05d1\u05ea\u05e9\u05dc\u05d5\u05de\u05d9\u05dd \u05e9\u05d5\u05d5\u05d9\u05dd \u05d5\u05e8\u05e6\u05d5\u05e4\u05d9\u05dd (\u05d4\u05dc\u05d5\u05d5\u05d0\u05ea \u05e9\u05e4\u05d9\u05e6\u05e8). \u05de\u05e1\u05dc\u05d5\u05dc \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05de\u05ea\u05d0\u05d9\u05dd \u05dc\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d1\u05d4 \u05d4\u05e7\u05e8\u05df \u05de\u05d5\u05d7\u05d6\u05e8\u05ea \u05d1\u05e1\u05d5\u05e3 \u05d4\u05ea\u05e7\u05d5\u05e4\u05d4, \u05d5\u05d4\u05e8\u05d9\u05d1\u05d9\u05ea \u05de\u05d5\u05d7\u05d6\u05e8\u05ea \u05d1\u05d0\u05d5\u05e4\u05df \u05e9\u05d5\u05d8\u05e3 (\u05d4\u05dc\u05d5\u05d5\u05d0\u05ea \u05d1\u05dc\u05d5\u05df). \u05e0\u05d9\u05ea\u05df \u05dc\u05d1\u05d7\u05d5\u05e8 \u05d4\u05d0\u05dd \u05d4\u05ea\u05e9\u05dc\u05d5\u05dd \u05d4\u05d7\u05d5\u05d3\u05e9\u05d9 \u05d9\u05d4\u05d9\u05d4 \u05d1\u05ea\u05e2\u05e8\u05d9\u05e3 \u05e7\u05d1\u05d5\u05e2, \u05d0\u05d5 \u05d1\u05ea\u05e2\u05e8\u05d9\u05e3 \u05de\u05e9\u05ea\u05e0\u05d4.&nbsp; \u05e0\u05d9\u05ea\u05df \u05dc\u05d4\u05e2\u05d1\u05d9\u05e8 \u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05de\u05d7\u05d1\u05e8\u05d4 \u05dc\u05d7\u05d1\u05e8\u05d4 \u05d2\u05dd \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7. \u05d4\u05d9\u05d4 \u05d5\u05d4\u05d7\u05dc\u05d8\u05ea\u05dd \u05dc\u05d4\u05e2\u05d1\u05d9\u05e8 \u05d0\u05ea \u05e4\u05d5\u05dc\u05d9\u05e1\u05ea \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0 \u05d0\u05dc\u05d9\u05e0\u05d5 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05d4, \u05d4\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05d5\u05e1\u05d1 \u05dc\u05dc\u05d0 \u05e2\u05dc\u05d5\u05d9\u05d5\u05ea \u05d4\u05d7\u05dc\u05e4\u05d4.",
            number: null,
            featured: 1,
            short:
              "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05db\u05d9\u05e1\u05d5\u05d9 \u05d2\u05d5\u05d1\u05d4 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
            note: null,
            created_at: "2020-04-08 11:46:50",
            updated_at: "2023-03-15 17:45:50",
          },
          "7": {
            id: 52,
            policy_id: 7,
            company_id: 4,
            condition_section_id: 7,
            text: '<span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df&nbsp; \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 -&nbsp;&nbsp;</span><span style="font-size: 1rem; font-weight: bolder;">87</span><br><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea -&nbsp;</span><span style="font-size: 1rem; font-weight: bolder;">94</span><br><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea -&nbsp;</span><span style="font-size: 1rem; font-weight: bolder;">78</span><br>',
            term: "<div>\u05e6\u05d9\u05d5\u05df&nbsp; \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 -&nbsp;&nbsp;<b>85</b></div><div>\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea -&nbsp;94</div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea -&nbsp;78</div>",
            number: null,
            featured: 1,
            short:
              "\u05de\u05d3\u05d3 \u05e9\u05d9\u05e8\u05d5\u05ea \u05d7\u05d1\u05e8\u05d4 85",
            note: null,
            created_at: "2020-04-08 11:47:27",
            updated_at: "2023-06-13 11:37:09",
          },
        },
        company_name: "\u05d4\u05e4\u05e0\u05d9\u05e7\u05e1",
      },
      {
        id: 156,
        agent_id: 156,
        provider_id: 25,
        product_id: 6423,
        policy_id: 7,
        mortgage_insurance_request_id: 3966,
        about_us:
          "\u05d0\u05d3\u05d5\u05d5\u05d9\u05d6\u05d5\u05e8 \u05de\u05de\u05d5\u05de\u05d7\u05d9 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05de\u05e2\u05e0\u05d9\u05e7\u05d9\u05dd \u05d4\u05db\u05d5\u05d5\u05e0\u05d4 \u05dc\u05de\u05e9\u05e4\u05d7\u05d5\u05ea \u05d5\u05d6\u05d5\u05d2\u05d5\u05ea \u05e6\u05e2\u05d9\u05e8\u05d9\u05dd \u05d4\u05de\u05d1\u05e7\u05e9\u05d9\u05dd \u05dc\u05e7\u05d1\u05dc \u05ea\u05de\u05d5\u05e0\u05ea \u05de\u05e6\u05d1 \u05e2\u05d3\u05db\u05e0\u05d9\u05ea \u05d0\u05d5\u05d3\u05d5\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7\u05d9\u05dd \u05d5\u05d4\u05d7\u05d9\u05e1\u05db\u05d5\u05df \u05dc\u05e4\u05e0\u05e1\u05d9\u05d4, \u05dc\u05d7\u05e1\u05d5\u05da \u05d1\u05e2\u05dc\u05d5\u05d9\u05d5\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d5\u05dc\u05d4\u05d1\u05d8\u05d9\u05d7 \u05d0\u05ea \u05e2\u05ea\u05d9\u05d3\u05dd",
        agency_name:
          "\u05d0\u05d3\u05d5\u05d5\u05d9\u05d6\u05e8 \u05e4\u05d9\u05e0\u05e0\u05e1\u05d9\u05dd",
        email: "<EMAIL>",
        phone: null,
        site_phone: "073-3745400",
        listing_image:
          "https://trusty.co.il/uploads/agents/listings/agent-listing64032d10079d4.png",
        address_city: "\u05d4\u05e8\u05e6\u05dc\u05d9\u05d4",
        company_img_name: null,
        avg_review: "9.33",
        total_reviews: 3,
        agent_areas: [1, 2],
        total_price: null,
        average_month_price: 43.85,
        company_id: 1,
        discount_first_year: "10.0",
        discount_for_all_years: [
          "10.0",
          "20.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
        ],
        discount_for_rest_of_the_years: "99.0",
        sort_by_period: "first_year_sum",
        benefits: [],
        the_user_request_parameters: {
          agent_id: 25,
          provider_id: 25,
          number_of_insured: "individual",
          age1: "35",
          gender1: "female",
          is_smoking1: false,
          date_of_birth1: "1988-07-07T16:22:22.000000Z",
          health_condition1: "excellent",
          tracks:
            '[{"desired_sum":"1000000","interest_type":"temporary","desired_period":"7","interest_rate":"0.001"},{"desired_sum":"1000000","interest_type":"permanent","desired_period":"8","interest_rate":"2"}]',
          total_loan: 2000000,
          user_id: null,
          ip: "**************",
          screen_width: "2034",
          screen_height: "532",
          sort_by_param: "price_asc",
          sort_by_period: "first_year_sum",
          area_id: "0",
          updated_at: "2023-07-03T16:22:22.000000Z",
          created_at: "2023-07-03T16:22:22.000000Z",
          id: 3966,
        },
        results_form_code: "s9879822",
        token: "sz7a3w5gka",
        companies_offer: [
          "[84.6405,79.5799,70.5677,61.3765,51.6415,37.4115,24.8225,9.0151]",
        ],
        companies_offer_after_agent_discount: 76.17645,
        bid_price_total_years: 4736.1,
        companies_offer_for_all_years_arr: [
          {
            company_one_year_offer: 84.64,
            agent_discount_for_that_year: 10,
            agent_discount_for_that_year_in_money: 101.57,
            company_offer_after_agent_discount_for_month: 76.18,
            company_offer_after_agent_discount_for_year: 914.12,
            incremental_cost: 914.12,
            incremental_year: 1,
          },
          {
            company_one_year_offer: 79.58,
            agent_discount_for_that_year: 20,
            agent_discount_for_that_year_in_money: 190.99,
            company_offer_after_agent_discount_for_month: 63.66,
            company_offer_after_agent_discount_for_year: 763.97,
            incremental_cost: 1678.08,
            incremental_year: 2,
          },
          {
            company_one_year_offer: 70.57,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 70.57,
            company_offer_after_agent_discount_for_year: 846.81,
            incremental_cost: 2524.9,
            incremental_year: 3,
          },
          {
            company_one_year_offer: 61.38,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 61.38,
            company_offer_after_agent_discount_for_year: 736.52,
            incremental_cost: 3261.41,
            incremental_year: 4,
          },
          {
            company_one_year_offer: 51.64,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 51.64,
            company_offer_after_agent_discount_for_year: 619.7,
            incremental_cost: 3881.11,
            incremental_year: 5,
          },
          {
            company_one_year_offer: 37.41,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 37.41,
            company_offer_after_agent_discount_for_year: 448.94,
            incremental_cost: 4330.05,
            incremental_year: 6,
          },
          {
            company_one_year_offer: 24.82,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 24.82,
            company_offer_after_agent_discount_for_year: 297.87,
            incremental_cost: 4627.92,
            incremental_year: 7,
          },
          {
            company_one_year_offer: 9.02,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 9.02,
            company_offer_after_agent_discount_for_year: 108.18,
            incremental_cost: 4736.1,
            incremental_year: 8,
          },
        ],
        offer_price: 76.18,
        b_first_year: 76.18,
        featured_terms: [
          {
            id: 40,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 6,
            text: "<ul>\n<li>\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.</li>\n<li>\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.</li>\n<li>\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.</li></ul>",
            term: "\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.\n\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.\n\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.",
            number: null,
            featured: 1,
            short:
              "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05db\u05d9\u05e1\u05d5\u05d9 \u05d2\u05d5\u05d1\u05d4 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
            note: null,
            created_at: "2020-04-08 11:41:24",
            updated_at: "2023-03-15 17:44:06",
          },
          {
            id: 41,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 7,
            text: '<span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - </span><b style="font-size: 1rem;">78</b><br><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - </span><b style="font-size: 1rem;">74</b><br><span style="font-size: 1rem;">&nbsp;\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - </span><b style="font-size: 1rem;">81</b><br>',
            term: "<div>\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - <b>84</b></div><div>\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - 74&nbsp;</div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - 81</div>",
            number: null,
            featured: 1,
            short:
              "\u05de\u05d3\u05d3 \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 84",
            note: null,
            created_at: "2020-04-08 11:41:39",
            updated_at: "2023-06-13 11:35:56",
          },
        ],
        company_name: "\u05de\u05d2\u05d3\u05dc",
      },
      {
        id: 61,
        agent_id: 61,
        provider_id: 25,
        product_id: 4145,
        policy_id: 7,
        mortgage_insurance_request_id: 3966,
        about_us:
          "\u05d4\u05d7\u05d1\u05e8\u05d4 \u05e8\u05d5\u05d0\u05d4 \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea\u05d9\u05d4 \u05e9\u05d5\u05ea\u05e4\u05d9\u05dd \u05dc\u05d3\u05e8\u05da, \u05de\u05e2\u05e0\u05d9\u05e7\u05d4 \u05dc\u05d4\u05dd \u05e9\u05e7\u05d8 \u05e0\u05e4\u05e9\u05d9 \u05d5\u05d3\u05d5\u05d0\u05d2\u05ea \u05dc\u05de\u05e7\u05e1\u05d5\u05dd \u05e0\u05db\u05e1\u05d9\u05d4\u05dd, \u05ea\u05d5\u05da \u05dc\u05d9\u05d5\u05d5\u05d9 \u05d0\u05d9\u05e9\u05d9 \u05d5\u05de\u05ea\u05de\u05e9\u05da \u05dc\u05d0\u05d5\u05e8\u05da \u05e9\u05e0\u05d9\u05dd. \u05de\u05d2\u05d5\u05d5\u05df \u05e8\u05d7\u05d1 \u05e9\u05dc \u05dc\u05e7\u05d5\u05d7\u05d5\u05ea \u05db\u05d2\u05d5\u05df: \u05e2\u05d5\u05e8\u05db\u05d9 ",
        agency_name:
          "\u05d0\u05e1\u05e3 \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d5\u05e4\u05d9\u05e0\u05e0\u05e1\u05d9\u05dd",
        email: "<EMAIL>",
        phone: null,
        site_phone: "073-3745418",
        listing_image:
          "https://trusty.co.il/uploads/agents/listings/agent-listing6000afc7a53b0.png",
        address_city: "\u05d1\u05d0\u05e8 \u05e9\u05d1\u05e2",
        company_img_name: null,
        avg_review: "0.00",
        total_reviews: 0,
        agent_areas: [3, 6],
        total_price: null,
        average_month_price: 46.56,
        company_id: 1,
        discount_first_year: "0.0",
        discount_for_all_years: [
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
        ],
        discount_for_rest_of_the_years: "0.0",
        sort_by_period: "first_year_sum",
        benefits: [
          {
            id: 32,
            agent_id: 61,
            name: "\u05e9\u05d9\u05d7\u05ea \u05d9\u05e2\u05d5\u05e5 \u05d7\u05d9\u05e0\u05dd",
            details:
              "\u05d1\u05d3\u05d9\u05e7\u05ea \u05ea\u05d9\u05e7 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05d9\u05e0\u05dd",
            active: 1,
            created_at: "2019-09-30T12:29:14.000000Z",
            updated_at: "2020-12-30T14:31:57.000000Z",
            pivot: { agent_mortgage_bid_id: 4145, benefit_id: 32 },
          },
        ],
        the_user_request_parameters: {
          agent_id: 25,
          provider_id: 25,
          number_of_insured: "individual",
          age1: "35",
          gender1: "female",
          is_smoking1: false,
          date_of_birth1: "1988-07-07T16:22:22.000000Z",
          health_condition1: "excellent",
          tracks:
            '[{"desired_sum":"1000000","interest_type":"temporary","desired_period":"7","interest_rate":"0.001"},{"desired_sum":"1000000","interest_type":"permanent","desired_period":"8","interest_rate":"2"}]',
          total_loan: 2000000,
          user_id: null,
          ip: "**************",
          screen_width: "2034",
          screen_height: "532",
          sort_by_param: "price_asc",
          sort_by_period: "first_year_sum",
          area_id: "0",
          updated_at: "2023-07-03T16:22:22.000000Z",
          created_at: "2023-07-03T16:22:22.000000Z",
          id: 3966,
        },
        results_form_code: "s9879822",
        token: "vsbxgbhpx3",
        companies_offer: [
          "[84.6405,79.5799,70.5677,61.3765,51.6415,37.4115,24.8225,9.0151]",
        ],
        companies_offer_after_agent_discount: 84.6405,
        bid_price_total_years: 5028.66,
        companies_offer_for_all_years_arr: [
          {
            company_one_year_offer: 84.64,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 84.64,
            company_offer_after_agent_discount_for_year: 1015.69,
            incremental_cost: 1015.69,
            incremental_year: 1,
          },
          {
            company_one_year_offer: 79.58,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 79.58,
            company_offer_after_agent_discount_for_year: 954.96,
            incremental_cost: 1970.64,
            incremental_year: 2,
          },
          {
            company_one_year_offer: 70.57,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 70.57,
            company_offer_after_agent_discount_for_year: 846.81,
            incremental_cost: 2817.46,
            incremental_year: 3,
          },
          {
            company_one_year_offer: 61.38,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 61.38,
            company_offer_after_agent_discount_for_year: 736.52,
            incremental_cost: 3553.98,
            incremental_year: 4,
          },
          {
            company_one_year_offer: 51.64,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 51.64,
            company_offer_after_agent_discount_for_year: 619.7,
            incremental_cost: 4173.67,
            incremental_year: 5,
          },
          {
            company_one_year_offer: 37.41,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 37.41,
            company_offer_after_agent_discount_for_year: 448.94,
            incremental_cost: 4622.61,
            incremental_year: 6,
          },
          {
            company_one_year_offer: 24.82,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 24.82,
            company_offer_after_agent_discount_for_year: 297.87,
            incremental_cost: 4920.48,
            incremental_year: 7,
          },
          {
            company_one_year_offer: 9.02,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 9.02,
            company_offer_after_agent_discount_for_year: 108.18,
            incremental_cost: 5028.66,
            incremental_year: 8,
          },
        ],
        offer_price: 84.64,
        b_first_year: 84.64,
        featured_terms: [
          {
            id: 40,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 6,
            text: "<ul>\n<li>\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.</li>\n<li>\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.</li>\n<li>\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.</li></ul>",
            term: "\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.\n\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.\n\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.",
            number: null,
            featured: 1,
            short:
              "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05db\u05d9\u05e1\u05d5\u05d9 \u05d2\u05d5\u05d1\u05d4 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
            note: null,
            created_at: "2020-04-08 11:41:24",
            updated_at: "2023-03-15 17:44:06",
          },
          {
            id: 41,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 7,
            text: '<span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - </span><b style="font-size: 1rem;">78</b><br><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - </span><b style="font-size: 1rem;">74</b><br><span style="font-size: 1rem;">&nbsp;\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - </span><b style="font-size: 1rem;">81</b><br>',
            term: "<div>\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - <b>84</b></div><div>\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - 74&nbsp;</div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - 81</div>",
            number: null,
            featured: 1,
            short:
              "\u05de\u05d3\u05d3 \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 84",
            note: null,
            created_at: "2020-04-08 11:41:39",
            updated_at: "2023-06-13 11:35:56",
          },
        ],
        company_name: "\u05de\u05d2\u05d3\u05dc",
      },
      {
        id: 87,
        agent_id: 87,
        provider_id: 25,
        product_id: 4851,
        policy_id: 7,
        mortgage_insurance_request_id: 3966,
        about_us:
          "\u05e9\u05dc\u05d5\u05dd \u05e8\u05d1,\r\n\u05e9\u05de\u05d9 \u05e8\u05d5\u05df \u05d3\u05d1\u05e8\u05d9 \u05d5\u05d0\u05e0\u05d9 \u05e1\u05d5\u05db\u05df \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05ea\u05d7\u05d5\u05de\u05d9 \u05d4\u05d7\u05d9\u05d9\u05dd, \u05d4\u05e4\u05e0\u05e1\u05d9\u05d5\u05e0\u05d9, \u05d4\u05d1\u05e8\u05d9\u05d0\u05d5\u05ea ,\u05d4\u05d3\u05d9\u05e8\u05d5\u05ea ,\u05d4\u05e8\u05db\u05d1\u05d9\u05dd \u05d5\u05d4\u05e2\u05e1\u05e7\u05d9\u05dd.  \u05db\u05de\u05d5 \u05db\u05df \u05e2\u05d5\u05e1\u05e7 \u05d1\u05ea\u05db\u05e0\u05d5\u05df \u05e4\u05e8\u05d9\u05e9\u05d4. \u05d5\u05db\u05dc \u05d6\u05d0\u05ea \u05d1\u05de\u05e9\u05da \u05dc\u05de\u05e2\u05dc\u05d4 \u05de 40 \u05e9\u05e0\u05d4. \r\n\u05d1\u05e9\u05d1\u05d9\u05dc\u05d9 \u05d6\u05d5 \u05d6\u05db\u05d5\u05ea \u05dc\u05ea\u05ea \u05e9\u05e8\u05d5\u05ea.",
        agency_name:
          "\u05e8\u05d5\u05df \u05d3\u05d1\u05e8\u05d9 \u05e1\u05d5\u05db\u05e0\u05d5\u05ea \u05dc\u05d1\u05d9\u05d8\u05d5\u05d7",
        email: "<EMAIL>",
        phone: null,
        site_phone: "073-3745419",
        listing_image:
          "https://trusty.co.il/uploads/agents/listings/agent-listing6000b0bc7e1ef.png",
        address_city: "\u05d6\u05db\u05e8\u05d5\u05df \u05d9\u05e2\u05e7\u05d1",
        company_img_name: null,
        avg_review: "0.00",
        total_reviews: 0,
        agent_areas: [1, 2, 3, 4, 5, 6],
        total_price: null,
        average_month_price: 46.56,
        company_id: 1,
        discount_first_year: "0.0",
        discount_for_all_years: [
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
        ],
        discount_for_rest_of_the_years: "0.0",
        sort_by_period: "first_year_sum",
        benefits: [
          {
            id: 62,
            agent_id: 87,
            name: "\u05e9\u05d9\u05d7\u05ea \u05d9\u05e2\u05d5\u05e5 \u05d7\u05d9\u05e0\u05dd",
            details:
              "\u05d1\u05d3\u05d9\u05e7\u05ea \u05ea\u05d9\u05e7 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05d9\u05e0\u05dd",
            active: 1,
            created_at: "2019-11-19T18:15:52.000000Z",
            updated_at: "2020-12-30T14:31:57.000000Z",
            pivot: { agent_mortgage_bid_id: 4851, benefit_id: 62 },
          },
        ],
        the_user_request_parameters: {
          agent_id: 25,
          provider_id: 25,
          number_of_insured: "individual",
          age1: "35",
          gender1: "female",
          is_smoking1: false,
          date_of_birth1: "1988-07-07T16:22:22.000000Z",
          health_condition1: "excellent",
          tracks:
            '[{"desired_sum":"1000000","interest_type":"temporary","desired_period":"7","interest_rate":"0.001"},{"desired_sum":"1000000","interest_type":"permanent","desired_period":"8","interest_rate":"2"}]',
          total_loan: 2000000,
          user_id: null,
          ip: "**************",
          screen_width: "2034",
          screen_height: "532",
          sort_by_param: "price_asc",
          sort_by_period: "first_year_sum",
          area_id: "0",
          updated_at: "2023-07-03T16:22:22.000000Z",
          created_at: "2023-07-03T16:22:22.000000Z",
          id: 3966,
        },
        results_form_code: "s9879822",
        token: "12gul1zny0",
        companies_offer: [
          "[84.6405,79.5799,70.5677,61.3765,51.6415,37.4115,24.8225,9.0151]",
        ],
        companies_offer_after_agent_discount: 84.6405,
        bid_price_total_years: 5028.66,
        companies_offer_for_all_years_arr: [
          {
            company_one_year_offer: 84.64,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 84.64,
            company_offer_after_agent_discount_for_year: 1015.69,
            incremental_cost: 1015.69,
            incremental_year: 1,
          },
          {
            company_one_year_offer: 79.58,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 79.58,
            company_offer_after_agent_discount_for_year: 954.96,
            incremental_cost: 1970.64,
            incremental_year: 2,
          },
          {
            company_one_year_offer: 70.57,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 70.57,
            company_offer_after_agent_discount_for_year: 846.81,
            incremental_cost: 2817.46,
            incremental_year: 3,
          },
          {
            company_one_year_offer: 61.38,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 61.38,
            company_offer_after_agent_discount_for_year: 736.52,
            incremental_cost: 3553.98,
            incremental_year: 4,
          },
          {
            company_one_year_offer: 51.64,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 51.64,
            company_offer_after_agent_discount_for_year: 619.7,
            incremental_cost: 4173.67,
            incremental_year: 5,
          },
          {
            company_one_year_offer: 37.41,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 37.41,
            company_offer_after_agent_discount_for_year: 448.94,
            incremental_cost: 4622.61,
            incremental_year: 6,
          },
          {
            company_one_year_offer: 24.82,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 24.82,
            company_offer_after_agent_discount_for_year: 297.87,
            incremental_cost: 4920.48,
            incremental_year: 7,
          },
          {
            company_one_year_offer: 9.02,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 9.02,
            company_offer_after_agent_discount_for_year: 108.18,
            incremental_cost: 5028.66,
            incremental_year: 8,
          },
        ],
        offer_price: 84.64,
        b_first_year: 84.64,
        featured_terms: [
          {
            id: 40,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 6,
            text: "<ul>\n<li>\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.</li>\n<li>\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.</li>\n<li>\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.</li></ul>",
            term: "\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.\n\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.\n\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.",
            number: null,
            featured: 1,
            short:
              "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05db\u05d9\u05e1\u05d5\u05d9 \u05d2\u05d5\u05d1\u05d4 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
            note: null,
            created_at: "2020-04-08 11:41:24",
            updated_at: "2023-03-15 17:44:06",
          },
          {
            id: 41,
            policy_id: 7,
            company_id: 1,
            condition_section_id: 7,
            text: '<span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - </span><b style="font-size: 1rem;">78</b><br><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - </span><b style="font-size: 1rem;">74</b><br><span style="font-size: 1rem;">&nbsp;\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - </span><b style="font-size: 1rem;">81</b><br>',
            term: "<div>\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - <b>84</b></div><div>\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - 74&nbsp;</div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - 81</div>",
            number: null,
            featured: 1,
            short:
              "\u05de\u05d3\u05d3 \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 84",
            note: null,
            created_at: "2020-04-08 11:41:39",
            updated_at: "2023-06-13 11:35:56",
          },
        ],
        company_name: "\u05de\u05d2\u05d3\u05dc",
      },
      {
        id: 25,
        agent_id: 25,
        provider_id: 25,
        product_id: 6260,
        policy_id: 7,
        mortgage_insurance_request_id: 3966,
        about_us:
          "\u05de\u05e8\u05db\u05d6 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7\u05d9\u05dd \u05d4\u05d9\u05e0\u05d4 \u05d0\u05d7\u05ea \u05d4\u05d7\u05d1\u05e8\u05d5\u05ea \u05d4\u05de\u05d5\u05d1\u05d9\u05dc\u05d5\u05ea \u05d1\u05d9\u05e9\u05e8\u05d0\u05dc \u05d1\u05ea\u05d7\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05e4\u05e8\u05d8\u05d9 \u05d5\u05d4\u05e2\u05e1\u05e7\u05d9. \u05d0\u05e0\u05d5 \u05de\u05e1\u05e4\u05e7\u05d9\u05dd \u05de\u05d2\u05d5\u05d5\u05df \u05e4\u05ea\u05e8\u05d5\u05e0\u05d5\u05ea \u05e2\u05d1\u05d5\u05e8 \u05e7\u05e9\u05ea \u05e8\u05d7\u05d1\u05d4 \u05e9\u05dc \u05dc\u05e7\u05d5\u05d7\u05d5\u05ea \u05de\u05db\u05dc \u05e8\u05d7\u05d1\u05d9 \u05d4\u05d0\u05e8\u05e5.",
        agency_name:
          "\u05de\u05e8\u05db\u05d6 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7\u05d9\u05dd",
        email: "<EMAIL>",
        phone: "**********",
        site_phone: "073-3745413",
        listing_image:
          "https://trusty.co.il/uploads/agents/listings/agent-listing6000b3236dd60.png",
        address_city: "\u05d1\u05d0\u05e8 \u05e9\u05d1\u05e2",
        company_img_name: null,
        avg_review: "9.96",
        total_reviews: 10,
        agent_areas: [1, 2, 3, 4],
        total_price: null,
        average_month_price: 51.09,
        company_id: 7,
        discount_first_year: "0.0",
        discount_for_all_years: [
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
        ],
        discount_for_rest_of_the_years: "0.0",
        sort_by_period: "first_year_sum",
        benefits: [
          {
            id: 6,
            agent_id: 25,
            name: "\u05e9\u05d9\u05d7\u05ea \u05d9\u05e2\u05d5\u05e5 \u05d7\u05d9\u05e0\u05dd",
            details:
              "\u05d1\u05d3\u05d9\u05e7\u05ea \u05ea\u05d9\u05e7 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05d9\u05e0\u05dd",
            active: 1,
            created_at: "2019-09-30T12:29:14.000000Z",
            updated_at: "2020-12-30T14:31:57.000000Z",
            pivot: { agent_mortgage_bid_id: 6260, benefit_id: 6 },
          },
        ],
        the_user_request_parameters: {
          agent_id: 25,
          provider_id: 25,
          number_of_insured: "individual",
          age1: "35",
          gender1: "female",
          is_smoking1: false,
          date_of_birth1: "1988-07-07T16:22:22.000000Z",
          health_condition1: "excellent",
          tracks:
            '[{"desired_sum":"1000000","interest_type":"temporary","desired_period":"7","interest_rate":"0.001"},{"desired_sum":"1000000","interest_type":"permanent","desired_period":"8","interest_rate":"2"}]',
          total_loan: 2000000,
          user_id: null,
          ip: "**************",
          screen_width: "2034",
          screen_height: "532",
          sort_by_param: "price_asc",
          sort_by_period: "first_year_sum",
          area_id: "0",
          updated_at: "2023-07-03T16:22:22.000000Z",
          created_at: "2023-07-03T16:22:22.000000Z",
          id: 3966,
        },
        results_form_code: "s9879822",
        token: "8chpsoksg8",
        companies_offer: [
          "[100,90.938,78.096,65.8046,53.3939,38.5426,24.5543,8.4378]",
        ],
        companies_offer_after_agent_discount: 100,
        bid_price_total_years: 5517.21,
        companies_offer_for_all_years_arr: [
          {
            company_one_year_offer: 100,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 100,
            company_offer_after_agent_discount_for_year: 1200,
            incremental_cost: 1200,
            incremental_year: 1,
          },
          {
            company_one_year_offer: 90.94,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 90.94,
            company_offer_after_agent_discount_for_year: 1091.26,
            incremental_cost: 2291.26,
            incremental_year: 2,
          },
          {
            company_one_year_offer: 78.1,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 78.1,
            company_offer_after_agent_discount_for_year: 937.15,
            incremental_cost: 3228.41,
            incremental_year: 3,
          },
          {
            company_one_year_offer: 65.8,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 65.8,
            company_offer_after_agent_discount_for_year: 789.66,
            incremental_cost: 4018.06,
            incremental_year: 4,
          },
          {
            company_one_year_offer: 53.39,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 53.39,
            company_offer_after_agent_discount_for_year: 640.73,
            incremental_cost: 4658.79,
            incremental_year: 5,
          },
          {
            company_one_year_offer: 38.54,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 38.54,
            company_offer_after_agent_discount_for_year: 462.51,
            incremental_cost: 5121.3,
            incremental_year: 6,
          },
          {
            company_one_year_offer: 24.55,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 24.55,
            company_offer_after_agent_discount_for_year: 294.65,
            incremental_cost: 5415.95,
            incremental_year: 7,
          },
          {
            company_one_year_offer: 8.44,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 8.44,
            company_offer_after_agent_discount_for_year: 101.25,
            incremental_cost: 5517.21,
            incremental_year: 8,
          },
        ],
        offer_price: 100,
        b_first_year: 100,
        featured_terms: [],
        company_name: "\u05d4\u05db\u05e9\u05e8\u05d4",
      },
      {
        id: 32,
        agent_id: 32,
        provider_id: 25,
        product_id: 215,
        policy_id: 7,
        mortgage_insurance_request_id: 3966,
        about_us:
          '"\u05d4\u05e9\u05e7\u05d8 \u05e9\u05dc\u05da \u05d4\u05d5\u05d0 \u05d4\u05d4\u05e6\u05dc\u05d7\u05d4 \u05e9\u05dc\u05e0\u05d5 "\r\n\u05d0\u05e0\u05d5 \u05e1\u05d5\u05db\u05e0\u05d5\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05de\u05e9\u05e4\u05d7\u05ea\u05d9\u05ea \u05d5\u05d0\u05d9\u05e0\u05d8\u05d9\u05de\u05d9\u05ea \u05e9\u05d4\u05e9\u05d9\u05e8\u05d5\u05ea \u05d4\u05d0\u05d9\u05e9\u05d9 \u05d4\u05d5\u05d0 \u05e0\u05e8 \u05dc\u05e8\u05d2\u05dc\u05d9\u05e0\u05d5 .',
        agency_name: "\u05d3\u05e8\u05d5\u05e8 \u05e0\u05d7\u05e9\u05d5\u05df",
        email: "<EMAIL>",
        phone: "09-9533030",
        site_phone: "073-3745409",
        listing_image:
          "https://trusty.co.il/uploads/agents/listings/agent-listing6000b82900945.png",
        address_city: "\u05d4\u05e8\u05e6\u05dc\u05d9\u05d4",
        company_img_name: null,
        avg_review: "0.00",
        total_reviews: 0,
        agent_areas: [2, 3],
        total_price: null,
        average_month_price: 70.46,
        company_id: 2,
        discount_first_year: "0.0",
        discount_for_all_years: [
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
        ],
        discount_for_rest_of_the_years: "0.0",
        sort_by_period: "first_year_sum",
        benefits: [
          {
            id: 11,
            agent_id: 32,
            name: "\u05e9\u05d9\u05d7\u05ea \u05d9\u05e2\u05d5\u05e5 \u05d7\u05d9\u05e0\u05dd",
            details:
              "\u05d1\u05d3\u05d9\u05e7\u05ea \u05ea\u05d9\u05e7 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05d9\u05e0\u05dd",
            active: 1,
            created_at: "2019-09-30T12:29:14.000000Z",
            updated_at: "2020-12-30T14:31:57.000000Z",
            pivot: { agent_mortgage_bid_id: 215, benefit_id: 11 },
          },
        ],
        the_user_request_parameters: {
          agent_id: 25,
          provider_id: 25,
          number_of_insured: "individual",
          age1: "35",
          gender1: "female",
          is_smoking1: false,
          date_of_birth1: "1988-07-07T16:22:22.000000Z",
          health_condition1: "excellent",
          tracks:
            '[{"desired_sum":"1000000","interest_type":"temporary","desired_period":"7","interest_rate":"0.001"},{"desired_sum":"1000000","interest_type":"permanent","desired_period":"8","interest_rate":"2"}]',
          total_loan: 2000000,
          user_id: null,
          ip: "**************",
          screen_width: "2034",
          screen_height: "532",
          sort_by_param: "price_asc",
          sort_by_period: "first_year_sum",
          area_id: "0",
          updated_at: "2023-07-03T16:22:22.000000Z",
          created_at: "2023-07-03T16:22:22.000000Z",
          id: 3966,
        },
        results_form_code: "s9879822",
        token: "pouqihhe6a",
        companies_offer: [
          "[130,119.0796,107.4382,93.3609,76.7975,58.2622,37.0142,12.1879]",
        ],
        companies_offer_after_agent_discount: 130,
        bid_price_total_years: 7609.69,
        companies_offer_for_all_years_arr: [
          {
            company_one_year_offer: 130,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 130,
            company_offer_after_agent_discount_for_year: 1560,
            incremental_cost: 1560,
            incremental_year: 1,
          },
          {
            company_one_year_offer: 119.08,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 119.08,
            company_offer_after_agent_discount_for_year: 1428.96,
            incremental_cost: 2988.96,
            incremental_year: 2,
          },
          {
            company_one_year_offer: 107.44,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 107.44,
            company_offer_after_agent_discount_for_year: 1289.26,
            incremental_cost: 4278.21,
            incremental_year: 3,
          },
          {
            company_one_year_offer: 93.36,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 93.36,
            company_offer_after_agent_discount_for_year: 1120.33,
            incremental_cost: 5398.54,
            incremental_year: 4,
          },
          {
            company_one_year_offer: 76.8,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 76.8,
            company_offer_after_agent_discount_for_year: 921.57,
            incremental_cost: 6320.11,
            incremental_year: 5,
          },
          {
            company_one_year_offer: 58.26,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 58.26,
            company_offer_after_agent_discount_for_year: 699.15,
            incremental_cost: 7019.26,
            incremental_year: 6,
          },
          {
            company_one_year_offer: 37.01,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 37.01,
            company_offer_after_agent_discount_for_year: 444.17,
            incremental_cost: 7463.43,
            incremental_year: 7,
          },
          {
            company_one_year_offer: 12.19,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 12.19,
            company_offer_after_agent_discount_for_year: 146.25,
            incremental_cost: 7609.69,
            incremental_year: 8,
          },
        ],
        offer_price: 130,
        b_first_year: 130,
        featured_terms: {
          "2": {
            id: 45,
            policy_id: 7,
            company_id: 2,
            condition_section_id: 6,
            text: "<ul><li>\u05e2\u05d3 \u05d7\u05d5\u05d3\u05e9\u05d9\u05d9\u05dd \u05e8\u05d0\u05e9\u05d5\u05e0\u05d9\u05dd \u05e9\u05dc \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05dc\u05d0 \u05ea\u05e9\u05dc\u05d5\u05dd </li><li>\u05d0\u05e4\u05e9\u05e8\u05d5\u05ea \u05dc\u05d1\u05d8\u05d7 \u05d0\u05ea \u05d9\u05ea\u05e8\u05ea \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d1\u05e2\u05dc\u05d5\u05ea \u05e0\u05d5\u05d7\u05d4</li><li> \u05d0\u05e4\u05e9\u05e8\u05d5\u05ea \u05dc\u05d1\u05d7\u05d5\u05e8 \u05d1\u05d9\u05df \u05d3\u05de\u05d9 \u05d1\u05d9\u05d8\u05d5\u05d7 \u05e7\u05d1\u05d5\u05e2\u05d9\u05dd \u05d5\u05d9\u05d3\u05d5\u05e2\u05d9\u05dd \u05de\u05e8\u05d0\u05e9 \u05d0\u05d5 \u05d1\u05d3\u05de\u05d9 \u05d1\u05d9\u05d8\u05d5\u05d7 \u05de\u05e9\u05ea\u05e0\u05d9\u05dd \u05d1- 1-3 \u05e9\u05e0\u05d9\u05dd \u05d4\u05d0\u05d7\u05e8\u05d5\u05e0\u05d5\u05ea \u05dc\u05ea\u05e9\u05dc\u05d5\u05dd \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0, </li><li>\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05de\u05e1\u05dc\u05d5\u05dc \u05d4\u05e7\u05d1\u05d5\u05e2 \u05d1\u05d7\u05d9\u05e0\u05dd, \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05de\u05e9\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d4\u05e6\u05d8\u05e8\u05e4\u05d5\u05ea \u05dc\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05d0\u05e4\u05e9\u05e8\u05d9\u05ea \u05de\u05d2\u05d9\u05dc 18 \u05e2\u05d3 70.</li><li> \u05ea\u05d5\u05e7\u05e3 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05e2\u05d3 \u05d2\u05d9\u05dc 80.</li></ul>",
            term: "\u05e2\u05d3 \u05d7\u05d5\u05d3\u05e9\u05d9\u05d9\u05dd \u05e8\u05d0\u05e9\u05d5\u05e0\u05d9\u05dd \u05e9\u05dc \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05dc\u05d0 \u05ea\u05e9\u05dc\u05d5\u05dd \u05d0\u05e4\u05e9\u05e8\u05d5\u05ea \u05dc\u05d1\u05d8\u05d7 \u05d0\u05ea \u05d9\u05ea\u05e8\u05ea \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d1\u05e2\u05dc\u05d5\u05ea \u05e0\u05d5\u05d7\u05d4 \u05d0\u05e4\u05e9\u05e8\u05d5\u05ea \u05dc\u05d1\u05d7\u05d5\u05e8 \u05d1\u05d9\u05df \u05d3\u05de\u05d9 \u05d1\u05d9\u05d8\u05d5\u05d7 \u05e7\u05d1\u05d5\u05e2\u05d9\u05dd \u05d5\u05d9\u05d3\u05d5\u05e2\u05d9\u05dd \u05de\u05e8\u05d0\u05e9 \u05d0\u05d5 \u05d1\u05d3\u05de\u05d9 \u05d1\u05d9\u05d8\u05d5\u05d7 \u05de\u05e9\u05ea\u05e0\u05d9\u05dd \u05d1- 1-3 \u05e9\u05e0\u05d9\u05dd \u05d4\u05d0\u05d7\u05e8\u05d5\u05e0\u05d5\u05ea \u05dc\u05ea\u05e9\u05dc\u05d5\u05dd \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0, \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05de\u05e1\u05dc\u05d5\u05dc \u05d4\u05e7\u05d1\u05d5\u05e2 \u05d1\u05d7\u05d9\u05e0\u05dd, \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05de\u05e9\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d4\u05e6\u05d8\u05e8\u05e4\u05d5\u05ea \u05dc\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05d0\u05e4\u05e9\u05e8\u05d9\u05ea \u05de\u05d2\u05d9\u05dc 18 \u05e2\u05d3 70. \u05ea\u05d5\u05e7\u05e3 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05e2\u05d3 \u05d2\u05d9\u05dc 80.",
            number: null,
            featured: 1,
            short:
              "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05db\u05d9\u05e1\u05d5\u05d9 \u05d2\u05d5\u05d1\u05d4 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
            note: null,
            created_at: "2020-04-08 11:42:57",
            updated_at: "2023-03-15 17:44:28",
          },
          "3": {
            id: 46,
            policy_id: 7,
            company_id: 2,
            condition_section_id: 7,
            text: '<ul><li><span style="font-size: 1rem;">&nbsp;\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9&nbsp; - </span><b style="font-size: 1rem;">78</b></li><li><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05ea\u05d1\u05d9\u05e2\u05d5\u05ea -&nbsp; </span><b style="font-size: 1rem;">77</b></li><li><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - </span><b style="font-size: 1rem;">76</b></li></ul>',
            term: "<div>&nbsp;\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9&nbsp; - <b>80</b></div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05ea\u05d1\u05d9\u05e2\u05d5\u05ea -&nbsp; 77</div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - 76</div>",
            number: null,
            featured: 1,
            short:
              "\u05de\u05d3\u05d3 \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 80",
            note: null,
            created_at: "2020-04-08 11:43:28",
            updated_at: "2023-06-13 11:36:16",
          },
        },
        company_name: "\u05d4\u05e8\u05d0\u05dc",
      },
      {
        id: 39,
        agent_id: 39,
        provider_id: 25,
        product_id: 603,
        policy_id: 7,
        mortgage_insurance_request_id: 3966,
        about_us:
          '\u05d0.\u05e9.\u05d0\u05d9\u05e0\u05e9\u05d5\u05e8 \u05e1\u05d5\u05db\u05e0\u05d5\u05ea \u05dc\u05d1\u05d9\u05d8\u05d5\u05d7 (2002) \u05d1\u05e2"\u05de, \u05d4\u05e1\u05d5\u05db\u05e0\u05d5\u05ea \u05e4\u05e2\u05d9\u05dc\u05d4 \u05de\u05e2\u05dc 30 \u05e9\u05e0\u05d4 \u05d1\u05db\u05dc \u05e2\u05e0\u05e4\u05d9 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05db\u05d0\u05e9\u05e8 \u05d0\u05d9\u05db\u05d5\u05ea \u05d4\u05e9\u05d9\u05e8\u05d5\u05ea \u05dc\u05dc\u05e7\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d4 \u05d4\u05d9\u05e2\u05d3 \u05d4\u05de\u05e8\u05db\u05d6\u05d9.\r\n\u05d2\u05d0\u05d5\u05d5\u05ea\u05e0\u05d5 \u05db\u05e6\u05d5\u05d5\u05ea \u05d4\u05d9\u05d0 \u05d4\u05d9\u05db\u05d5\u05dc\u05ea \u05dc\u05e1\u05e4\u05e7 \u05e9\u05d9\u05e8\u05d5\u05ea  \u05d1\u05e8\u05de\u05d4 \u05d4\u05de\u05e7\u05e6\u05d5\u05e2\u05d9\u05ea \u05d4\u05d2\u05d1\u05d5\u05d4\u05d4 \u05d1\u05d9\u05d5\u05ea\u05e8',
        agency_name:
          '\u05d0.\u05e9. \u05d0\u05d9\u05e0\u05e9\u05d5\u05e8 \u05e1\u05d5\u05db\u05e0\u05d5\u05ea \u05dc\u05d1\u05d9\u05d8\u05d5\u05d7 (2002) \u05d1\u05e2"\u05de',
        email: "<EMAIL>",
        phone: "09-7676806",
        site_phone: "073-3745401",
        listing_image:
          "https://trusty.co.il/uploads/agents/listings/agent-listing6000b16b2d198.png",
        address_city: "\u05db\u05e4\u05e8 \u05e1\u05d1\u05d0",
        company_img_name: null,
        avg_review: "0.00",
        total_reviews: 0,
        agent_areas: [2, 3],
        total_price: null,
        average_month_price: 70.46,
        company_id: 2,
        discount_first_year: "0.0",
        discount_for_all_years: [
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
          "0.0",
        ],
        discount_for_rest_of_the_years: "0.0",
        sort_by_period: "first_year_sum",
        benefits: [
          {
            id: 16,
            agent_id: 39,
            name: "\u05e9\u05d9\u05d7\u05ea \u05d9\u05e2\u05d5\u05e5 \u05d7\u05d9\u05e0\u05dd",
            details:
              "\u05d1\u05d3\u05d9\u05e7\u05ea \u05ea\u05d9\u05e7 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05d9\u05e0\u05dd",
            active: 1,
            created_at: "2019-09-30T12:29:14.000000Z",
            updated_at: "2020-12-30T14:31:57.000000Z",
            pivot: { agent_mortgage_bid_id: 603, benefit_id: 16 },
          },
        ],
        the_user_request_parameters: {
          agent_id: 25,
          provider_id: 25,
          number_of_insured: "individual",
          age1: "35",
          gender1: "female",
          is_smoking1: false,
          date_of_birth1: "1988-07-07T16:22:22.000000Z",
          health_condition1: "excellent",
          tracks:
            '[{"desired_sum":"1000000","interest_type":"temporary","desired_period":"7","interest_rate":"0.001"},{"desired_sum":"1000000","interest_type":"permanent","desired_period":"8","interest_rate":"2"}]',
          total_loan: 2000000,
          user_id: null,
          ip: "**************",
          screen_width: "2034",
          screen_height: "532",
          sort_by_param: "price_asc",
          sort_by_period: "first_year_sum",
          area_id: "0",
          updated_at: "2023-07-03T16:22:22.000000Z",
          created_at: "2023-07-03T16:22:22.000000Z",
          id: 3966,
        },
        results_form_code: "s9879822",
        token: "perwxcgzj5",
        companies_offer: [
          "[130,119.0796,107.4382,93.3609,76.7975,58.2622,37.0142,12.1879]",
        ],
        companies_offer_after_agent_discount: 130,
        bid_price_total_years: 7609.69,
        companies_offer_for_all_years_arr: [
          {
            company_one_year_offer: 130,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 130,
            company_offer_after_agent_discount_for_year: 1560,
            incremental_cost: 1560,
            incremental_year: 1,
          },
          {
            company_one_year_offer: 119.08,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 119.08,
            company_offer_after_agent_discount_for_year: 1428.96,
            incremental_cost: 2988.96,
            incremental_year: 2,
          },
          {
            company_one_year_offer: 107.44,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 107.44,
            company_offer_after_agent_discount_for_year: 1289.26,
            incremental_cost: 4278.21,
            incremental_year: 3,
          },
          {
            company_one_year_offer: 93.36,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 93.36,
            company_offer_after_agent_discount_for_year: 1120.33,
            incremental_cost: 5398.54,
            incremental_year: 4,
          },
          {
            company_one_year_offer: 76.8,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 76.8,
            company_offer_after_agent_discount_for_year: 921.57,
            incremental_cost: 6320.11,
            incremental_year: 5,
          },
          {
            company_one_year_offer: 58.26,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 58.26,
            company_offer_after_agent_discount_for_year: 699.15,
            incremental_cost: 7019.26,
            incremental_year: 6,
          },
          {
            company_one_year_offer: 37.01,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 37.01,
            company_offer_after_agent_discount_for_year: 444.17,
            incremental_cost: 7463.43,
            incremental_year: 7,
          },
          {
            company_one_year_offer: 12.19,
            agent_discount_for_that_year: 0,
            agent_discount_for_that_year_in_money: 0,
            company_offer_after_agent_discount_for_month: 12.19,
            company_offer_after_agent_discount_for_year: 146.25,
            incremental_cost: 7609.69,
            incremental_year: 8,
          },
        ],
        offer_price: 130,
        b_first_year: 130,
        featured_terms: {
          "2": {
            id: 45,
            policy_id: 7,
            company_id: 2,
            condition_section_id: 6,
            text: "<ul><li>\u05e2\u05d3 \u05d7\u05d5\u05d3\u05e9\u05d9\u05d9\u05dd \u05e8\u05d0\u05e9\u05d5\u05e0\u05d9\u05dd \u05e9\u05dc \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05dc\u05d0 \u05ea\u05e9\u05dc\u05d5\u05dd </li><li>\u05d0\u05e4\u05e9\u05e8\u05d5\u05ea \u05dc\u05d1\u05d8\u05d7 \u05d0\u05ea \u05d9\u05ea\u05e8\u05ea \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d1\u05e2\u05dc\u05d5\u05ea \u05e0\u05d5\u05d7\u05d4</li><li> \u05d0\u05e4\u05e9\u05e8\u05d5\u05ea \u05dc\u05d1\u05d7\u05d5\u05e8 \u05d1\u05d9\u05df \u05d3\u05de\u05d9 \u05d1\u05d9\u05d8\u05d5\u05d7 \u05e7\u05d1\u05d5\u05e2\u05d9\u05dd \u05d5\u05d9\u05d3\u05d5\u05e2\u05d9\u05dd \u05de\u05e8\u05d0\u05e9 \u05d0\u05d5 \u05d1\u05d3\u05de\u05d9 \u05d1\u05d9\u05d8\u05d5\u05d7 \u05de\u05e9\u05ea\u05e0\u05d9\u05dd \u05d1- 1-3 \u05e9\u05e0\u05d9\u05dd \u05d4\u05d0\u05d7\u05e8\u05d5\u05e0\u05d5\u05ea \u05dc\u05ea\u05e9\u05dc\u05d5\u05dd \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0, </li><li>\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05de\u05e1\u05dc\u05d5\u05dc \u05d4\u05e7\u05d1\u05d5\u05e2 \u05d1\u05d7\u05d9\u05e0\u05dd, \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05de\u05e9\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d4\u05e6\u05d8\u05e8\u05e4\u05d5\u05ea \u05dc\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05d0\u05e4\u05e9\u05e8\u05d9\u05ea \u05de\u05d2\u05d9\u05dc 18 \u05e2\u05d3 70.</li><li> \u05ea\u05d5\u05e7\u05e3 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05e2\u05d3 \u05d2\u05d9\u05dc 80.</li></ul>",
            term: "\u05e2\u05d3 \u05d7\u05d5\u05d3\u05e9\u05d9\u05d9\u05dd \u05e8\u05d0\u05e9\u05d5\u05e0\u05d9\u05dd \u05e9\u05dc \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05dc\u05d0 \u05ea\u05e9\u05dc\u05d5\u05dd \u05d0\u05e4\u05e9\u05e8\u05d5\u05ea \u05dc\u05d1\u05d8\u05d7 \u05d0\u05ea \u05d9\u05ea\u05e8\u05ea \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d1\u05e2\u05dc\u05d5\u05ea \u05e0\u05d5\u05d7\u05d4 \u05d0\u05e4\u05e9\u05e8\u05d5\u05ea \u05dc\u05d1\u05d7\u05d5\u05e8 \u05d1\u05d9\u05df \u05d3\u05de\u05d9 \u05d1\u05d9\u05d8\u05d5\u05d7 \u05e7\u05d1\u05d5\u05e2\u05d9\u05dd \u05d5\u05d9\u05d3\u05d5\u05e2\u05d9\u05dd \u05de\u05e8\u05d0\u05e9 \u05d0\u05d5 \u05d1\u05d3\u05de\u05d9 \u05d1\u05d9\u05d8\u05d5\u05d7 \u05de\u05e9\u05ea\u05e0\u05d9\u05dd \u05d1- 1-3 \u05e9\u05e0\u05d9\u05dd \u05d4\u05d0\u05d7\u05e8\u05d5\u05e0\u05d5\u05ea \u05dc\u05ea\u05e9\u05dc\u05d5\u05dd \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0, \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05de\u05e1\u05dc\u05d5\u05dc \u05d4\u05e7\u05d1\u05d5\u05e2 \u05d1\u05d7\u05d9\u05e0\u05dd, \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05de\u05e9\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d4\u05e6\u05d8\u05e8\u05e4\u05d5\u05ea \u05dc\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05d0\u05e4\u05e9\u05e8\u05d9\u05ea \u05de\u05d2\u05d9\u05dc 18 \u05e2\u05d3 70. \u05ea\u05d5\u05e7\u05e3 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05e2\u05d3 \u05d2\u05d9\u05dc 80.",
            number: null,
            featured: 1,
            short:
              "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05db\u05d9\u05e1\u05d5\u05d9 \u05d2\u05d5\u05d1\u05d4 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
            note: null,
            created_at: "2020-04-08 11:42:57",
            updated_at: "2023-03-15 17:44:28",
          },
          "3": {
            id: 46,
            policy_id: 7,
            company_id: 2,
            condition_section_id: 7,
            text: '<ul><li><span style="font-size: 1rem;">&nbsp;\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9&nbsp; - </span><b style="font-size: 1rem;">78</b></li><li><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05ea\u05d1\u05d9\u05e2\u05d5\u05ea -&nbsp; </span><b style="font-size: 1rem;">77</b></li><li><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - </span><b style="font-size: 1rem;">76</b></li></ul>',
            term: "<div>&nbsp;\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9&nbsp; - <b>80</b></div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05ea\u05d1\u05d9\u05e2\u05d5\u05ea -&nbsp; 77</div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - 76</div>",
            number: null,
            featured: 1,
            short:
              "\u05de\u05d3\u05d3 \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 80",
            note: null,
            created_at: "2020-04-08 11:43:28",
            updated_at: "2023-06-13 11:36:16",
          },
        },
        company_name: "\u05d4\u05e8\u05d0\u05dc",
      },
    ],
    form_params: {
      agent_id: 25,
      provider_id: 25,
      number_of_insured: "individual",
      age1: "35",
      gender1: "female",
      is_smoking1: false,
      date_of_birth1: "1988-07-07T16:22:22.000000Z",
      health_condition1: "excellent",
      tracks:
        '[{"desired_sum":"1000000","interest_type":"temporary","desired_period":"7","interest_rate":"0.001"},{"desired_sum":"1000000","interest_type":"permanent","desired_period":"8","interest_rate":"2"}]',
      total_loan: 2000000,
      user_id: null,
      ip: "**************",
      screen_width: "2034",
      screen_height: "532",
      sort_by_param: "price_asc",
      sort_by_period: "first_year_sum",
      area_id: "0",
      updated_at: "2023-07-03T16:22:22.000000Z",
      created_at: "2023-07-03T16:22:22.000000Z",
      id: 3966,
    },
    companies_conditions: {
      "1": {
        id: 1,
        name: "\u05de\u05d2\u05d3\u05dc",
        sections: [
          {
            id: 6,
            name: "\u05ea\u05e0\u05d0\u05d9\u05dd \u05db\u05dc\u05dc\u05d9\u05dd",
            view_order: 1,
            terms: [
              {
                id: 40,
                policy_id: 7,
                company_id: 1,
                condition_section_id: 6,
                text: "<ul>\n<li>\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.</li>\n<li>\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.</li>\n<li>\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.</li></ul>",
                term: "\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05ea\u05db\u05e0\u05d9\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05dc\u05d9\u05dc\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d1\u05e0\u05e7, \u05dc\u05e6\u05d5\u05e8\u05da \u05db\u05d9\u05e1\u05d5\u05d9 \u05d7\u05d5\u05d1 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05d4\u05d9\u05ea\u05e8\u05d4 (\u05d0\u05dd \u05ea\u05d4\u05d9\u05d4) \u05ea\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd.\n\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05ea\u05d2\u05d1\u05d4 \u05de\u05d3\u05d9 \u05d7\u05d5\u05d3\u05e9 \u05d1\u05d7\u05d5\u05d3\u05e9\u05d5 \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05d5\u05e8\u05d0\u05ea \u05e7\u05d1\u05e2 \u05d0\u05d5 \u05db\u05e8\u05d8\u05d9\u05e1 \u05d0\u05e9\u05e8\u05d0\u05d9 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d1\u05d7\u05d9\u05e8\u05ea\u05da.\n\u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05e1\u05ea\u05d9\u05d9\u05dd \u05d1\u05de\u05d5\u05e2\u05d3 \u05ea\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d0\u05d5 \u05d1\u05ea\u05d0\u05e8\u05d9\u05da \u05d2\u05de\u05e8 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05d1\u05d4\u05d2\u05d9\u05e2\u05da \u05dc\u05d2\u05d9\u05dc 85 - \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d5\u05e7\u05d3\u05dd \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd.",
                number: null,
                featured: 1,
                short:
                  "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05db\u05d9\u05e1\u05d5\u05d9 \u05d2\u05d5\u05d1\u05d4 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
                note: null,
              },
            ],
          },
          {
            id: 7,
            name: "\u05e6\u05d9\u05d5\u05df \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7",
            view_order: 2,
            terms: [
              {
                id: 41,
                policy_id: 7,
                company_id: 1,
                condition_section_id: 7,
                text: '<span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - </span><b style="font-size: 1rem;">78</b><br><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - </span><b style="font-size: 1rem;">74</b><br><span style="font-size: 1rem;">&nbsp;\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - </span><b style="font-size: 1rem;">81</b><br>',
                term: "<div>\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - <b>84</b></div><div>\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - 74&nbsp;</div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - 81</div>",
                number: null,
                featured: 1,
                short:
                  "\u05de\u05d3\u05d3 \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 84",
                note: null,
              },
            ],
          },
          {
            id: 8,
            name: "\u05d7\u05e9\u05d5\u05d1 \u05dc\u05d3\u05e2\u05ea",
            view_order: 3,
            terms: [
              {
                id: 43,
                policy_id: 7,
                company_id: 1,
                condition_section_id: 8,
                text: "<ul><li>\u05d2\u05d1\u05d5\u05dc\u05d5\u05ea \u05d0\u05d7\u05e8\u05d9\u05d5\u05ea, \u05d7\u05e8\u05d9\u05d2\u05d9\u05dd \u05d5\u05d4\u05d2\u05d1\u05dc\u05d5\u05ea \u05dc\u05d7\u05d1\u05d5\u05ea \u05d4\u05d7\u05d1\u05e8\u05d4: \u05d4\u05d7\u05d1\u05e8\u05d4 \u05ea\u05d4\u05d9\u05d4 \u05e4\u05d8\u05d5\u05e8\u05d4 \u05de\u05d7\u05d1\u05d5\u05ea\u05d4 \u05dc\u05ea\u05e9\u05dc\u05d5\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05dd \u05e0\u05d2\u05e8\u05dd \u05de\u05d5\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05e2\u05e7\u05d1 \u05d4\u05ea\u05d0\u05d1\u05d3\u05d5\u05ea (\u05d1\u05d9\u05df \u05d0\u05dd \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d4\u05d9\u05d4 \u05e9\u05e4\u05d5\u05d9 \u05d1\u05d3\u05e2\u05ea\u05d5 \u05d5\u05d1\u05d9\u05df \u05d0\u05dd \u05dc\u05d0\u05d5) \u05d1\u05ea\u05d5\u05da \u05e9\u05e0\u05d4 \u05de\u05d9\u05d5\u05dd \u05db\u05e8\u05d9\u05ea\u05ea \u05d7\u05d5\u05d6\u05d4 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7, \u05d0\u05d5 \u05de\u05d9\u05d5\u05dd \u05d7\u05d9\u05d3\u05d5\u05e9 \u05d4\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4, \u05d4\u05db\u05dc \u05dc\u05e4\u05d9 \u05d4\u05ea\u05d0\u05e8\u05d9\u05da \u05d4\u05de\u05d0\u05d5\u05d7\u05e8 \u05d9\u05d5\u05ea\u05e8. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e9\u05dc \u05d4\u05d2\u05d3\u05dc\u05d4 \u05e9\u05dc \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d9\u05d7\u05d5\u05dc \u05d4\u05d0\u05de\u05d5\u05e8 \u05d1\u05e1\u05e2\u05d9\u05e3 \u05d6\u05d4 \u05dc\u05d2\u05d1\u05d9 \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05e0\u05d5\u05e1\u05e3, \u05d4\u05d7\u05dc \u05de\u05de\u05d5\u05e2\u05d3 \u05db\u05e8\u05d9\u05ea\u05ea \u05d7\u05d5\u05d6\u05d4 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d2\u05d1\u05d9 \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05e0\u05d5\u05e1\u05e3.</li></ul>",
                term: "\u05d2\u05d1\u05d5\u05dc\u05d5\u05ea \u05d0\u05d7\u05e8\u05d9\u05d5\u05ea, \u05d7\u05e8\u05d9\u05d2\u05d9\u05dd \u05d5\u05d4\u05d2\u05d1\u05dc\u05d5\u05ea \u05dc\u05d7\u05d1\u05d5\u05ea \u05d4\u05d7\u05d1\u05e8\u05d4: \u05d4\u05d7\u05d1\u05e8\u05d4 \u05ea\u05d4\u05d9\u05d4 \u05e4\u05d8\u05d5\u05e8\u05d4 \u05de\u05d7\u05d1\u05d5\u05ea\u05d4 \u05dc\u05ea\u05e9\u05dc\u05d5\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05dd \u05e0\u05d2\u05e8\u05dd \u05de\u05d5\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05e2\u05e7\u05d1 \u05d4\u05ea\u05d0\u05d1\u05d3\u05d5\u05ea (\u05d1\u05d9\u05df \u05d0\u05dd \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d4\u05d9\u05d4 \u05e9\u05e4\u05d5\u05d9 \u05d1\u05d3\u05e2\u05ea\u05d5 \u05d5\u05d1\u05d9\u05df \u05d0\u05dd \u05dc\u05d0\u05d5) \u05d1\u05ea\u05d5\u05da \u05e9\u05e0\u05d4 \u05de\u05d9\u05d5\u05dd \u05db\u05e8\u05d9\u05ea\u05ea \u05d7\u05d5\u05d6\u05d4 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7, \u05d0\u05d5 \u05de\u05d9\u05d5\u05dd \u05d7\u05d9\u05d3\u05d5\u05e9 \u05d4\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4, \u05d4\u05db\u05dc \u05dc\u05e4\u05d9 \u05d4\u05ea\u05d0\u05e8\u05d9\u05da \u05d4\u05de\u05d0\u05d5\u05d7\u05e8 \u05d9\u05d5\u05ea\u05e8. \u05d1\u05de\u05e7\u05e8\u05d4 \u05e9\u05dc \u05d4\u05d2\u05d3\u05dc\u05d4 \u05e9\u05dc \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d9\u05d7\u05d5\u05dc \u05d4\u05d0\u05de\u05d5\u05e8 \u05d1\u05e1\u05e2\u05d9\u05e3 \u05d6\u05d4 \u05dc\u05d2\u05d1\u05d9 \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05e0\u05d5\u05e1\u05e3, \u05d4\u05d7\u05dc \u05de\u05de\u05d5\u05e2\u05d3 \u05db\u05e8\u05d9\u05ea\u05ea \u05d7\u05d5\u05d6\u05d4 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05d2\u05d1\u05d9 \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05e0\u05d5\u05e1\u05e3.",
                number: null,
                featured: null,
                short: null,
                note: null,
              },
            ],
          },
        ],
      },
      "2": {
        id: 2,
        name: "\u05d4\u05e8\u05d0\u05dc",
        sections: [
          {
            id: 6,
            name: "\u05ea\u05e0\u05d0\u05d9\u05dd \u05db\u05dc\u05dc\u05d9\u05dd",
            view_order: 1,
            terms: [
              {
                id: 45,
                policy_id: 7,
                company_id: 2,
                condition_section_id: 6,
                text: "<ul><li>\u05e2\u05d3 \u05d7\u05d5\u05d3\u05e9\u05d9\u05d9\u05dd \u05e8\u05d0\u05e9\u05d5\u05e0\u05d9\u05dd \u05e9\u05dc \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05dc\u05d0 \u05ea\u05e9\u05dc\u05d5\u05dd </li><li>\u05d0\u05e4\u05e9\u05e8\u05d5\u05ea \u05dc\u05d1\u05d8\u05d7 \u05d0\u05ea \u05d9\u05ea\u05e8\u05ea \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d1\u05e2\u05dc\u05d5\u05ea \u05e0\u05d5\u05d7\u05d4</li><li> \u05d0\u05e4\u05e9\u05e8\u05d5\u05ea \u05dc\u05d1\u05d7\u05d5\u05e8 \u05d1\u05d9\u05df \u05d3\u05de\u05d9 \u05d1\u05d9\u05d8\u05d5\u05d7 \u05e7\u05d1\u05d5\u05e2\u05d9\u05dd \u05d5\u05d9\u05d3\u05d5\u05e2\u05d9\u05dd \u05de\u05e8\u05d0\u05e9 \u05d0\u05d5 \u05d1\u05d3\u05de\u05d9 \u05d1\u05d9\u05d8\u05d5\u05d7 \u05de\u05e9\u05ea\u05e0\u05d9\u05dd \u05d1- 1-3 \u05e9\u05e0\u05d9\u05dd \u05d4\u05d0\u05d7\u05e8\u05d5\u05e0\u05d5\u05ea \u05dc\u05ea\u05e9\u05dc\u05d5\u05dd \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0, </li><li>\u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05de\u05e1\u05dc\u05d5\u05dc \u05d4\u05e7\u05d1\u05d5\u05e2 \u05d1\u05d7\u05d9\u05e0\u05dd, \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05de\u05e9\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d4\u05e6\u05d8\u05e8\u05e4\u05d5\u05ea \u05dc\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05d0\u05e4\u05e9\u05e8\u05d9\u05ea \u05de\u05d2\u05d9\u05dc 18 \u05e2\u05d3 70.</li><li> \u05ea\u05d5\u05e7\u05e3 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05e2\u05d3 \u05d2\u05d9\u05dc 80.</li></ul>",
                term: "\u05e2\u05d3 \u05d7\u05d5\u05d3\u05e9\u05d9\u05d9\u05dd \u05e8\u05d0\u05e9\u05d5\u05e0\u05d9\u05dd \u05e9\u05dc \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05dc\u05d0 \u05ea\u05e9\u05dc\u05d5\u05dd \u05d0\u05e4\u05e9\u05e8\u05d5\u05ea \u05dc\u05d1\u05d8\u05d7 \u05d0\u05ea \u05d9\u05ea\u05e8\u05ea \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d1\u05e2\u05dc\u05d5\u05ea \u05e0\u05d5\u05d7\u05d4 \u05d0\u05e4\u05e9\u05e8\u05d5\u05ea \u05dc\u05d1\u05d7\u05d5\u05e8 \u05d1\u05d9\u05df \u05d3\u05de\u05d9 \u05d1\u05d9\u05d8\u05d5\u05d7 \u05e7\u05d1\u05d5\u05e2\u05d9\u05dd \u05d5\u05d9\u05d3\u05d5\u05e2\u05d9\u05dd \u05de\u05e8\u05d0\u05e9 \u05d0\u05d5 \u05d1\u05d3\u05de\u05d9 \u05d1\u05d9\u05d8\u05d5\u05d7 \u05de\u05e9\u05ea\u05e0\u05d9\u05dd \u05d1- 1-3 \u05e9\u05e0\u05d9\u05dd \u05d4\u05d0\u05d7\u05e8\u05d5\u05e0\u05d5\u05ea \u05dc\u05ea\u05e9\u05dc\u05d5\u05dd \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0, \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05de\u05e1\u05dc\u05d5\u05dc \u05d4\u05e7\u05d1\u05d5\u05e2 \u05d1\u05d7\u05d9\u05e0\u05dd, \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05de\u05e9\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d4\u05e6\u05d8\u05e8\u05e4\u05d5\u05ea \u05dc\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05d0\u05e4\u05e9\u05e8\u05d9\u05ea \u05de\u05d2\u05d9\u05dc 18 \u05e2\u05d3 70. \u05ea\u05d5\u05e7\u05e3 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e0\u05d5 \u05e2\u05d3 \u05d2\u05d9\u05dc 80.",
                number: null,
                featured: 1,
                short:
                  "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05db\u05d9\u05e1\u05d5\u05d9 \u05d2\u05d5\u05d1\u05d4 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
                note: null,
              },
            ],
          },
          {
            id: 7,
            name: "\u05e6\u05d9\u05d5\u05df \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7",
            view_order: 2,
            terms: [
              {
                id: 46,
                policy_id: 7,
                company_id: 2,
                condition_section_id: 7,
                text: '<ul><li><span style="font-size: 1rem;">&nbsp;\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9&nbsp; - </span><b style="font-size: 1rem;">78</b></li><li><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05ea\u05d1\u05d9\u05e2\u05d5\u05ea -&nbsp; </span><b style="font-size: 1rem;">77</b></li><li><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - </span><b style="font-size: 1rem;">76</b></li></ul>',
                term: "<div>&nbsp;\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9&nbsp; - <b>80</b></div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05ea\u05d1\u05d9\u05e2\u05d5\u05ea -&nbsp; 77</div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - 76</div>",
                number: null,
                featured: 1,
                short:
                  "\u05de\u05d3\u05d3 \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 80",
                note: null,
              },
            ],
          },
          {
            id: 8,
            name: "\u05d7\u05e9\u05d5\u05d1 \u05dc\u05d3\u05e2\u05ea",
            view_order: 3,
            terms: [
              {
                id: 47,
                policy_id: 7,
                company_id: 2,
                condition_section_id: 8,
                text: "<ul><li>\u200b\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05d9\u05d9\u05dd \u05dc\u05de\u05e9\u05db\u05e0\u05ea\u05d0 \u05d4\u05d9\u05e0\u05d4 \u05e4\u05d5\u05dc\u05d9\u05e1\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea, \u05d4\u05de\u05d9\u05d5\u05e2\u05d3\u05ea \u05dc\u05d4\u05d1\u05d8\u05d7\u05ea \u05ea\u05e9\u05dc\u05d5\u05de\u05d9 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0 \u05dc\u05d1\u05e0\u05e7. \u05d4\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05de\u05d1\u05d8\u05d9\u05d7\u05d4 \u05db\u05d9 \u05d1\u05de\u05e7\u05e8\u05d4 \u05e9\u05dc \u05e4\u05d8\u05d9\u05e8\u05ea \u05d0\u05d7\u05d3 \u05d0\u05d5 \u05d9\u05d5\u05ea\u05e8 \u05de\u05d4\u05de\u05d1\u05d5\u05d8\u05d7\u05d9\u05dd \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0 \u05dc\u05d1\u05e0\u05e7 \u05d5\u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d4\u05e9\u05e0\u05d9 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 (\u05d1\u05de\u05d9\u05d3\u05d4 \u05d5\u05e7\u05d9\u05d9\u05dd) \u05dc\u05d0 \u05d9\u05d9\u05d3\u05e8\u05e9 \u05dc\u05d4\u05de\u05e9\u05da \u05ea\u05e9\u05dc\u05d5\u05de\u05d9 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0.</li></ul>",
                term: "\u200b\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05d9\u05d9\u05dd \u05dc\u05de\u05e9\u05db\u05e0\u05ea\u05d0 \u05d4\u05d9\u05e0\u05d4 \u05e4\u05d5\u05dc\u05d9\u05e1\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea, \u05d4\u05de\u05d9\u05d5\u05e2\u05d3\u05ea \u05dc\u05d4\u05d1\u05d8\u05d7\u05ea \u05ea\u05e9\u05dc\u05d5\u05de\u05d9 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0 \u05dc\u05d1\u05e0\u05e7. \u05d4\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05de\u05d1\u05d8\u05d9\u05d7\u05d4 \u05db\u05d9 \u05d1\u05de\u05e7\u05e8\u05d4 \u05e9\u05dc \u05e4\u05d8\u05d9\u05e8\u05ea \u05d0\u05d7\u05d3 \u05d0\u05d5 \u05d9\u05d5\u05ea\u05e8 \u05de\u05d4\u05de\u05d1\u05d5\u05d8\u05d7\u05d9\u05dd \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4, \u05d9\u05e9\u05d5\u05dc\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0 \u05dc\u05d1\u05e0\u05e7 \u05d5\u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d4\u05e9\u05e0\u05d9 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 (\u05d1\u05de\u05d9\u05d3\u05d4 \u05d5\u05e7\u05d9\u05d9\u05dd) \u05dc\u05d0 \u05d9\u05d9\u05d3\u05e8\u05e9 \u05dc\u05d4\u05de\u05e9\u05da \u05ea\u05e9\u05dc\u05d5\u05de\u05d9 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0.",
                number: null,
                featured: null,
                short: null,
                note: null,
              },
            ],
          },
        ],
      },
      "3": {
        id: 3,
        name: "\u05d0\u05d9\u05d9\u05dc\u05d5\u05df",
        sections: [
          {
            id: 6,
            name: "\u05ea\u05e0\u05d0\u05d9\u05dd \u05db\u05dc\u05dc\u05d9\u05dd",
            view_order: 1,
            terms: [
              {
                id: 48,
                policy_id: 7,
                company_id: 3,
                condition_section_id: 6,
                text: "<ul><li>\u05db\u05d9\u05e1\u05d5\u05d9 \u05dc\u05e1\u05db\u05d5\u05dd \u05d7\u05d3 \u05e4\u05e2\u05de\u05d9 \u05e9\u05d9\u05e9\u05d5\u05dc\u05dd \u05dc\u05d1\u05e0\u05e7 \u05d4\u05de\u05e9\u05e2\u05d1\u05d3 \u05d1\u05de\u05e7\u05e8\u05d4 \u05e9\u05dc \u05d0\u05d9\u05e8\u05d5\u05e2 \u05d1\u05d9\u05d8\u05d5\u05d7\u05d9 </li><li>\u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05e7\u05d8\u05df \u05de\u05d9\u05d3\u05d9 \u05e9\u05e0\u05d4 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d2\u05d5\u05d1\u05d4 \u05d4\u05e8\u05d9\u05d1\u05d9\u05ea \u05e9\u05e7\u05d9\u05d9\u05de\u05ea \u05d1\u05de\u05e1\u05dc\u05d5\u05dc \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0 \u05d5\u05dc\u05ea\u05e9\u05dc\u05d5\u05de\u05d9 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0 </li><li>\u05d7\u05d5\u05d1\u05d4 \u05dc\u05e8\u05db\u05d5\u05e9 \u05d2\u05dd \u05d1\u05d9\u05d8\u05d5\u05d7 \u05de\u05d1\u05e0\u05d4 \u05d3\u05d9\u05e8\u05d4 \u05dc\u05de\u05e9\u05db\u05e0\u05ea\u05d0 </li><li>\u05d4\u05e4\u05e7\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05de\u05e1\u05dc\u05d5\u05dc \u05d9\u05e8\u05d5\u05e7 \u05de\u05d4\u05d9\u05e8, \u05ea\u05d5\u05da 24 \u05e9\u05e2\u05d5\u05ea \u05de\u05e7\u05d1\u05dc\u05ea \u05d8\u05d5\u05e4\u05e1 \u05d4\u05d1\u05e7\u05e9\u05d4</li></ul>",
                term: "\u05db\u05d9\u05e1\u05d5\u05d9 \u05dc\u05e1\u05db\u05d5\u05dd \u05d7\u05d3 \u05e4\u05e2\u05de\u05d9 \u05e9\u05d9\u05e9\u05d5\u05dc\u05dd \u05dc\u05d1\u05e0\u05e7 \u05d4\u05de\u05e9\u05e2\u05d1\u05d3 \u05d1\u05de\u05e7\u05e8\u05d4 \u05e9\u05dc \u05d0\u05d9\u05e8\u05d5\u05e2 \u05d1\u05d9\u05d8\u05d5\u05d7\u05d9 \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05e7\u05d8\u05df \u05de\u05d9\u05d3\u05d9 \u05e9\u05e0\u05d4 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d2\u05d5\u05d1\u05d4 \u05d4\u05e8\u05d9\u05d1\u05d9\u05ea \u05e9\u05e7\u05d9\u05d9\u05de\u05ea \u05d1\u05de\u05e1\u05dc\u05d5\u05dc \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0 \u05d5\u05dc\u05ea\u05e9\u05dc\u05d5\u05de\u05d9 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0 \u05d7\u05d5\u05d1\u05d4 \u05dc\u05e8\u05db\u05d5\u05e9 \u05d2\u05dd \u05d1\u05d9\u05d8\u05d5\u05d7 \u05de\u05d1\u05e0\u05d4 \u05d3\u05d9\u05e8\u05d4 \u05dc\u05de\u05e9\u05db\u05e0\u05ea\u05d0 \u05d4\u05e4\u05e7\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05de\u05e1\u05dc\u05d5\u05dc \u05d9\u05e8\u05d5\u05e7 \u05de\u05d4\u05d9\u05e8, \u05ea\u05d5\u05da 24 \u05e9\u05e2\u05d5\u05ea \u05de\u05e7\u05d1\u05dc\u05ea \u05d8\u05d5\u05e4\u05e1 \u05d4\u05d1\u05e7\u05e9\u05d4",
                number: null,
                featured: 1,
                short:
                  "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05db\u05d9\u05e1\u05d5\u05d9 \u05d2\u05d5\u05d1\u05d4 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
                note: null,
              },
            ],
          },
          {
            id: 7,
            name: "\u05e6\u05d9\u05d5\u05df \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7",
            view_order: 2,
            terms: [
              {
                id: 49,
                policy_id: 7,
                company_id: 3,
                condition_section_id: 7,
                text: "<div><ul><li>\u05e6\u05d9\u05d5\u05df&nbsp; \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 -&nbsp; <b>73</b></li><li>\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - <b>79</b></li><li>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - <b>80</b></li></ul></div>",
                term: "<div><ul><li>\u05e6\u05d9\u05d5\u05df&nbsp; \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 -&nbsp; <b>86</b></li><li><br></li><li>\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - 79</li></ul></div><div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - 80</div></div>",
                number: null,
                featured: 1,
                short:
                  "\u05de\u05d3\u05d3 \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 86",
                note: null,
              },
            ],
          },
          {
            id: 8,
            name: "\u05d7\u05e9\u05d5\u05d1 \u05dc\u05d3\u05e2\u05ea",
            view_order: 3,
            terms: [
              {
                id: 50,
                policy_id: 7,
                company_id: 3,
                condition_section_id: 8,
                text: "<ul><li>\u05d4\u05ea\u05d5\u05db\u05e0\u05d9\u05ea \u05de\u05d9\u05d5\u05e2\u05d3\u05ea \u05dc\u05db\u05dc \u05de\u05d9 \u05e9\u05dc\u05d5\u05e7\u05d7 \u05de\u05e9\u05db\u05e0\u05ea\u05d4 \u05d1\u05d0\u05d7\u05d3 \u05de\u05d4\u05d1\u05e0\u05e7\u05d9\u05dd \u05d1\u05d9\u05e9\u05e8\u05d0\u05dc. \u05d0\u05d7\u05d3 \u05de\u05d4\u05de\u05e8\u05db\u05d9\u05d1\u05d9\u05dd \u05e9\u05dc \u05e2\u05dc\u05d5\u05ea \u05e4\u05e8\u05de\u05d9\u05d9\u05ea \u05e4\u05d5\u05dc\u05d9\u05e1\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05e0\u05e7\u05d1\u05e2\u05ea \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d2\u05d9\u05dc. </li><li>\u05de\u05d9 \u05e9\u05dc\u05d5\u05e7\u05d7 \u05de\u05e9\u05db\u05e0\u05ea\u05d4 \u05d1\u05d2\u05d9\u05dc \u05de\u05d1\u05d5\u05d2\u05e8 \u05d9\u05d5\u05ea\u05e8, \u05de\u05d7\u05d9\u05e8 \u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05d9\u05d4\u05d9\u05d4 \u05d2\u05d1\u05d5\u05d4 \u05d9\u05d5\u05ea\u05e8.</li></ul>",
                term: "\u05d4\u05ea\u05d5\u05db\u05e0\u05d9\u05ea \u05de\u05d9\u05d5\u05e2\u05d3\u05ea \u05dc\u05db\u05dc \u05de\u05d9 \u05e9\u05dc\u05d5\u05e7\u05d7 \u05de\u05e9\u05db\u05e0\u05ea\u05d4 \u05d1\u05d0\u05d7\u05d3 \u05de\u05d4\u05d1\u05e0\u05e7\u05d9\u05dd \u05d1\u05d9\u05e9\u05e8\u05d0\u05dc. \u05d0\u05d7\u05d3 \u05de\u05d4\u05de\u05e8\u05db\u05d9\u05d1\u05d9\u05dd \u05e9\u05dc \u05e2\u05dc\u05d5\u05ea \u05e4\u05e8\u05de\u05d9\u05d9\u05ea \u05e4\u05d5\u05dc\u05d9\u05e1\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05e0\u05e7\u05d1\u05e2\u05ea \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d2\u05d9\u05dc. \u05de\u05d9 \u05e9\u05dc\u05d5\u05e7\u05d7 \u05de\u05e9\u05db\u05e0\u05ea\u05d4 \u05d1\u05d2\u05d9\u05dc \u05de\u05d1\u05d5\u05d2\u05e8 \u05d9\u05d5\u05ea\u05e8, \u05de\u05d7\u05d9\u05e8 \u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05d9\u05d4\u05d9\u05d4 \u05d2\u05d1\u05d5\u05d4 \u05d9\u05d5\u05ea\u05e8.",
                number: null,
                featured: null,
                short: null,
                note: null,
              },
            ],
          },
        ],
      },
      "4": {
        id: 4,
        name: "\u05d4\u05e4\u05e0\u05d9\u05e7\u05e1",
        sections: [
          {
            id: 6,
            name: "\u05ea\u05e0\u05d0\u05d9\u05dd \u05db\u05dc\u05dc\u05d9\u05dd",
            view_order: 1,
            terms: [
              {
                id: 51,
                policy_id: 7,
                company_id: 4,
                condition_section_id: 6,
                text: "<ul><li>\u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d9\u05e7\u05d8\u05df \u05de\u05d9\u05d3\u05d9 \u05e9\u05e0\u05d4, \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d4\u05d7\u05d6\u05e8\u05d9 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05e9\u05dc \u05d4\u05dc\u05d5\u05d5\u05d4 \u05dc\u05d1\u05e0\u05e7. </li><li>\u05d1\u05db\u05da \u05e7\u05d9\u05d9\u05de\u05ea \u05d4\u05ea\u05d0\u05de\u05d4 \u05de\u05dc\u05d0\u05d4 \u05dc\u05de\u05d9\u05d3\u05ea \u05d4\u05e1\u05d9\u05db\u05d5\u05df \u05d4\u05d4\u05d5\u05dc\u05db\u05ea \u05d5\u05e7\u05d8\u05e0\u05d4, \u05d0\u05dc\u05d9\u05d4 \u05e0\u05d7\u05e9\u05e3 \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7. </li><li>\u05e0\u05d9\u05ea\u05df \u05d2\u05dd \u05dc\u05d1\u05d7\u05d5\u05e8 \u05d1\u05de\u05e1\u05dc\u05d5\u05dc \u05e9\u05d1\u05d5 \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05e0\u05d5\u05ea\u05e8 \u05e7\u05d1\u05d5\u05e2, \u05d4\u05de\u05ea\u05d0\u05d9\u05dd \u05dc\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d1\u05d4 \u05de\u05d5\u05d7\u05d6\u05e8\u05ea \u05d4\u05e7\u05e8\u05df \u05e8\u05e7 \u05d1\u05e1\u05d5\u05e3 \u05d4\u05ea\u05e7\u05d5\u05e4\u05d4 \u05d5\u05d4\u05e8\u05d9\u05d1\u05d9\u05ea \u05de\u05d5\u05d7\u05d6\u05e8\u05ea \u05d1\u05d0\u05d5\u05e4\u05df \u05e9\u05d5\u05d8\u05e3. </li><li>\u05e0\u05d9\u05ea\u05df \u05dc\u05d1\u05d7\u05d5\u05e8 \u05d0\u05ea \u05de\u05e1\u05dc\u05d5\u05dc\u05d9 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05e4\u05d9 \u05e1\u05d5\u05d2\u05d9 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d5\u05ea \u05e9\u05e0\u05dc\u05e7\u05d7\u05d5\u05ea \u05de\u05d4\u05d1\u05e0\u05e7: </li><li>\u05de\u05e1\u05dc\u05d5\u05dc \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05de\u05d5\u05ea\u05d0\u05dd \u05dc\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d1\u05d4 \u05d4\u05e7\u05e8\u05df \u05d5\u05d4\u05e8\u05d9\u05d1\u05d9\u05ea \u05de\u05d5\u05d7\u05d6\u05e8\u05d5\u05ea \u05d9\u05d7\u05d3\u05d9\u05d5, \u05d1\u05ea\u05e9\u05dc\u05d5\u05de\u05d9\u05dd \u05e9\u05d5\u05d5\u05d9\u05dd \u05d5\u05e8\u05e6\u05d5\u05e4\u05d9\u05dd (\u05d4\u05dc\u05d5\u05d5\u05d0\u05ea \u05e9\u05e4\u05d9\u05e6\u05e8). </li><li>\u05de\u05e1\u05dc\u05d5\u05dc \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05de\u05ea\u05d0\u05d9\u05dd \u05dc\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d1\u05d4 \u05d4\u05e7\u05e8\u05df \u05de\u05d5\u05d7\u05d6\u05e8\u05ea \u05d1\u05e1\u05d5\u05e3 \u05d4\u05ea\u05e7\u05d5\u05e4\u05d4, \u05d5\u05d4\u05e8\u05d9\u05d1\u05d9\u05ea \u05de\u05d5\u05d7\u05d6\u05e8\u05ea \u05d1\u05d0\u05d5\u05e4\u05df \u05e9\u05d5\u05d8\u05e3 (\u05d4\u05dc\u05d5\u05d5\u05d0\u05ea \u05d1\u05dc\u05d5\u05df). </li><li>\u05e0\u05d9\u05ea\u05df \u05dc\u05d1\u05d7\u05d5\u05e8 \u05d4\u05d0\u05dd \u05d4\u05ea\u05e9\u05dc\u05d5\u05dd \u05d4\u05d7\u05d5\u05d3\u05e9\u05d9 \u05d9\u05d4\u05d9\u05d4 \u05d1\u05ea\u05e2\u05e8\u05d9\u05e3 \u05e7\u05d1\u05d5\u05e2, \u05d0\u05d5 \u05d1\u05ea\u05e2\u05e8\u05d9\u05e3 \u05de\u05e9\u05ea\u05e0\u05d4.&nbsp; \u05e0\u05d9\u05ea\u05df \u05dc\u05d4\u05e2\u05d1\u05d9\u05e8 \u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05de\u05d7\u05d1\u05e8\u05d4 \u05dc\u05d7\u05d1\u05e8\u05d4 \u05d2\u05dd \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7.</li><li> \u05d4\u05d9\u05d4 \u05d5\u05d4\u05d7\u05dc\u05d8\u05ea\u05dd \u05dc\u05d4\u05e2\u05d1\u05d9\u05e8 \u05d0\u05ea \u05e4\u05d5\u05dc\u05d9\u05e1\u05ea \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0 \u05d0\u05dc\u05d9\u05e0\u05d5 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05d4, \u05d4\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05d5\u05e1\u05d1 \u05dc\u05dc\u05d0 \u05e2\u05dc\u05d5\u05d9\u05d5\u05ea \u05d4\u05d7\u05dc\u05e4\u05d4.</li></ul>",
                term: "\u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d9\u05e7\u05d8\u05df \u05de\u05d9\u05d3\u05d9 \u05e9\u05e0\u05d4, \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05d4\u05d7\u05d6\u05e8\u05d9 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05e9\u05dc \u05d4\u05dc\u05d5\u05d5\u05d4 \u05dc\u05d1\u05e0\u05e7. \u05d1\u05db\u05da \u05e7\u05d9\u05d9\u05de\u05ea \u05d4\u05ea\u05d0\u05de\u05d4 \u05de\u05dc\u05d0\u05d4 \u05dc\u05de\u05d9\u05d3\u05ea \u05d4\u05e1\u05d9\u05db\u05d5\u05df \u05d4\u05d4\u05d5\u05dc\u05db\u05ea \u05d5\u05e7\u05d8\u05e0\u05d4, \u05d0\u05dc\u05d9\u05d4 \u05e0\u05d7\u05e9\u05e3 \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7. \u05e0\u05d9\u05ea\u05df \u05d2\u05dd \u05dc\u05d1\u05d7\u05d5\u05e8 \u05d1\u05de\u05e1\u05dc\u05d5\u05dc \u05e9\u05d1\u05d5 \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05e0\u05d5\u05ea\u05e8 \u05e7\u05d1\u05d5\u05e2, \u05d4\u05de\u05ea\u05d0\u05d9\u05dd \u05dc\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d1\u05d4 \u05de\u05d5\u05d7\u05d6\u05e8\u05ea \u05d4\u05e7\u05e8\u05df \u05e8\u05e7 \u05d1\u05e1\u05d5\u05e3 \u05d4\u05ea\u05e7\u05d5\u05e4\u05d4 \u05d5\u05d4\u05e8\u05d9\u05d1\u05d9\u05ea \u05de\u05d5\u05d7\u05d6\u05e8\u05ea \u05d1\u05d0\u05d5\u05e4\u05df \u05e9\u05d5\u05d8\u05e3. \u05e0\u05d9\u05ea\u05df \u05dc\u05d1\u05d7\u05d5\u05e8 \u05d0\u05ea \u05de\u05e1\u05dc\u05d5\u05dc\u05d9 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05e4\u05d9 \u05e1\u05d5\u05d2\u05d9 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d5\u05ea \u05e9\u05e0\u05dc\u05e7\u05d7\u05d5\u05ea \u05de\u05d4\u05d1\u05e0\u05e7: \u05de\u05e1\u05dc\u05d5\u05dc \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05de\u05d5\u05ea\u05d0\u05dd \u05dc\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d1\u05d4 \u05d4\u05e7\u05e8\u05df \u05d5\u05d4\u05e8\u05d9\u05d1\u05d9\u05ea \u05de\u05d5\u05d7\u05d6\u05e8\u05d5\u05ea \u05d9\u05d7\u05d3\u05d9\u05d5, \u05d1\u05ea\u05e9\u05dc\u05d5\u05de\u05d9\u05dd \u05e9\u05d5\u05d5\u05d9\u05dd \u05d5\u05e8\u05e6\u05d5\u05e4\u05d9\u05dd (\u05d4\u05dc\u05d5\u05d5\u05d0\u05ea \u05e9\u05e4\u05d9\u05e6\u05e8). \u05de\u05e1\u05dc\u05d5\u05dc \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05de\u05ea\u05d0\u05d9\u05dd \u05dc\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d1\u05d4 \u05d4\u05e7\u05e8\u05df \u05de\u05d5\u05d7\u05d6\u05e8\u05ea \u05d1\u05e1\u05d5\u05e3 \u05d4\u05ea\u05e7\u05d5\u05e4\u05d4, \u05d5\u05d4\u05e8\u05d9\u05d1\u05d9\u05ea \u05de\u05d5\u05d7\u05d6\u05e8\u05ea \u05d1\u05d0\u05d5\u05e4\u05df \u05e9\u05d5\u05d8\u05e3 (\u05d4\u05dc\u05d5\u05d5\u05d0\u05ea \u05d1\u05dc\u05d5\u05df). \u05e0\u05d9\u05ea\u05df \u05dc\u05d1\u05d7\u05d5\u05e8 \u05d4\u05d0\u05dd \u05d4\u05ea\u05e9\u05dc\u05d5\u05dd \u05d4\u05d7\u05d5\u05d3\u05e9\u05d9 \u05d9\u05d4\u05d9\u05d4 \u05d1\u05ea\u05e2\u05e8\u05d9\u05e3 \u05e7\u05d1\u05d5\u05e2, \u05d0\u05d5 \u05d1\u05ea\u05e2\u05e8\u05d9\u05e3 \u05de\u05e9\u05ea\u05e0\u05d4.&nbsp; \u05e0\u05d9\u05ea\u05df \u05dc\u05d4\u05e2\u05d1\u05d9\u05e8 \u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05de\u05d7\u05d1\u05e8\u05d4 \u05dc\u05d7\u05d1\u05e8\u05d4 \u05d2\u05dd \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7. \u05d4\u05d9\u05d4 \u05d5\u05d4\u05d7\u05dc\u05d8\u05ea\u05dd \u05dc\u05d4\u05e2\u05d1\u05d9\u05e8 \u05d0\u05ea \u05e4\u05d5\u05dc\u05d9\u05e1\u05ea \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0 \u05d0\u05dc\u05d9\u05e0\u05d5 \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05d4, \u05d4\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05ea\u05d5\u05e1\u05d1 \u05dc\u05dc\u05d0 \u05e2\u05dc\u05d5\u05d9\u05d5\u05ea \u05d4\u05d7\u05dc\u05e4\u05d4.",
                number: null,
                featured: 1,
                short:
                  "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05db\u05d9\u05e1\u05d5\u05d9 \u05d2\u05d5\u05d1\u05d4 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0",
                note: null,
              },
            ],
          },
          {
            id: 7,
            name: "\u05e6\u05d9\u05d5\u05df \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7",
            view_order: 2,
            terms: [
              {
                id: 52,
                policy_id: 7,
                company_id: 4,
                condition_section_id: 7,
                text: '<span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df&nbsp; \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 -&nbsp;&nbsp;</span><span style="font-size: 1rem; font-weight: bolder;">87</span><br><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea -&nbsp;</span><span style="font-size: 1rem; font-weight: bolder;">94</span><br><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea -&nbsp;</span><span style="font-size: 1rem; font-weight: bolder;">78</span><br>',
                term: "<div>\u05e6\u05d9\u05d5\u05df&nbsp; \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 -&nbsp;&nbsp;<b>85</b></div><div>\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea -&nbsp;94</div><div>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea -&nbsp;78</div>",
                number: null,
                featured: 1,
                short:
                  "\u05de\u05d3\u05d3 \u05e9\u05d9\u05e8\u05d5\u05ea \u05d7\u05d1\u05e8\u05d4 85",
                note: null,
              },
            ],
          },
          {
            id: 8,
            name: "\u05d7\u05e9\u05d5\u05d1 \u05dc\u05d3\u05e2\u05ea",
            view_order: 3,
            terms: [
              {
                id: 54,
                policy_id: 7,
                company_id: 4,
                condition_section_id: 8,
                text: "<ul><li>\u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05ea\u05d3\u05d0\u05d2 \u05dc\u05e9\u05dc\u05dd \u05d0\u05ea \u05d9\u05ea\u05e8\u05ea \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05dc\u05d1\u05e0\u05e7, \u05d5\u05d1\u05db\u05da \u05ea\u05e4\u05d8\u05d5\u05e8 \u05d0\u05ea \u05d4\u05dc\u05d5\u05d5\u05d4 \u05d4\u05e0\u05d5\u05ea\u05e8 \u05de\u05d4\u05de\u05e9\u05da \u05ea\u05e9\u05dc\u05d5\u05de\u05d9\u05d1\u05d9\u05d8\u05d5\u05d7 \u05e8\u05d9\u05e1\u05e7 \u05de\u05e9\u05db\u05e0\u05ea\u05d0 \u05de\u05d2\u05df \u05e2\u05dc \u05d4\u05d1\u05e0\u05e7 \u05de\u05d0\u05d9 \u05d9\u05db\u05d5\u05dc\u05ea \u05d4\u05d7\u05d6\u05e8 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05e2\u05e7\u05d1 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05dc\u05d5\u05d5\u05d4. \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0 \u05dc\u05d1\u05e0\u05e7.</li></ul>",
                term: "\u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05ea\u05d3\u05d0\u05d2 \u05dc\u05e9\u05dc\u05dd \u05d0\u05ea \u05d9\u05ea\u05e8\u05ea \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05dc\u05d1\u05e0\u05e7, \u05d5\u05d1\u05db\u05da \u05ea\u05e4\u05d8\u05d5\u05e8 \u05d0\u05ea \u05d4\u05dc\u05d5\u05d5\u05d4 \u05d4\u05e0\u05d5\u05ea\u05e8 \u05de\u05d4\u05de\u05e9\u05da \u05ea\u05e9\u05dc\u05d5\u05de\u05d9\u05d1\u05d9\u05d8\u05d5\u05d7 \u05e8\u05d9\u05e1\u05e7 \u05de\u05e9\u05db\u05e0\u05ea\u05d0 \u05de\u05d2\u05df \u05e2\u05dc \u05d4\u05d1\u05e0\u05e7 \u05de\u05d0\u05d9 \u05d9\u05db\u05d5\u05dc\u05ea \u05d4\u05d7\u05d6\u05e8 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05e2\u05e7\u05d1 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05dc\u05d5\u05d5\u05d4. \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0 \u05dc\u05d1\u05e0\u05e7.",
                number: null,
                featured: null,
                short: null,
                note: null,
              },
            ],
          },
        ],
      },
      "5": {
        id: 5,
        name: "\u05db\u05dc\u05dc",
        sections: [
          {
            id: 6,
            name: "\u05ea\u05e0\u05d0\u05d9\u05dd \u05db\u05dc\u05dc\u05d9\u05dd",
            view_order: 1,
            terms: [
              {
                id: 55,
                policy_id: 7,
                company_id: 5,
                condition_section_id: 6,
                text: "<ul><li>\u05e4\u05d5\u05dc\u05d9\u05e1\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05d9\u05d9\u05dd \u05dc\u05e1\u05d9\u05dc\u05d5\u05e7 \u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d1\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea -\u05de\u05e7\u05e0\u05d4: </li><li>\u05d4\u05ea\u05d0\u05de\u05d4 \u05de\u05e8\u05d0\u05e9 \u05e9\u05dc \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05ea\u05e0\u05d0\u05d9 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05e9\u05e0\u05d8\u05dc \u05d4\u05de\u05d1\u05d5\u05d8\u05d7</li><li> \u05e4\u05d9\u05e8\u05e2\u05d5\u05df \u05de\u05dc\u05d5\u05d0 \u05e1\u05db\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4, \u05d1\u05ea\u05d5\u05e1\u05e4\u05ea \u05e9\u05dc \u05e2\u05d3 12 \u05d7\u05d5\u05d3\u05e9\u05d9 \u05e4\u05d9\u05d2\u05d5\u05e8 \u05d1\u05ea\u05e9\u05dc\u05d5\u05de\u05d9\u05dd (\u05d1\u05de\u05d9\u05d3\u05d4 \u05d5\u05d4\u05d9\u05d5 \u05db\u05d0\u05dc\u05d4), \u05dc\u05e8\u05d1\u05d5\u05ea \u05d4\u05e4\u05e8\u05e9\u05d9 \u05e8\u05d9\u05d1\u05d9\u05ea \u05e9\u05e0\u05d5\u05e1\u05e4\u05d5 \u05dc\u05d7\u05d5\u05d1, </li><li>\u05d1\u05de\u05e7\u05e8\u05d4 \u05e9\u05dc \u05e4\u05d8\u05d9\u05e8\u05d4 \u05d4\u05ea\u05e9\u05dc\u05d5\u05dd \u05dc\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05de\u05e9\u05ea\u05e0\u05d4 \u05db\u05dc \u05e9\u05e0\u05d4 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 (\u05e1\u05db\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4), \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05e2\u05dc \u05e4\u05d9 \u05d0\u05d7\u05d5\u05d6 \u05d4\u05e8\u05d9\u05d1\u05d9\u05ea \u05e9\u05e2\u05dc \u05e4\u05d9\u05d5 \u05e0\u05dc\u05e7\u05d7\u05d4 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4</li></ul>",
                term: "\u05e4\u05d5\u05dc\u05d9\u05e1\u05ea \u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05d9\u05d9\u05dd \u05dc\u05e1\u05d9\u05dc\u05d5\u05e7 \u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d1\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea -\u05de\u05e7\u05e0\u05d4: \u05d4\u05ea\u05d0\u05de\u05d4 \u05de\u05e8\u05d0\u05e9 \u05e9\u05dc \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05ea\u05e0\u05d0\u05d9 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05e9\u05e0\u05d8\u05dc \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05e4\u05d9\u05e8\u05e2\u05d5\u05df \u05de\u05dc\u05d5\u05d0 \u05e1\u05db\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4, \u05d1\u05ea\u05d5\u05e1\u05e4\u05ea \u05e9\u05dc \u05e2\u05d3 12 \u05d7\u05d5\u05d3\u05e9\u05d9 \u05e4\u05d9\u05d2\u05d5\u05e8 \u05d1\u05ea\u05e9\u05dc\u05d5\u05de\u05d9\u05dd (\u05d1\u05de\u05d9\u05d3\u05d4 \u05d5\u05d4\u05d9\u05d5 \u05db\u05d0\u05dc\u05d4), \u05dc\u05e8\u05d1\u05d5\u05ea \u05d4\u05e4\u05e8\u05e9\u05d9 \u05e8\u05d9\u05d1\u05d9\u05ea \u05e9\u05e0\u05d5\u05e1\u05e4\u05d5 \u05dc\u05d7\u05d5\u05d1, \u05d1\u05de\u05e7\u05e8\u05d4 \u05e9\u05dc \u05e4\u05d8\u05d9\u05e8\u05d4 \u05d4\u05ea\u05e9\u05dc\u05d5\u05dd \u05dc\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05de\u05e9\u05ea\u05e0\u05d4 \u05db\u05dc \u05e9\u05e0\u05d4 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 (\u05e1\u05db\u05d5\u05dd \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4), \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d5\u05e2\u05dc \u05e4\u05d9 \u05d0\u05d7\u05d5\u05d6 \u05d4\u05e8\u05d9\u05d1\u05d9\u05ea \u05e9\u05e2\u05dc \u05e4\u05d9\u05d5 \u05e0\u05dc\u05e7\u05d7\u05d4 \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4",
                number: null,
                featured: null,
                short: null,
                note: null,
              },
            ],
          },
          {
            id: 7,
            name: "\u05e6\u05d9\u05d5\u05df \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7",
            view_order: 2,
            terms: [
              {
                id: 56,
                policy_id: 7,
                company_id: 5,
                condition_section_id: 7,
                text: '<span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - </span><b style="font-size: 1rem;">80</b><br><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - </span><b style="font-size: 1rem;">83</b><br><span style="font-size: 1rem;">\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - </span><b style="font-size: 1rem;">75</b><br>',
                term: "\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - 80\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - 83\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - 75",
                number: null,
                featured: null,
                short: null,
                note: null,
              },
            ],
          },
          {
            id: 8,
            name: "\u05d7\u05e9\u05d5\u05d1 \u05dc\u05d3\u05e2\u05ea",
            view_order: 3,
            terms: [
              {
                id: 57,
                policy_id: 7,
                company_id: 5,
                condition_section_id: 8,
                text: "<ul><li>\u05d4\u05d7\u05d1\u05e8\u05d4 \u05ea\u05d4\u05d9\u05d4 \u05e4\u05d8\u05d5\u05e8\u05d4 \u05de\u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d2\u05de\u05d5\u05dc\u05d9 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05dd \u05de\u05d5\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05e0\u05d2\u05e8\u05dd \u05e2\u05e7\u05d1 \u05d4\u05ea\u05d0\u05d1\u05d3\u05d5\u05ea\u05d5, \u05e9\u05d0\u05d9\u05e8\u05e2\u05d4 \u05d1\u05ea\u05d5\u05da 12 \u05d7\u05d5\u05d3\u05e9\u05d9\u05dd \u05de\u05d9\u05d5\u05dd \u05d4\u05ea\u05d7\u05dc\u05ea \u05d4\u05db\u05d9\u05e1\u05d5\u05d9 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7\u05d9 \u05d0\u05d5 \u05de\u05d9\u05d5\u05dd \u05d7\u05d9\u05d3\u05d5\u05e9 \u05d4\u05db\u05d9\u05e1\u05d5\u05d9 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7\u05d9 \u05dc\u05d0\u05d7\u05e8 \u05e9\u05d1\u05d5\u05d8\u05dc \u05db\u05de\u05e4\u05d5\u05e8\u05d8 \u05d1\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e1\u05d5\u05d3\u05d9, \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d0\u05d5\u05d7\u05e8 \u05de\u05d1\u05d9\u05df \u05de\u05d5\u05e2\u05d3\u05d9\u05dd \u05d0\u05dc\u05d4.</li></ul>",
                term: "\u05d4\u05d7\u05d1\u05e8\u05d4 \u05ea\u05d4\u05d9\u05d4 \u05e4\u05d8\u05d5\u05e8\u05d4 \u05de\u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d2\u05de\u05d5\u05dc\u05d9 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05dd \u05de\u05d5\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05e0\u05d2\u05e8\u05dd \u05e2\u05e7\u05d1 \u05d4\u05ea\u05d0\u05d1\u05d3\u05d5\u05ea\u05d5, \u05e9\u05d0\u05d9\u05e8\u05e2\u05d4 \u05d1\u05ea\u05d5\u05da 12 \u05d7\u05d5\u05d3\u05e9\u05d9\u05dd \u05de\u05d9\u05d5\u05dd \u05d4\u05ea\u05d7\u05dc\u05ea \u05d4\u05db\u05d9\u05e1\u05d5\u05d9 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7\u05d9 \u05d0\u05d5 \u05de\u05d9\u05d5\u05dd \u05d7\u05d9\u05d3\u05d5\u05e9 \u05d4\u05db\u05d9\u05e1\u05d5\u05d9 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7\u05d9 \u05dc\u05d0\u05d7\u05e8 \u05e9\u05d1\u05d5\u05d8\u05dc \u05db\u05de\u05e4\u05d5\u05e8\u05d8 \u05d1\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05d9\u05e1\u05d5\u05d3\u05d9, \u05dc\u05e4\u05d9 \u05d4\u05de\u05d5\u05e2\u05d3 \u05d4\u05de\u05d0\u05d5\u05d7\u05e8 \u05de\u05d1\u05d9\u05df \u05de\u05d5\u05e2\u05d3\u05d9\u05dd \u05d0\u05dc\u05d4.",
                number: null,
                featured: null,
                short: null,
                note: null,
              },
            ],
          },
        ],
      },
      "6": {
        id: 6,
        name: "\u05de\u05e0\u05d5\u05e8\u05d4",
        sections: [
          {
            id: 6,
            name: "\u05ea\u05e0\u05d0\u05d9\u05dd \u05db\u05dc\u05dc\u05d9\u05dd",
            view_order: 1,
            terms: [
              {
                id: 58,
                policy_id: 7,
                company_id: 6,
                condition_section_id: 6,
                text: "<ul><li>\u05db\u05d9\u05e1\u05d5\u05d9 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05ea\u05e2\u05e8\u05d9\u05e4\u05d9\u05dd \u05d0\u05d8\u05e8\u05e7\u05d8\u05d9\u05d1\u05d9\u05d9\u05dd</li><li>\u05d1\u05de\u05e7\u05e8\u05d4 \u05d1\u05d9\u05d8\u05d5\u05d7 \u05ea\u05db\u05d5\u05e1\u05d4 \u05d9\u05ea\u05e8\u05ea \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0 \u05db\u05d5\u05dc\u05dc \u05e7\u05e0\u05e1\u05d5\u05ea \u05e4\u05d9\u05e8\u05e2\u05d5\u05df \u05d1\u05de\u05d9\u05d3\u05d4 \u05d5\u05d9\u05d4\u05d9\u05d5 \u05d5\u05e4\u05d9\u05d2\u05d5\u05e8\u05d9\u05dd \u05d1\u05ea\u05e9\u05dc\u05d5\u05dd \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0 \u05e2\u05d3 12 \u05d7\u05d5\u05d3\u05e9\u05d9\u05dd </li><li>&nbsp;\u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05de\u05db\u05dc \u05e1\u05d9\u05d1\u05d4 \u05e9\u05d4\u05d9\u05d0, \u05ea\u05d0\u05d5\u05e0\u05d4 \u05d0\u05d5 \u05de\u05d7\u05dc\u05d4, \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05ea\u05e9\u05dc\u05dd \u05d4\u05d7\u05d1\u05e8\u05d4 \u05d0\u05ea \u05d4\u05e1\u05db\u05d5\u05dd \u05d4\u05d2\u05d1\u05d5\u05d4 \u05de\u05d1\u05d9\u05df \u05d4\u05e9\u05e0\u05d9\u05d9\u05dd:</li><li> 1 .\u05d9\u05ea\u05e8\u05ea \u05d4\u05dc\u05d5\u05d5\u05d0\u05ea \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0 \u05d4\u05de\u05d1\u05d5\u05d8\u05d7\u05ea \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4, \u05db\u05d4\u05d2\u05d3\u05e8\u05ea\u05d4 \u05d1\u05ea\u05e0\u05d0\u05d9 \u05d4\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4.</li><li> 2 .\u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05db\u05d4\u05d2\u05d3\u05e8\u05ea\u05d5 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4. </li><li>\u05db\u05dc \u05d6\u05d0\u05ea \u05d1\u05db\u05e4\u05d5\u05e3 \u05dc\u05d4\u05e6\u05d4\u05e8\u05d5\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05d8\u05d5\u05e4\u05e1 \u05d4\u05d4\u05e6\u05e2\u05d4 \u05d5\u05d1\u05d4\u05e6\u05d4\u05e8\u05ea \u05d4\u05d1\u05e8\u05d9\u05d0\u05d5\u05ea.</li></ul>",
                term: "\u05db\u05d9\u05e1\u05d5\u05d9 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d1\u05ea\u05e2\u05e8\u05d9\u05e4\u05d9\u05dd \u05d0\u05d8\u05e8\u05e7\u05d8\u05d9\u05d1\u05d9\u05d9\u05dd\u05d1\u05de\u05e7\u05e8\u05d4 \u05d1\u05d9\u05d8\u05d5\u05d7 \u05ea\u05db\u05d5\u05e1\u05d4 \u05d9\u05ea\u05e8\u05ea \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0 \u05db\u05d5\u05dc\u05dc \u05e7\u05e0\u05e1\u05d5\u05ea \u05e4\u05d9\u05e8\u05e2\u05d5\u05df \u05d1\u05de\u05d9\u05d3\u05d4 \u05d5\u05d9\u05d4\u05d9\u05d5 \u05d5\u05e4\u05d9\u05d2\u05d5\u05e8\u05d9\u05dd \u05d1\u05ea\u05e9\u05dc\u05d5\u05dd \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0 \u05e2\u05d3 12 \u05d7\u05d5\u05d3\u05e9\u05d9\u05dd &nbsp;\u05d1\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05de\u05db\u05dc \u05e1\u05d9\u05d1\u05d4 \u05e9\u05d4\u05d9\u05d0, \u05ea\u05d0\u05d5\u05e0\u05d4 \u05d0\u05d5 \u05de\u05d7\u05dc\u05d4, \u05d1\u05de\u05d4\u05dc\u05da \u05ea\u05e7\u05d5\u05e4\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05ea\u05e9\u05dc\u05dd \u05d4\u05d7\u05d1\u05e8\u05d4 \u05d0\u05ea \u05d4\u05e1\u05db\u05d5\u05dd \u05d4\u05d2\u05d1\u05d5\u05d4 \u05de\u05d1\u05d9\u05df \u05d4\u05e9\u05e0\u05d9\u05d9\u05dd: 1 .\u05d9\u05ea\u05e8\u05ea \u05d4\u05dc\u05d5\u05d5\u05d0\u05ea \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0 \u05d4\u05de\u05d1\u05d5\u05d8\u05d7\u05ea \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4, \u05db\u05d4\u05d2\u05d3\u05e8\u05ea\u05d4 \u05d1\u05ea\u05e0\u05d0\u05d9 \u05d4\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4. 2 .\u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05db\u05d4\u05d2\u05d3\u05e8\u05ea\u05d5 \u05d1\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4. \u05db\u05dc \u05d6\u05d0\u05ea \u05d1\u05db\u05e4\u05d5\u05e3 \u05dc\u05d4\u05e6\u05d4\u05e8\u05d5\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d1\u05d8\u05d5\u05e4\u05e1 \u05d4\u05d4\u05e6\u05e2\u05d4 \u05d5\u05d1\u05d4\u05e6\u05d4\u05e8\u05ea \u05d4\u05d1\u05e8\u05d9\u05d0\u05d5\u05ea.",
                number: null,
                featured: null,
                short: null,
                note: null,
              },
            ],
          },
          {
            id: 7,
            name: "\u05e6\u05d9\u05d5\u05df \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7",
            view_order: 2,
            terms: [
              {
                id: 59,
                policy_id: 7,
                company_id: 6,
                condition_section_id: 7,
                text: "<div><ul><li>\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 <b>85</b></li><li>\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea <b>86</b></li><li>\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea <b>79</b></li></ul></div>",
                term: "\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 85\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea 86\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea 79",
                number: null,
                featured: null,
                short: null,
                note: null,
              },
            ],
          },
          {
            id: 8,
            name: "\u05d7\u05e9\u05d5\u05d1 \u05dc\u05d3\u05e2\u05ea",
            view_order: 3,
            terms: [
              {
                id: 60,
                policy_id: 7,
                company_id: 6,
                condition_section_id: 8,
                text: "<ul><li>\u05d4\u05d7\u05d1\u05e8\u05d4 \u05ea\u05d4\u05d9\u05d4 \u05e4\u05d8\u05d5\u05e8\u05d4 \u05de\u05ea\u05e9\u05dc\u05d5\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05dd \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d0\u05d9\u05e8\u05e2\u05d4 \u05e2\u05e7\u05d1 \u05d4\u05ea\u05d0\u05d1\u05d3\u05d5\u05ea \u05d1\u05de\u05d4\u05dc\u05da \u05e9\u05e0\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05e8\u05d0\u05e9\u05d5\u05e0\u05d4.</li></ul>",
                term: "\u05d4\u05d7\u05d1\u05e8\u05d4 \u05ea\u05d4\u05d9\u05d4 \u05e4\u05d8\u05d5\u05e8\u05d4 \u05de\u05ea\u05e9\u05dc\u05d5\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05dd \u05e4\u05d8\u05d9\u05e8\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d0\u05d9\u05e8\u05e2\u05d4 \u05e2\u05e7\u05d1 \u05d4\u05ea\u05d0\u05d1\u05d3\u05d5\u05ea \u05d1\u05de\u05d4\u05dc\u05da \u05e9\u05e0\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05e8\u05d0\u05e9\u05d5\u05e0\u05d4.",
                number: null,
                featured: null,
                short: null,
                note: null,
              },
            ],
          },
        ],
      },
      "7": {
        id: 7,
        name: "\u05d4\u05db\u05e9\u05e8\u05d4",
        sections: [
          {
            id: 6,
            name: "\u05ea\u05e0\u05d0\u05d9\u05dd \u05db\u05dc\u05dc\u05d9\u05dd",
            view_order: 1,
            terms: [
              {
                id: 61,
                policy_id: 7,
                company_id: 7,
                condition_section_id: 6,
                text: "<ul><li>\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05d9\u05d9\u05dd (\u05e8\u05d9\u05e1\u05e7) \u05dc\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05d4: \u05e1\u05d2\u05d9\u05e8\u05ea \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0 \u05d1\u05de\u05e7\u05e8\u05d4 \u05e9\u05dc \u05e4\u05d8\u05d9\u05e8\u05ea \u05d0\u05d7\u05d3 \u05de\u05d1\u05e0\u05d9 \u05d4\u05d6\u05d5\u05d2, \u05e2\u05dc \u05d9\u05d3 \u05ea\u05e9\u05dc\u05d5\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7. \u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05d6\u05d5 \u05de\u05d4\u05d5\u05d5\u05d4 \u05d0\u05ea \u05d4\u05d1\u05e1\u05d9\u05e1 \u05dc\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0.</li><li>\u05d1\u05e7\u05e8\u05d5\u05ea \u05de\u05e7\u05e8\u05d4 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05d1\u05d5\u05d8\u05d7, \u05ea\u05e9\u05dc\u05dd \u05d4\u05d7\u05d1\u05e8\u05d4 \u05d0\u05ea \u05ea\u05d2\u05de\u05d5\u05dc\u05d9 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05d0\u05d5\u05e4\u05df \u05d4\u05d1\u05d0: \u05e1\u05db\u05d5\u05dd \u05d4\u05e9\u05d5\u05d5\u05d4 \u05dc\u05d9\u05ea\u05e8\u05ea \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d4\u05de\u05e2\u05d5\u05d3\u05db\u05e0\u05ea \u05d9\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1 \u05d4\u05d1\u05dc\u05ea\u05d9 \u05d7\u05d5\u05d6\u05e8 \u05d5\u05d4\u05e1\u05db\u05d5\u05dd \u05d4\u05e2\u05d5\u05d3\u05e3 \u05d9\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1/\u05d9 \u05e9\u05dc \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05e9\u05e0\u05e4\u05d8\u05e8 \u05e9\u05de\u05d5\u05e0\u05d4, \u05d5\u05d4\u05db\u05dc \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05ea\u05e0\u05d0\u05d9 \u05d4\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4. </li><li>\u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05de\u05e9\u05ea\u05e0\u05d4 \u05de\u05d9\u05d3\u05d9 \u05e9\u05e0\u05d4 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05e0\u05ea\u05d5\u05e0\u05d9 \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d5\u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7.</li></ul>",
                term: "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05d9\u05d9\u05dd (\u05e8\u05d9\u05e1\u05e7) \u05dc\u05de\u05e7\u05e8\u05d4 \u05e4\u05d8\u05d9\u05e8\u05d4: \u05e1\u05d2\u05d9\u05e8\u05ea \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0 \u05d1\u05de\u05e7\u05e8\u05d4 \u05e9\u05dc \u05e4\u05d8\u05d9\u05e8\u05ea \u05d0\u05d7\u05d3 \u05de\u05d1\u05e0\u05d9 \u05d4\u05d6\u05d5\u05d2, \u05e2\u05dc \u05d9\u05d3 \u05ea\u05e9\u05dc\u05d5\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7. \u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05d6\u05d5 \u05de\u05d4\u05d5\u05d5\u05d4 \u05d0\u05ea \u05d4\u05d1\u05e1\u05d9\u05e1 \u05dc\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d4\u05de\u05e9\u05db\u05e0\u05ea\u05d0.\u05d1\u05e7\u05e8\u05d5\u05ea \u05de\u05e7\u05e8\u05d4 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05d1\u05d5\u05d8\u05d7, \u05ea\u05e9\u05dc\u05dd \u05d4\u05d7\u05d1\u05e8\u05d4 \u05d0\u05ea \u05ea\u05d2\u05de\u05d5\u05dc\u05d9 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05d0\u05d5\u05e4\u05df \u05d4\u05d1\u05d0: \u05e1\u05db\u05d5\u05dd \u05d4\u05e9\u05d5\u05d5\u05d4 \u05dc\u05d9\u05ea\u05e8\u05ea \u05d4\u05d4\u05dc\u05d5\u05d5\u05d0\u05d4 \u05d4\u05de\u05e2\u05d5\u05d3\u05db\u05e0\u05ea \u05d9\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1 \u05d4\u05d1\u05dc\u05ea\u05d9 \u05d7\u05d5\u05d6\u05e8 \u05d5\u05d4\u05e1\u05db\u05d5\u05dd \u05d4\u05e2\u05d5\u05d3\u05e3 \u05d9\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1/\u05d9 \u05e9\u05dc \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05e9\u05e0\u05e4\u05d8\u05e8 \u05e9\u05de\u05d5\u05e0\u05d4, \u05d5\u05d4\u05db\u05dc \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05ea\u05e0\u05d0\u05d9 \u05d4\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4. \u05d4\u05e4\u05e8\u05de\u05d9\u05d4 \u05de\u05e9\u05ea\u05e0\u05d4 \u05de\u05d9\u05d3\u05d9 \u05e9\u05e0\u05d4 \u05d1\u05d4\u05ea\u05d0\u05dd \u05dc\u05e0\u05ea\u05d5\u05e0\u05d9 \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d5\u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7.",
                number: null,
                featured: null,
                short: null,
                note: null,
              },
            ],
          },
          {
            id: 7,
            name: "\u05e6\u05d9\u05d5\u05df \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7",
            view_order: 2,
            terms: [
              {
                id: 207,
                policy_id: 7,
                company_id: 7,
                condition_section_id: 7,
                text: '<div><span style="font-size: 1rem; white-space: pre-wrap; background-color: rgb(248, 249, 252);">\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - </span><span style="font-weight: bolder; font-size: 1rem; white-space: pre-wrap; background-color: rgb(248, 249, 252);">79</span></div><span style="font-size: 1rem; white-space: pre-wrap; background-color: rgb(248, 249, 252);">\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - </span><span style="font-weight: bolder; font-size: 1rem; white-space: pre-wrap; background-color: rgb(248, 249, 252);">82</span><br style="white-space: pre-wrap; background-color: rgb(248, 249, 252);"><div><span style="font-size: 1rem; white-space: pre-wrap; background-color: rgb(248, 249, 252);">\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - </span><span style="font-weight: bolder; font-size: 1rem; white-space: pre-wrap; background-color: rgb(248, 249, 252);">78</span></div>',
                term: "\u05e6\u05d9\u05d5\u05df \u05e9\u05d9\u05e8\u05d5\u05ea \u05db\u05dc\u05dc\u05d9 - 79\u05e6\u05d9\u05d5\u05df \u05ea\u05e9\u05dc\u05d5\u05dd \u05ea\u05d1\u05d9\u05e2\u05d5\u05ea - 82\u05e6\u05d9\u05d5\u05df \u05d8\u05d9\u05e4\u05d5\u05dc \u05d1\u05dc\u05e7\u05d5\u05d7\u05d5\u05ea - 78",
                number: null,
                featured: null,
                short: null,
                note: null,
              },
            ],
          },
          {
            id: 8,
            name: "\u05d7\u05e9\u05d5\u05d1 \u05dc\u05d3\u05e2\u05ea",
            view_order: 3,
            terms: [
              {
                id: 63,
                policy_id: 7,
                company_id: 7,
                condition_section_id: 8,
                text: "<ul><li>\u05d4\u05d7\u05d1\u05e8\u05d4 \u05ea\u05d4\u05d9\u05d4 \u05e4\u05d8\u05d5\u05e8\u05d4 \u05de\u05ea\u05e9\u05dc\u05d5\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d0\u05dd \u05de\u05d5\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05e0\u05d2\u05e8\u05dd \u05e2\u05e7\u05d1 \u05d4\u05ea\u05d0\u05d1\u05d3\u05d5\u05ea, \u05d1\u05d9\u05df \u05d0\u05dd \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d4\u05d9\u05d4 \u05e9\u05e4\u05d5\u05d9 \u05d1\u05d3\u05e2\u05ea\u05d5 \u05d5\u05d1\u05d9\u05df \u05d0\u05dd \u05dc\u05d0\u05d5, \u05d1\u05ea\u05d5\u05da \u05e9\u05e0\u05d4 \u05de\u05d9\u05d5\u05dd \u05ea\u05d7\u05d9\u05dc\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05de\u05d9\u05d5\u05dd \u05d7\u05d9\u05d3\u05d5\u05e9 \u05d4\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05dc\u05d0\u05d7\u05e8 \u05e9\u05d1\u05d5\u05d8\u05dc\u05d4, \u05dc\u05e4\u05d9 \u05d4\u05de\u05d0\u05d5\u05d7\u05e8 \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd. \u05e0\u05d2\u05e8\u05dd \u05de\u05e7\u05e8\u05d4 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05d9\u05d3\u05d9 \u05de\u05d5\u05d8\u05d1 \u05d1\u05de\u05ea\u05db\u05d5\u05d5\u05df - \u05e4\u05d8\u05d5\u05e8\u05d4 \u05d4\u05d7\u05d1\u05e8\u05d4 \u05de\u05d7\u05d1\u05d5\u05ea\u05d4 \u05db\u05dc\u05e4\u05d9 \u05d0\u05d5\u05ea\u05d5 \u05de\u05d5\u05d8\u05d1 \u05d5\u05d7\u05dc\u05e7\u05d5 \u05d1\u05db\u05d9\u05e1\u05d5\u05d9 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7\u05d9 \u05d9\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd \u05d0\u05d7\u05e8\u05d9\u05dd \u05db\u05db\u05dc \u05e9\u05e7\u05d9\u05d9\u05de\u05d9\u05dd, \u05d1\u05d7\u05dc\u05e7\u05d9\u05dd \u05e9\u05d5\u05d5\u05d9\u05dd. \u05d1\u05d4\u05e2\u05d3\u05e8 \u05de\u05d5\u05d8\u05d1\u05d9\u05dd \u05d0\u05d7\u05e8\u05d9\u05dd \u05d4\u05e1\u05db\u05d5\u05dd \u05d9\u05e9\u05d5\u05dc\u05dd \u05dc\u05d9\u05d5\u05e8\u05e9\u05d9\u05d5 \u05d4\u05d7\u05d5\u05e7\u05d9\u05d9\u05dd \u05e9\u05dc</li></ul>",
                term: "\u05d4\u05d7\u05d1\u05e8\u05d4 \u05ea\u05d4\u05d9\u05d4 \u05e4\u05d8\u05d5\u05e8\u05d4 \u05de\u05ea\u05e9\u05dc\u05d5\u05dd \u05e1\u05db\u05d5\u05dd \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05dc\u05de\u05e7\u05e8\u05d4 \u05de\u05d5\u05d5\u05ea \u05d0\u05dd \u05de\u05d5\u05ea \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05e0\u05d2\u05e8\u05dd \u05e2\u05e7\u05d1 \u05d4\u05ea\u05d0\u05d1\u05d3\u05d5\u05ea, \u05d1\u05d9\u05df \u05d0\u05dd \u05d4\u05de\u05d1\u05d5\u05d8\u05d7 \u05d4\u05d9\u05d4 \u05e9\u05e4\u05d5\u05d9 \u05d1\u05d3\u05e2\u05ea\u05d5 \u05d5\u05d1\u05d9\u05df \u05d0\u05dd \u05dc\u05d0\u05d5, \u05d1\u05ea\u05d5\u05da \u05e9\u05e0\u05d4 \u05de\u05d9\u05d5\u05dd \u05ea\u05d7\u05d9\u05dc\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d0\u05d5 \u05de\u05d9\u05d5\u05dd \u05d7\u05d9\u05d3\u05d5\u05e9 \u05d4\u05e4\u05d5\u05dc\u05d9\u05e1\u05d4 \u05dc\u05d0\u05d7\u05e8 \u05e9\u05d1\u05d5\u05d8\u05dc\u05d4, \u05dc\u05e4\u05d9 \u05d4\u05de\u05d0\u05d5\u05d7\u05e8 \u05de\u05d1\u05d9\u05e0\u05d9\u05d4\u05dd. \u05e0\u05d2\u05e8\u05dd \u05de\u05e7\u05e8\u05d4 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d1\u05d9\u05d3\u05d9 \u05de\u05d5\u05d8\u05d1 \u05d1\u05de\u05ea\u05db\u05d5\u05d5\u05df - \u05e4\u05d8\u05d5\u05e8\u05d4 \u05d4\u05d7\u05d1\u05e8\u05d4 \u05de\u05d7\u05d1\u05d5\u05ea\u05d4 \u05db\u05dc\u05e4\u05d9 \u05d0\u05d5\u05ea\u05d5 \u05de\u05d5\u05d8\u05d1 \u05d5\u05d7\u05dc\u05e7\u05d5 \u05d1\u05db\u05d9\u05e1\u05d5\u05d9 \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7\u05d9 \u05d9\u05e9\u05d5\u05dc\u05dd \u05dc\u05de\u05d5\u05d8\u05d1\u05d9\u05dd \u05d0\u05d7\u05e8\u05d9\u05dd \u05db\u05db\u05dc \u05e9\u05e7\u05d9\u05d9\u05de\u05d9\u05dd, \u05d1\u05d7\u05dc\u05e7\u05d9\u05dd \u05e9\u05d5\u05d5\u05d9\u05dd. \u05d1\u05d4\u05e2\u05d3\u05e8 \u05de\u05d5\u05d8\u05d1\u05d9\u05dd \u05d0\u05d7\u05e8\u05d9\u05dd \u05d4\u05e1\u05db\u05d5\u05dd \u05d9\u05e9\u05d5\u05dc\u05dd \u05dc\u05d9\u05d5\u05e8\u05e9\u05d9\u05d5 \u05d4\u05d7\u05d5\u05e7\u05d9\u05d9\u05dd \u05e9\u05dc",
                number: null,
                featured: null,
                short: null,
                note: null,
              },
            ],
          },
        ],
      },
      "8": {
        id: 8,
        name: "\u05e9\u05d9\u05e8\u05d1\u05d9\u05d8",
        sections: [
          {
            id: 6,
            name: "\u05ea\u05e0\u05d0\u05d9\u05dd \u05db\u05dc\u05dc\u05d9\u05dd",
            view_order: 1,
            terms: [],
          },
          {
            id: 7,
            name: "\u05e6\u05d9\u05d5\u05df \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7",
            view_order: 2,
            terms: [],
          },
          {
            id: 8,
            name: "\u05d7\u05e9\u05d5\u05d1 \u05dc\u05d3\u05e2\u05ea",
            view_order: 3,
            terms: [],
          },
        ],
      },
      "9": {
        id: 9,
        name: "\u05d1\u05d9\u05d8\u05d5\u05d7 \u05d7\u05e7\u05dc\u05d0\u05d9",
        sections: [
          {
            id: 6,
            name: "\u05ea\u05e0\u05d0\u05d9\u05dd \u05db\u05dc\u05dc\u05d9\u05dd",
            view_order: 1,
            terms: [],
          },
          {
            id: 7,
            name: "\u05e6\u05d9\u05d5\u05df \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7",
            view_order: 2,
            terms: [],
          },
          {
            id: 8,
            name: "\u05d7\u05e9\u05d5\u05d1 \u05dc\u05d3\u05e2\u05ea",
            view_order: 3,
            terms: [],
          },
        ],
      },
      "10": {
        id: 10,
        name: "\u05e9\u05dc\u05de\u05d4 \u05d1\u05d9\u05d8\u05d5\u05d7",
        sections: [
          {
            id: 6,
            name: "\u05ea\u05e0\u05d0\u05d9\u05dd \u05db\u05dc\u05dc\u05d9\u05dd",
            view_order: 1,
            terms: [],
          },
          {
            id: 7,
            name: "\u05e6\u05d9\u05d5\u05df \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7",
            view_order: 2,
            terms: [],
          },
          {
            id: 8,
            name: "\u05d7\u05e9\u05d5\u05d1 \u05dc\u05d3\u05e2\u05ea",
            view_order: 3,
            terms: [],
          },
        ],
      },
      "11": {
        id: 11,
        name: "\u05e9\u05d5\u05de\u05e8\u05d4",
        sections: [
          {
            id: 6,
            name: "\u05ea\u05e0\u05d0\u05d9\u05dd \u05db\u05dc\u05dc\u05d9\u05dd",
            view_order: 1,
            terms: [],
          },
          {
            id: 7,
            name: "\u05e6\u05d9\u05d5\u05df \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7",
            view_order: 2,
            terms: [],
          },
          {
            id: 8,
            name: "\u05d7\u05e9\u05d5\u05d1 \u05dc\u05d3\u05e2\u05ea",
            view_order: 3,
            terms: [],
          },
        ],
      },
      "13": {
        id: 13,
        name: "\u05e4\u05e1\u05e4\u05d5\u05e8\u05d8 \u05e7\u05e8\u05d3",
        sections: [
          {
            id: 6,
            name: "\u05ea\u05e0\u05d0\u05d9\u05dd \u05db\u05dc\u05dc\u05d9\u05dd",
            view_order: 1,
            terms: [],
          },
          {
            id: 7,
            name: "\u05e6\u05d9\u05d5\u05df \u05d7\u05d1\u05e8\u05ea \u05d4\u05d1\u05d9\u05d8\u05d5\u05d7",
            view_order: 2,
            terms: [],
          },
          {
            id: 8,
            name: "\u05d7\u05e9\u05d5\u05d1 \u05dc\u05d3\u05e2\u05ea",
            view_order: 3,
            terms: [],
          },
        ],
      },
    },
  },
  success: true,
};
