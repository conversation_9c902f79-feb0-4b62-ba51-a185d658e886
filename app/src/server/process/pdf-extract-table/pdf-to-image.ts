import type { CoreMessage } from "ai";
import { z } from "zod";
import { aiGenerateObject } from "@/server/process/ai/aisdk";

const schema = z.object({
  loan_tracks: z
    .array(
      z.object({
        columnHeader: z.string().describe("Column header"),
        trackNumber: z.number().describe("מסלול"),
        name: z.string().describe("שם הלוואה"),
        amount: z.number().describe("סכום הלוואה"),
        months: z.number().describe("תקופת הלוואה"),
        paymentType: z.enum(["שפיצר", "בלון"]).describe("שפיצר או בלון"),
        interestRate: z.number().describe("שנתית ריבית"),
      })
    )
    .describe("table columns, each column contains loan track information"),
});

export const pdfToImage = async (base64Image: string) => {
  // const tempFilePath = `/tmp/${Math.random()
  //   .toString(36)
  //   .substring(2, 15)}.pdf`;
  //
  // const outputPath = `${Math.random().toString(36).substring(2, 15)}`;
  //
  // // Download the PDF
  // const response = await axios.get(pdfPath, { responseType: "arraybuffer" });
  //
  // // Write the downloaded content to a temporary file
  // await fs.writeFile(tempFilePath, Buffer.from(response.data));
  //
  // const options = {
  //   format: "png",
  //   scale: 2048,
  //   out_dir: "/tmp",
  //   out_prefix: outputPath,
  //   page: page,
  // };
  //
  // // eslint-disable-next-line @typescript-eslint/no-unsafe-call
  // // const result = await convert(tempFilePath, options);
  // throw new Error("pdf-poppler not working on linux???");

  // const imagePath = `/tmp/${outputPath}-${`${page}`
  //   .toString()
  //   .padStart(2, "0")}.png`;
  // const imageData = await fs.readFile(imagePath);
  // const base64Image = imageData.toString("base64");
  //
  // console.log(`muly:pdfToImage`, {
  //   result,
  //   tempFilePath,
  //   outputPath,
  //   base64Image,
  // });
  //
  // // await fs.unlink(tempFilePath);
  // // await fs.unlink(imagePath);
  //
  const messages: CoreMessage[] = [
    {
      role: "system",
      content: `You are a helpful assistant that extracts data from images.
        Your task is to extract data from the image

        the table is in hebrew to make sure to look of the table data from right to left
        the 2 right most columns row index and row name are not data, they are just for reference
        the left most column is the totals, it is not a loan track
        make sure to read columns from right to left

        be very accurate and precise in your responses, make sure the table is extracted correctly and the data is accurate.
        make sure the data exists in the table, if not, return an empty array.

        notice that some columns can be empty, ignore them.
        also ignore the total column, it is not a loan track.
        `,
    },
    {
      role: "user",
      content:
        "Get values from table below\n\n[Image content is attached but not shown in this message]",
    },
  ];

  const result = await aiGenerateObject({
    provider: "openai",
    model: "gpt-4o-2024-08-06",
    messages,
    schema,
  });

  return result.loan_tracks;
};
