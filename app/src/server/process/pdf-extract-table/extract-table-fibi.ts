import type { LoanTrack } from "@/server/process/pdf-extract-table/job-call-pdf-extract-table";

export const convertTableToLoanTrackFibi = (
  tCols: string[][],
  tCol: string[],
  colIndex: number,
  version: number
): LoanTrack | undefined => {
  const check = (colIndex: number, text: string): string => {
    if (!names[colIndex] || !tCol[colIndex]) {
      throw new Error(`Column ${colIndex} not found (${text})`);
    }
    const name = names[colIndex]!.replaceAll("\n", " ");
    const cell = tCol[colIndex]!.replaceAll("\n", " ");

    if (name.includes(text)) {
      return cell;
    }

    console.log(`Column ${colIndex} not found (${text})`, {
      text,
      name,
      cell,
    });
    throw new Error(`Column ${colIndex} not found (${name}/${text})`);
  };

  const names = tCols.at(-2)!;
  const colHeader = tCol.at(0)!;
  if (!colHeader || !tCol.at(1)) {
    return undefined;
  }

  const interest_type = check(1, "האוולהה םש").includes("הנתשמ")
    ? "change"
    : "fixed";
  const balance = parseFloat(check(2, "האוולהה םוכס").replaceAll(",", ""));
  const loanTrackYears = parseFloat(check(3, "האוולהה תפוקת")) / 12;
  const loan_type = check(4, "םולשת ןפוא").includes("רציפש")
    ? "shpizer"
    : "ballon";
  const interest_rateText = check(5, "תיבירה רועיש תיתנשה");
  const lines = interest_rateText.split("\n");
  const interest_rate = parseFloat(lines[0]!);

  return {
    balance,
    loanTrackYears,
    interest_rate,
    interest_type,
    loan_type,
  };
};
