import { Langfuse } from "langfuse";
import { aiGenerateObject, type LL<PERSON>rovider } from "./aisdk";
import type { CoreMessage } from "ai";
import { env } from "@/env.mjs";
import type { z } from "zod";

export const runStructuredLLM = async <T>(
  taskName: string,
  options: {
    messages: CoreMessage[];
    schema: z.ZodType<T>;
    provider?: LLMProvider;
    model?: string;
  }
): Promise<T> => {
  const langfuse = new Langfuse({
    publicKey: env.LANGFUSE_PUBLIC_KEY,
    secretKey: env.LANGFUSE_SECRET_KEY,
    baseUrl: "https://cloud.langfuse.com",
  });

  const result = await aiGenerateObject({
    provider: options.provider ?? "openai",
    model: options.model,
    messages: options.messages,
    schema: options.schema,
  });

  await langfuse.flushAsync();

  return result;
};
