import axios from "axios";
import { z } from "zod";
import { logger } from "@trigger.dev/sdk";
import { castError } from "@/utils/errors";

const schemaCreatePolywiseLink = z.object({
  answer: z
    .object({
      card_id: z.string(),
      first_name: z.string(),
      last_name: z.string(),
      phone: z.string(),
      mail: z.string().optional(),
      link_url: z.string().nullish(),
      status: z.string().optional(),
      createdNew: z.boolean().optional(),
    })
    .nullish(),
  error: z.string().optional(),
  tracingUrl: z.string().url().optional(),
});

import { polywiseCreateLinkV2 } from "@/trigger/polywise-create-link-v2";

export const createPolywiseLink = async (
  card_id: string,
  first_name: string,
  last_name: string,
  phone: string,
  mail?: string
): Promise<{
  signed: boolean;
  status: string;
  createdNew?: boolean;
  link?: string | null;
}> => {
  const startTime = Date.now();

  if (!card_id || !first_name) {
    throw new Error("card_id or first_name is missing");
  }

  try {
    logger.info(`Triggering polywise-create-link-v2 task`, {
      card_id,
      first_name,
      last_name,
      phone,
      mail,
    });

    const answer = await polywiseCreateLinkV2.triggerAndWait({
      card_id,
      first_name,
      last_name,
      phone,
      mail,
    });

    if (!answer.ok) {
      throw new Error(
        // @ts-ignore
        `Failed to trigger polywiseCreateLinkV2: ${answer.error?.message}`
      );
    }

    const result = answer.output;

    const durationSeconds = Math.round((Date.now() - startTime) / 1000);

    logger.info(`Got result from polywise-create-link-v2 task`, {
      durationSeconds,
      result,
    });

    const status = result.status;
    const createdNew = result.createdNew;
    const link_url = result.link_url;

    const signed = status !== "טרם נשלח ללקוח לחתימה";
    logger.warn(`createPolywiseLink signed:${signed}`, {
      status,
      createdNew,
      link_url,
    });

    return {
      signed,
      status: status || "",
      link: link_url,
      createdNew: createdNew,
    };
  } catch (e) {
    const error = castError(e);
    logger.error(`Error createPolywiseLink ${error.message}`, {
      t: typeof error,
      error: error.stack,
    });
    throw e;
  }
};
