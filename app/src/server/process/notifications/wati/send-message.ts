import { env } from "@/env.mjs";
import { formatPhoneNumber } from "@/utils/phone";
import type { PrismaClient } from "@prisma/client";
import { logger } from "@trigger.dev/sdk";

export const sendMessage = async (
  prisma: PrismaClient,
  phone: string,
  templateName: string,
  sessionId: string,
  info: Record<string, string>,
  runId: string,
  context: Record<string, any>
) => {
  const whatsappNumber = formatPhoneNumber(phone)!
    .replaceAll("+", "")
    .replaceAll(" ", "");

  const url = env.WATI_URL;
  const options = {
    method: "POST",
    headers: {
      "content-type": "application/json-patch+json",
      Authorization: env.WATI_TOKEN,
    },
  };

  const body = {
    template_name: templateName,
    broadcast_name: sessionId,
    receivers: [
      {
        whatsappNumber,
        customParams: Object.keys(info).map((key) => {
          return {
            name: key,
            value: info[key],
          };
        }),
      },
    ],
  };

  logger.info(`muly:sendMessage:sendMessage`, {
    templateName,
    whatsappNumber,
    sessionId,
    info,
    url,
    options,
    body,
  });

  const answer = await fetch(url, {
    ...options,
    body: JSON.stringify(body),
  });

  const json = await answer.json();

  const notification = await prisma.notifications.create({
    data: {
      source: "WATI",
      template: templateName,
      info,
      message: templateName,
      phone,
      sessionId,
      providerAnswer: json,
      runId,
      context,
      status: json.error ? "error" : json.result ? "success" : "unknown",
    },
  });

  logger.info(`muly:sendMessage:sendMessage`, {
    templateName,
    whatsappNumber,
    sessionId,
    info,
    notification,
    answer,
    wati_body: body,
  });

  // eslint-disable-next-line  @typescript-eslint/no-unsafe-return
  return json;
};
