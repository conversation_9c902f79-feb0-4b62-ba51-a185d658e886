import type { NextPage } from "next";
import React from "react";
import { FormTestComponent } from "@/components/common/forms/form-test";
import { z } from "zod";
import { customerModel } from "../../../prisma/zod";

const Page: NextPage = () => {
  // const schema = z.object({
  //   bool: z.boolean().describe("Boolean"),
  // });

  const schema2 = z.object({
    gender: customerModel.shape.gender.describe("Gender").meta({
      control: "RadioGroup",
      choices: [
        { id: "male", title: "Male" },
        { id: "female", title: "Female" },
      ],
    }),
  });

  return <FormTestComponent schema={schema2} />;
};

export default Page;
