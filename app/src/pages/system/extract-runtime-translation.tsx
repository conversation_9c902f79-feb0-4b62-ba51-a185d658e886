import { useState } from "react";
import { extractRuntimeTranslation } from "../../server/process/extract-runtime-translation";
import { useTranslation } from "next-i18next";
import { i18nGetServerSideProps } from "@/utils/i18n-ssr";
import { Button } from "@/components/ui/button";

export const getServerSideProps = i18nGetServerSideProps(["common"]);

const ExtractRuntimeTranslation = () => {
  const { t: common } = useTranslation("common");
  const [isLoading, setIsLoading] = useState(false);

  const handleExtract = async () => {
    setIsLoading(true);
    const answer = await extractRuntimeTranslation({
      common,
    });
    console.log(`extractRuntimeTranslation ${answer.message}`, { answer });
    setIsLoading(false);
  };

  return (
    <div className="flex flex-col">
      <div>ExtractRuntimeTranslation</div>
      <Button
        variant="primary"
        isLoading={isLoading}
        onClick={() => void handleExtract()}
      >
        EXTRACT
      </Button>
    </div>
  );
};

export default ExtractRuntimeTranslation;
