import type { NextApiRequest, NextApiResponse } from "next";
import { isValidSignature, SIGNATURE_HEADER_NAME } from "@sanity/webhook";
import { env } from "@/env.mjs";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const secret = env.SANITY_REVALIDATE_SECRET;
  const signature = req.headers[SIGNATURE_HEADER_NAME] as string;
  const body = await readBody(req); // Read the body into a string
  if (!(await isValidSignature(body, signature, secret))) {
    res.status(401).json({ success: false, message: "Invalid signature" });
    return;
  }

  const jsonBody = JSON.parse(body);

  console.log(`muly:sanity-revalidate:handler`, {
    jsonBody,
  });

  if (jsonBody._type === "post") {
    await res.revalidate(`/blog/index`);
    await res.revalidate(`/blog/${jsonBody.slug.current}`);
  }

  res.json({ success: true });
}

// Next.js will by default parse the body, which can lead to invalid signatures
export const config = {
  api: {
    bodyParser: false,
  },
};

export async function readBody(readable: NextApiRequest) {
  const chunks = [];
  for await (const chunk of readable) {
    chunks.push(typeof chunk === "string" ? Buffer.from(chunk) : chunk);
  }
  return Buffer.concat(chunks).toString("utf8");
}
