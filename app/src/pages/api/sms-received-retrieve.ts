import type { NextApiRequest, NextApiResponse } from "next";
import { prisma } from "@/server/db";
import { readBody } from "@/pages/api/sanity-revalidate";
import { env } from "@/env.mjs";

export function extractCode(text: string): string | null {
  const match = text.match(/\d+/gm);
  return match ? match[0] : null;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  console.log(`muly:sms-recived-retrive:handler`, {
    query: req.query,
    method: req.method,
    body: req.body,
  });

  if (req.method === "POST") {
    const { action, api_key, startTime } = req.body;
    if (api_key !== env.ADMIN_SECRET) {
      res.status(401).end();
    }
    if (action === "polywizz") {
      const incomingSms = await prisma.incommingSms.findMany({
        where: {
          phone: "972533732915",
          createdAt: {
            gte: new Date(startTime),
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        take: 2,
      });

      const codes = incomingSms
        .map(({ body }) => extractCode(body))
        .filter(Boolean);

      const code = codes[0];
      console.log(`muly:sms-received-retrieve:handler`, {
        action,
        api_key,
        incomingSms,
        codes: codes[0],
        code,
      });

      if (code) {
        res.status(200).send({ code });
      } else {
        res.status(400).send({ error: "No code found" });
      }
    }
  }
  return res.status(200).end();
}
