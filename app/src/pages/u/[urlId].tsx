import { useRouter } from "next/router";
import { api } from "@/utils/api";
import { Loading } from "@/components/common/Loading";
import React, { useEffect } from "react";
import { ErrorMessage } from "@/components/common/ErrorMessage";

const UrlShortnerPage = () => {
  const router = useRouter();
  const { urlId } = router.query;

  const { data, isLoading } = api.misc.getUrlShortner.useQuery(String(urlId));

  useEffect(() => {
    if (data && data !== "not-found") {
      void router.replace(data);
    }
  }, [data, router]);

  return data === "not-found" ? <ErrorMessage /> : <Loading />;
};

export default UrlShortnerPage;
