import { task, logger, tags } from "@trigger.dev/sdk";
import z from "zod";
import { formatPhoneNumber } from "@/utils/phone";
import { prisma } from "@/server/db";
import { formatName } from "@/utils/format";
import { env } from "@/env.mjs";
import {
  NotificationChannel,
  sendTextMessageNotification,
} from "@/server/process/notifications/send-text-message-notification";
import type { messageTemplates } from "@/server/process/notifications/message-template";
import { sendMessage } from "@/server/process/notifications/wati/send-message";
import type { MessageClass } from "@/server/process/whatsapp-agent/classify-message";
import { classifyMessage } from "@/server/process/whatsapp-agent/classify-message";
import { sub } from "date-fns";
import { addSystemEvent } from "@/server/process/system-events";
import { automationProcessLock } from "@/server/process/automation-process-lock";

export const watiHandleWebhook = task({
  id: "wati-handle-webhook",
  run: async (
    payload: {
      message: z.infer<typeof messageSchema>;
    },
    { ctx }
  ) => {
    const { message } = payload;
    const lock = await automationProcessLock(
      prisma,
      message.id,
      "wati-webhook"
    );

    if (!lock) {
      logger.info(`automation process locked ${message.id}`);
      return;
    }

    const formatedPhone = formatPhoneNumber(message.waId);

    // const notification = await prisma.notifications.create({
    //   data: {
    //     source: "WATI-WEBHOOK",
    //     template: message.eventType,
    //     message: message.text,
    //     phone: formatedPhone ?? message.waId,
    //     providerAnswer: message,
    //     status: message.statusString,
    //   },
    // });

    await tags.add(`phn_${formatedPhone}`);

    if (!message.text) {
      logger.info(`wati-webhook ${message.waId} no text`);
      return;
    }

    // templateMessageSent
    if (message.eventType === "message") {
      // find latest customer insurance based on phone number
      const lifeInsurance = await prisma.lifeInsurance.findFirst({
        where: {
          lifeInsuranceCustomer: {
            some: {
              customer: {
                formatedPhone: formatedPhone,
              },
            },
          },
        },
        orderBy: {
          serialNumber: "desc",
        },
        select: {
          id: true,
          serialNumber: true,
          automationDisabled: true,
          automation2Disabled: true,
          advisor: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              phone: true,
            },
          },
          lifeInsuranceCustomer: {
            select: {
              customer: {
                select: {
                  first_name: true,
                  last_name: true,
                  phone: true,
                  email: true,
                },
              },
            },
          },
        },
      });

      if (lifeInsurance) {
        await tags.add(`ins_${lifeInsurance.serialNumber}`);
      }

      const info = lifeInsurance
        ? {
            agent_name: formatName(lifeInsurance.advisor),
            agent_phone: lifeInsurance.advisor.phone,
            customer_name: lifeInsurance.lifeInsuranceCustomer
              .map((c) => formatName(c.customer))
              .join(", "),
            customer_phone: formatedPhone,
            adminUrl: `${env.UI_URL}/admin#/lifeInsurance/${lifeInsurance.id}`,
          }
        : {
            agent_name: "לא ידוע",
            agent_phone: "לא ידוע",
            customer_name: "לא ידוע",
            customer_phone: formatedPhone,
          };

      const customer = lifeInsurance?.lifeInsuranceCustomer?.find(
        (c) => formatPhoneNumber(c.customer.phone) === formatedPhone
      );

      logger.info(`wati-webhook ${message.waId} `, {
        info,
        message,
        lifeInsurance,
        customer,
      });

      let messageClass: MessageClass = "Unknown";
      let isClassifyByAI = false;
      if (message.type === "button") {
        if (
          message.buttonReply?.text === "אשמח לשיחה" ||
          message.buttonReply?.text === "אשמח לעזרה"
        ) {
          messageClass = "Call";
        } else if (
          lifeInsurance &&
          message.buttonReply?.text === "לא מעוניין בקשר נוסף"
        ) {
          // TODO, update
          await prisma.lifeInsurance.update({
            where: {
              id: lifeInsurance.id,
            },
            data: {
              automationDisabled: true,
              automation2Disabled: true,
            },
          });

          logger.warn(`muly:wati-webhook:stopAutomation`);
          await sendMessage(
            prisma,
            message.waId,
            "customer_stop2_001",
            lifeInsurance?.id ?? message.id,
            {
              // name:
              //   customer?.customer.first_name ||
              //   customer?.customer.last_name ||
              //   "לקוח",
            },
            `wati-webhook-${message.id}`,
            {
              wati: message,
            }
          );
        }
      }

      if (message.type === "text") {
        const text = message.text;
        if (text) {
          messageClass = await classifyMessage(text);
          isClassifyByAI = true;
          await addSystemEvent(
            `Wati webhook ${message.waId} classifyMessage ${messageClass}`,
            {
              text,
              phone: message.waId,
              lifeInsuranceId: lifeInsurance?.id,
            },
            false
          );
        }
      }

      if (
        isClassifyByAI &&
        (messageClass === "Call" || messageClass === "Stop")
      ) {
        const count = await prisma.notifications.count({
          where: {
            phone: message.waId,
            createdAt: {
              gte: sub(new Date(), { days: 2 }),
            },
            source: "WATI-WEBHOOK",
          },
        });

        if (count > 0) {
          messageClass = "Unknown";
        }
      }

      logger.info(`messageClass ${messageClass}`);
      if (messageClass === "Call") {
        const answer = await sendTextMessageNotification(
          prisma,
          env.ADMIN_PHONE_NUMBER ?? "",
          "admin_customer_request_call" as keyof typeof messageTemplates,
          info,
          NotificationChannel.SMS // TODO: change to whatsapp
        );

        await sendMessage(
          prisma,
          message.waId,
          "customer_call_request_001",
          lifeInsurance?.id ?? message.id,
          {},
          `wati-webhook-${message.id}`,
          {
            messageClass,
            isClassifyByAI,
            wati: message,
          }
        );
      } else if (messageClass === "Stop") {
        if (
          !lifeInsurance?.automationDisabled ||
          !lifeInsurance?.automation2Disabled
        ) {
          await sendMessage(
            prisma,
            message.waId,
            "customer_stop_001",
            lifeInsurance?.id ?? message.id,
            {
              name:
                customer?.customer.first_name ||
                customer?.customer.last_name ||
                "לקוח",
            },
            `wati-webhook-${message.id}`,
            {
              messageClass,
              isClassifyByAI,
              wati: message,
            }
          );
        } else {
          logger.warn(`Duplicate stop messages, ignore`, {
            messageClass,
            lifeInsurance,
          });
        }
      }
    }
  },
});

export const messageSchema = z.object({
  id: z.string(),
  created: z.string(),
  whatsappMessageId: z.string(),
  conversationId: z.string(),
  ticketId: z.string(),
  text: z.string().nullish(),
  type: z.enum(["text", "button", "template", "document"]), // Added "template"
  data: z.string().nullish(),
  sourceId: z.string().nullish(),
  sourceUrl: z.string().nullish(),
  timestamp: z.string().nullish(),
  owner: z.boolean().nullish(),
  eventType: z.enum([
    "message",
    "templateMessageSent",
    "templateMessageSent_v2",
    "message_v2",
  ]), // Added "templateMessageSent"
  statusString: z.enum(["SENT"]),
  avatarUrl: z.string().nullish(),
  assignedId: z.string().nullish(),
  operatorName: z.string().nullish(),
  operatorEmail: z.string().nullable(), // Changed to allow string or null
  waId: z.string(),
  messageContact: z.string().nullish(),
  senderName: z.string().nullish(),
  listReply: z.string().nullish(),
  interactiveButtonReply: z.string().nullish(),
  replyContextId: z.string().nullish(),
  sourceType: z.coerce.string(),
  frequentlyForwarded: z.boolean().nullish(),
  forwarded: z.boolean().nullish(),
  buttonReply: z
    .object({
      text: z.string(),
      payload: z.string(),
    })
    .nullish(),
  templateId: z.string().nullish(), // New field
  templateName: z.string().nullish(), // New field
});
