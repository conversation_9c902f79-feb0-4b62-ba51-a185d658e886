import { task, logger } from "@trigger.dev/sdk";
import type { NotificationChannel } from "@/server/process/notifications/send-text-message-notification";
import { sendTextMessageNotification } from "@/server/process/notifications/send-text-message-notification";
import { prisma } from "@/server/db";
import type { MessageTemplate } from "@/server/process/notifications/message-template";
import { messageTemplates } from "@/server/process/notifications/message-template";

export const sendTextNotification = task({
  id: "send-text-notification",
  run: async (payload: {
    phones: string;
    templateId: string;
    data: Record<string, string>;
    notificationChannel: NotificationChannel[];
  }) => {
    const { phones, templateId, data, notificationChannel } = payload;

    const template = messageTemplates[templateId as MessageTemplate];
    if (!template) {
      throw new Error(`Invalid templateId: ${templateId}`);
    }

    return await sendTextMessageNotification(
      prisma,
      phones,
      templateId as MessageTemplate,
      data,
      notificationChannel
    );
  },
});
