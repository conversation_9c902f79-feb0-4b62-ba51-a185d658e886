import React from "react";
import { AdvisorLifeInsurancePages } from "@/components/advisor/advisor-life-insurance/advisor-life-insurance-schema";
import {
  customer0Data,
  recordData,
  WizardTest,
} from "@/components/common/wizard/wizard-test";
import { trpcMsw } from "@/server/api/mock";
import { mockGetOffers } from "@/server/api/routers/insurance/mock-getOffers";
import { data } from "@/server/api/routers/insurance/mock-getLifeInsuranceComplete";
import { MortgageShowResultsDialog } from "@/components/advisor/advisor-life-insurance/mortgage-show-results-dialog";
import { LoadingDialog } from "@/components/common/ui/LoadingDialog";

const meta = {
  component: WizardTest,
};

export default meta;
const pages = AdvisorLifeInsurancePages;

export const howDoesItWork = {
  ...WizardTest,
  args: { pages, step: "0-1" },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/sAvmr55UwcNhZNAJ9XGpA6/smapy-landing-page?node-id=294-39449&t=Omiw4Kd4jROxXo3N-4",
    },
  },
};

export const numberOfCustomers = {
  ...howDoesItWork,
  args: {
    pages,
    step: "1-1",
    data: { ...recordData, number_of_customers: null },
  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/sAvmr55UwcNhZNAJ9XGpA6/smapy-landing-page?node-id=294-38623&t=Omiw4Kd4jROxXo3N-4",
    },
  },
};

export const numberOfCustomersSelected = {
  ...howDoesItWork,
  args: {
    pages,
    step: "1-1",
    data: { ...recordData, number_of_customers: 2 },
  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/sAvmr55UwcNhZNAJ9XGpA6/smapy-landing-page?node-id=294-38623&t=Omiw4Kd4jROxXo3N-4",
    },
  },
};

export const customer0_details1 = {
  ...howDoesItWork,
  args: { pages, step: "1-2" },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/sAvmr55UwcNhZNAJ9XGpA6/smapy-landing-page?node-id=294-38703&t=Omiw4Kd4jROxXo3N-4",
    },
  },
};

export const customer1_details1 = {
  ...howDoesItWork,
  args: { pages, step: "1-3" },
  parameters: {
    ...customer0Data,
    design: {
      type: "figma",
      url: "https://www.figma.com/file/sAvmr55UwcNhZNAJ9XGpA6/smapy-design?type=design&node-id=294-40220&t=C9MBcMolsRlPF5hU-4",
    },
  },
};

export const customer0_details2 = {
  ...howDoesItWork,
  args: { pages, step: "1-4" },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/sAvmr55UwcNhZNAJ9XGpA6/smapy-landing-page?node-id=294-38771&t=Omiw4Kd4jROxXo3N-4",
    },
  },
};
export const customer1_details2 = {
  ...howDoesItWork,
  args: { pages, step: "1-5" },
  parameters: {
    ...customer0Data,
    design: {
      type: "figma",
      url: "https://www.figma.com/file/sAvmr55UwcNhZNAJ9XGpA6/smapy-design?type=design&node-id=294-40445&t=C9MBcMolsRlPF5hU-4",
    },
  },
};

// export const loanDetailsIntro = {
//   ...howDoesItWork,
//   args: { pages, step: "1-6" },
//   parameters: {
//     design: {
//       type: "figma",
//       url: "https://www.figma.com/file/sAvmr55UwcNhZNAJ9XGpA6/smapy-landing-page?node-id=294-38862&t=Omiw4Kd4jROxXo3N-4",
//     },
//   },
// };

export const approvalUpload = {
  ...howDoesItWork,
  args: { pages, step: "1-6" },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/design/OZXc5IgGcAl7BKUnsNDX7W/%D7%AA%D7%94%D7%9C%D7%99%D7%9A-%D7%9E%D7%A9%D7%9B%D7%A0%D7%AA%D7%94-%D7%99%D7%95%D7%A2%D7%A5-%D7%A2%D7%9D-%D7%98%D7%A2%D7%99%D7%A0%D7%AA-%D7%90%D7%99%D7%A9%D7%95%D7%A8-%D7%A2%D7%A7%D7%A8%D7%95%D7%A0%D7%99-25%2F08%2F2024?node-id=7074-1582&t=HKGRBaU3So3Zaa2C-4",
    },
  },
};

export const approvalUploadLoading = {
  render: () => {
    return (
      <>
        <div>Some text</div>
        <LoadingDialog />
      </>
    );
  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/OZXc5IgGcAl7BKUnsNDX7W/%D7%AA%D7%94%D7%9C%D7%99%D7%9A-%D7%9E%D7%A9%D7%9B%D7%A0%D7%AA%D7%94-%D7%99%D7%95%D7%A2%D7%A5-%D7%A2%D7%9D-%D7%98%D7%A2%D7%99%D7%A0%D7%AA-%D7%90%D7%99%D7%A9%D7%95%D7%A8-%D7%A2%D7%A7%D7%A8%D7%95%D7%A0%D7%99-25%2F08%2F2024?node-id=4821-36926&t=sRCdfNTZFqCBb8Wd-4",
    },
  },
};

export const loanTracksCount = {
  ...howDoesItWork,
  args: { pages, step: "1-7" },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/sAvmr55UwcNhZNAJ9XGpA6/smapy-landing-page?node-id=294-40071&t=Omiw4Kd4jROxXo3N-4",
    },
  },
};

export const track1 = {
  ...howDoesItWork,
  args: { pages, step: "1-8" },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/design/OZXc5IgGcAl7BKUnsNDX7W/%D7%AA%D7%94%D7%9C%D7%99%D7%9A-%D7%9E%D7%A9%D7%9B%D7%A0%D7%AA%D7%94-%D7%99%D7%95%D7%A2%D7%A5-%D7%A2%D7%9D-%D7%98%D7%A2%D7%99%D7%A0%D7%AA-%D7%90%D7%99%D7%A9%D7%95%D7%A8-%D7%A2%D7%A7%D7%A8%D7%95%D7%A0%D7%99-25%2F08%2F2024?node-id=7059-3273&t=bPSqoO7UTIuFHdWc-4",
    },
  },
};

export const summarizeDetails = {
  ...howDoesItWork,
  args: { pages, stepName: "summarizeDetailsWithApprovalUpload" },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/sAvmr55UwcNhZNAJ9XGpA6/smapy-design?type=design&node-id=27885-10696&mode=design&t=9H72BLkFVsz7v8m9-4",
    },
  },
};

export const mortgageShowResults = {
  ...howDoesItWork,
  args: { pages, step: "2-1" },
  parameters: {
    msw: {
      handlers: [
        trpcMsw.insurance.getOffers.query((req, res, ctx) => {
          return res(ctx.status(200), ctx.data(mockGetOffers));
        }),
      ],
    },
    design: {
      type: "figma",
      url: "https://www.figma.com/file/sAvmr55UwcNhZNAJ9XGpA6/smapy-design?node-id=294-42041&t=yhfLoEoqhsCAgWp1-4",
    },
  },
};

export const mortgageShowResultsDialog = {
  render: () => {
    return (
      <MortgageShowResultsDialog
        buttonText="OK"
        myAction={async () => {}}
        isLoading={false}
      />
    );
  },
};

export const mortgageSelectedResult = {
  ...howDoesItWork,
  args: { pages, step: "2-2" },
  parameters: {
    msw: {
      handlers: [
        trpcMsw.insurance.getLifeInsuranceComplete.query((req, res, ctx) => {
          // @ts-ignore
          return res(ctx.status(200), ctx.data(data));
        }),
      ],
    },
    design: {
      type: "figma",
      url: "https://www.figma.com/file/sAvmr55UwcNhZNAJ9XGpA6/smapy-design?type=design&node-id=1377-34346&mode=dev",
    },
  },
};

export const customer0_moreDetails = {
  ...howDoesItWork,
  args: { pages, step: "3-1" },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/sAvmr55UwcNhZNAJ9XGpA6/smapy-landing-page?node-id=294-39061&t=Omiw4Kd4jROxXo3N-4",
    },
  },
};
export const customer1_moreDetails = {
  ...howDoesItWork,
  args: { pages, step: "3-2" },
  parameters: {
    ...customer0Data,
    design: {
      type: "figma",
      url: "https://www.figma.com/file/sAvmr55UwcNhZNAJ9XGpA6/smapy-landing-page?node-id=294-41496&t=Omiw4Kd4jROxXo3N-4",
    },
  },
};
export const mortgagePropertyAddress = {
  ...howDoesItWork,
  args: { pages, step: "3-3" },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/sAvmr55UwcNhZNAJ9XGpA6/smapy-landing-page?node-id=294-39449&t=Omiw4Kd4jROxXo3N-4",
    },
  },
};
export const insuranceDetails = {
  ...howDoesItWork,
  args: { pages, step: "3-4" },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/sAvmr55UwcNhZNAJ9XGpA6/smapy-landing-page?node-id=294-40851&t=Omiw4Kd4jROxXo3N-4",
    },
  },
};
export const sendLinksToComplete = {
  ...howDoesItWork,
  args: { pages, step: "4-1", data: { ...recordData, number_of_customers: 1 } },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/sAvmr55UwcNhZNAJ9XGpA6/smapy-landing-page?node-id=294-39188&t=Omiw4Kd4jROxXo3N-4",
    },
  },
};

export const sendLinksToComplete2Customers = {
  ...howDoesItWork,
  args: {
    pages,
    step: "4-1",
    data: { ...recordData, number_of_customers: 2 },
  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/sAvmr55UwcNhZNAJ9XGpA6/smapy-landing-page?node-id=294-39188&t=Omiw4Kd4jROxXo3N-4",
    },
  },
};

export const end_askForBuildingInsurance = {
  ...howDoesItWork,
  args: { pages, step: "4-2" },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/sAvmr55UwcNhZNAJ9XGpA6/smapy-landing-page?node-id=294-39808&t=Omiw4Kd4jROxXo3N-4",
    },
  },
};

export const endAnswerYes = {
  ...howDoesItWork,
  args: {
    pages,
    step: "4-3",
    data: { ...recordData, need_build_insurance: true },
  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/sAvmr55UwcNhZNAJ9XGpA6/smapy-design?type=design&node-id=294-39942&t=6jdgRazF5PWpb2G4-4",
    },
  },
};

export const endAnswerNo = {
  ...howDoesItWork,
  args: {
    pages,
    step: "4-3",
    data: { ...recordData, need_build_insurance: false },
  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/sAvmr55UwcNhZNAJ9XGpA6/smapy-design?type=design&node-id=294-39942&t=6jdgRazF5PWpb2G4-4",
    },
  },
};
