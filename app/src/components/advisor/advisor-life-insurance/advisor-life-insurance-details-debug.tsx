import { useRouter } from "next/router";
import { api } from "@/utils/api";
import { Button } from "@/components/ui/button";
import { saveAs } from "file-saver";
import React, { useState } from "react";

const AdvisorLifeInsuranceDetailsDebug = () => {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { id } = router.query;

  const { data } = api.insurance.getLifeInsuranceComplete.useQuery(
    {
      id: String(id),
      role: "advisor",
    },
    { keepPreviousData: true, refetchOnWindowFocus: false }
  );

  return (
    <div className="m-16 flex flex-col gap-8">
      <h1 className="text-xl">Forms</h1>
      <h2 className="text-xl">Customer Link</h2>
      <div>
        {`${window.location.origin}/customer/life-insurance/?link=${String(
          id
        )}`}
      </div>

      <h2 className="text-xl">Full Data</h2>
      <pre>{JSON.stringify(data, null, 2)}</pre>
    </div>
  );
};

export default AdvisorLifeInsuranceDetailsDebug;
