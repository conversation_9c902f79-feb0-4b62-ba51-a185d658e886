import type { WizardControlProps } from "@/components/common/wizard/useWizardFlow";
import { useTranslation } from "next-i18next";

export const AdvisorRegistrationEndHeader = ({
  onStepNext,
  step,
}: WizardControlProps) => {
  const { t } = useTranslation("common");
  return (
    <div className="max-w-md">
      <div className="text-2xl font-bold md:text-3xl">
        {t("advisor.registration.end.title")}
      </div>
      <div className="text-lg font-medium">
        {t("advisor.registration.end.subtitle")}
      </div>
    </div>
  );
};
