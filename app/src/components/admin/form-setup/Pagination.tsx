"use client";

import { cn } from "@/lib/utils";
import { cva } from "class-variance-authority";
import { ChevronsLeft, ChevronsRight } from "lucide-react";
import * as React from "react";

export interface PaginationProps {
  error?: any;
  count: number;
  variant?: string;
  currentPage: number;
  onChange: (page: number) => void;
}

const paginationVariants = cva(
  "inline-flex h-10 w-10 items-center rounded-md",
  {
    variants: {
      variant: {
        focus: "bg-blue-500 p-4 text-sm font-medium bg-[#C9CAD1]",
        secondary: "p-4 text-sm font-medium hover:text-blue-600 bg-[#e9eaed]",
      },
    },
    defaultVariants: {
      variant: "focus",
    },
  }
);

const Pagination = React.forwardRef<HTMLInputElement, PaginationProps>(
  ({ currentPage, onChange, count, error, variant, ...props }, ref) => {
    const pages = Array.from(Array(count).keys());
    // const page =
    // props.totalItems / itemsPerPage < 1 ? 1 : props.totalItems / itemsPerPage;
    // for (let i = 1; i <= page; i++) {
    //   pages.push(i);
    // }

    // console.log("items per page ", props.totalItems / props.itemsPerPage);
    return (
      <nav
        className="dir-ltr mt-3 flex items-center justify-start space-x-2 text-[#04002B]"
        ref={ref}
        {...props}
      >
        <a
          onClick={(event) => {
            onChange(currentPage !== 0 ? currentPage - 1 : 0);
          }}
          className="inline-flex items-center gap-2 rounded-md bg-[#C9CAD1] p-2 hover:text-blue-600"
        >
          <ChevronsLeft />
        </a>

        {pages.map((item, key) => {
          return (
            <a
              className={
                item === currentPage
                  ? cn(paginationVariants({ variant: "focus" }))
                  : cn(paginationVariants({ variant: "secondary" }))
              }
              onClick={(event) => {
                onChange(item);
              }}
              aria-current="page"
              key={key}
            >
              {item + 1}
            </a>
          );
        })}
        <a
          onClick={(event) => {
            console.log(currentPage <= count - 1);
            onChange(currentPage < count - 1 ? currentPage + 1 : currentPage);
          }}
          className="inline-flex items-center gap-2 rounded-md border border-[#04002B] p-2 hover:text-blue-600"
        >
          <ChevronsRight />
        </a>
      </nav>
    );
  }
);
Pagination.displayName = "Pagination";

export { Pagination };
