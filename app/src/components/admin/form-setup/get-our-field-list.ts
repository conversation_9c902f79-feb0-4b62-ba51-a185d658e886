import type { TranslationFn } from "@/utils/i18n-utils";
import { translateSchemaInfo } from "@/utils/i18n-utils";
import { getPagesZodMetaInfo } from "@/components/common/wizard/useWizardFlow";
import { AdvisorLifeInsurancePages } from "@/components/advisor/advisor-life-insurance/advisor-life-insurance-schema";
import { CustomerLifeInsurancePages } from "@/components/customer/customer-life-insurance/customer-life-insurance-schema";
import type { FlowParameters } from "@/components/advisor/advisor-life-insurance/get-flow-info";
import { getFlowParameters } from "@/components/advisor/advisor-life-insurance/get-flow-info";
import type { ChoiceType, MetaInfo } from "@/utils/zod-meta";
import { unwrap } from "@/components/libs/react-ts-form/unwrap";

export interface FieldInfo {
  name: string;
  label?: string;
  path?: string;
  typeName: string;
  choices?: ChoiceType[];
  flow: FlowParameters["flow"];
}

export const getOurFieldList = (
  t: TranslationFn,
  formName: string
): Record<string, FieldInfo> => {
  console.log(`muly:getOurFieldList START ${formName}`);
  const fields: Record<string, FieldInfo> = {
    "Keep Empty": {
      name: "Keep Empty",
      label: "השאר שדה ריק",
      typeName: "Keep Empty",
      flow: "Insurance",
    },
    Fixed: {
      name: "Fixed",
      label: "ערך קבוע",
      typeName: "Fixed",
      flow: "Insurance",
    },
    "Company Signature": {
      name: "Company Signature",
      label: "חותמת חברה",
      typeName: "Company Signature",
      flow: "Insurance",
    },
  };

  const flowIndexDone: Record<string, boolean> = {};
  function getSchemaFieldList(
    children: Record<string, MetaInfo> | undefined,
    flow:
      | "Insurance"
      | "LoanTrack"
      | "Customer"
      | "HealthDeclaration"
      | "Successor",
    path: string
  ) {
    if (!flowIndexDone[flow]) {
      flowIndexDone[flow] = true;
      if (flow !== "Insurance") {
        const fieldName = `${flow}.number`;
        fields[fieldName] = {
          name: fieldName,
          label: fieldName,
          path,
          typeName: fieldName,
          flow,
        };
      }
    }
    Object.values(children || {}).forEach((child, index) => {
      const { name, typeName, meta, children } = child;
      if (children) {
        if (flow === "HealthDeclaration") {
          path = `בריאות: ${`00${index + 1}`.slice(-2)}`;
        }
        getSchemaFieldList(children, flow, path);
      } else {
        const { label, choices } = meta;
        const fieldName = `${flow}.${name}`;

        let useLabel = "<dynamic>";
        if (typeof label === "string") {
          useLabel = label;
        } else {
          const type = unwrap(child.type);
          useLabel = type.type.description ? type.type.description : useLabel;
        }

        if (typeof label !== "string") {
          console.log(`muly:no string label ${fieldName}`, { meta, child });
        }

        // if (fieldName === "HealthDeclaration.diabetes") {
        //   console.log(`muly:DUPLICATE:HealthDeclaration.diabetes ${path}`, {
        //     child,
        //     meta,
        //     fieldName,
        //     typeName,
        //     useLabel,
        //     path,
        //     choices,
        //     flow,
        //     index,
        //   });
        // }

        // &&
        if (
          fields[fieldName] &&
          path !== fields[fieldName]?.path &&
          flow === "HealthDeclaration"
        ) {
          console.log(
            `DUPLICATE FIELD ${formName}:${fieldName}:${path} !== ${fields[fieldName]?.path}`,
            {
              path,
              d: fields[fieldName],
            }
          );
        }

        fields[fieldName] = {
          name: fieldName,
          label: useLabel,
          path,
          typeName,
          choices,
          flow,
        };
      }
    });
  }

  function getPageFieldList(steps: MetaInfo, lastPathName: string) {
    Object.values(steps.children || {}).forEach((step) => {
      const { children, name: stepName } = step;
      let lastSub = "";

      const { flow, loanTrackIdx, successorIdx, customerIdx } =
        getFlowParameters(step.name);

      if (
        flow === "HealthDeclaration" &&
        !step.name.includes(`healthDeclaration_${formName}_`)
      ) {
        return;
      }

      // if (step.name === "customer1_healthDeclaration_eylon_0") {
      //   console.log(
      //     `muly:getOurFieldList customer1_healthDeclaration_eylon_0`,
      //     {
      //       step,
      //       steps,
      //       children,
      //     }
      //   );
      // }

      const stepInfo = step.meta.stepInfo;
      if (stepInfo && stepInfo !== "none") {
        const { name, sub } = stepInfo;
        if (name) {
          lastPathName = name;
        }
        lastSub = sub;
      }

      const path = lastSub ? `${lastPathName} -> ${lastSub}` : "";
      getSchemaFieldList(children, flow, path);
    });
  }

  [AdvisorLifeInsurancePages, CustomerLifeInsurancePages].forEach(
    (wizardPagesDefinition) => {
      const stepsRaw = getPagesZodMetaInfo(wizardPagesDefinition);
      const steps = translateSchemaInfo(
        stepsRaw,
        t,
        wizardPagesDefinition.name
      );

      // console.log(`muly:getOurFieldList ${wizardPagesDefinition.name}`, {
      //   stepsRaw,
      //   steps,
      //   wizardPagesDefinition,
      // });

      const lastPathName = "";
      getPageFieldList(steps, lastPathName);
    }
  );

  fields["CALC: creditCardToken"] = {
    name: "CALC: creditCardToken",
    label: "מחושב: כרטיס אשראי טוקן",
    typeName: "string",
    flow: "Insurance",
  };

  fields["CALC: creditCardExpiration"] = {
    name: "CALC: creditCardExpiration",
    label: "מחושב: כרטיס אשראי תפוגה",
    typeName: "string",
    flow: "Insurance",
  };
  fields["CALC: creditCardExpirationYear"] = {
    name: "CALC: creditCardExpirationYear",
    label: "מחושב: כרטיס אשראי תפוגה שנה",
    typeName: "string",
    flow: "Insurance",
  };
  fields["CALC: creditCardExpirationMonth"] = {
    name: "CALC: creditCardExpirationMonth",
    label: "מחושב: כרטיס אשראי תפוגה חודש",
    typeName: "string",
    flow: "Insurance",
  };

  fields["CALC: Address property"] = {
    name: "CALC: Address property",
    label: "מחושב: כתובת נכס",
    typeName: "calc-address",
    flow: "Insurance",
  };

  fields["CALC: Address customer 1"] = {
    name: "CALC: Address customer 1",
    label: "1 מחושב: כתובת לקוח",
    typeName: "calc-address",
    flow: "Customer",
  };

  fields["CALC: Address customer 2"] = {
    name: "CALC: Address customer 2",
    label: "מחושב: כתובת לקוח 2",
    typeName: "calc-address",
    flow: "Customer",
  };

  fields["CALC: Discount Name"] = {
    name: "CALC: Discount Name",
    label: "מסלול הנחה",
    typeName: "string",
    flow: "Insurance",
  };

  // fields["CALC: Cancel Policy"] = {
  //   name: "CALC: Cancel Policy",
  //   label: "מחושב: החלפת פוליסה הצהרת סוכן",
  //   typeName: "ZodBoolean",
  //   flow: "Insurance",
  //   choices: [
  //     { id: true, title: "כן" },
  //     { id: false, title: "לא" },
  //   ],
  // };

  fields["LoanTrack.CALC_Loan Track Years"] = {
    name: "LoanTrack.CALC_Loan Track Years",
    label: "שנים לסיום הלוואה",
    typeName: "number",
    flow: "LoanTrack",
  };

  fields["LoanTrack.CALC_Loan Track Date"] = {
    name: "LoanTrack.CALC_Loan Track Date",
    label: "תאריך לסיום הלוואה",
    typeName: "string",
    flow: "LoanTrack",
  };

  fields["LoanTrack.CALC_Spitzer & Fixed"] = {
    name: "LoanTrack.CALC_Spitzer & Fixed",
    label: "שפיצר קבוע",
    typeName: "ZodBoolean",
    flow: "LoanTrack",
  };

  fields["LoanTrack.CALC_Spitzer & Change"] = {
    name: "LoanTrack.CALC_Spitzer & Change",
    label: "שפיצר משתנה",
    typeName: "ZodBoolean",
    flow: "LoanTrack",
  };

  fields["LoanTrack.CALC_Balloon & Change"] = {
    name: "LoanTrack.CALC_Balloon & Change",
    label: "בלון משתנה",
    typeName: "ZodBoolean",
    flow: "LoanTrack",
  };

  fields["LoanTrack.CALC_Balloon & Fixed"] = {
    name: "LoanTrack.CALC_Balloon & Fixed",
    label: "בלון קבוע",
    typeName: "ZodBoolean",
    flow: "LoanTrack",
  };

  console.log(`muly:getOurFieldList:fields`, { fields, formName });
  return fields;
};
