import { CalendarDateRangePicker } from "./date3-input";
import { useState } from "react";
import { format, isMatch, isValid } from "date-fns";

const meta = {
  component: CalendarDateRangePicker,
};

export default meta;

export const date3InputEmpty = {
  render: () => <CalendarDateRangePicker />,
};

const Date3InputTest = () => {
  const [value, setValue] = useState<Date | undefined>(new Date("2020-01-01"));

  console.log(`muly:render`, {
    value,
    a: isMatch("2023-02-31", "yyyy-MM-dd"),
    b: isMatch("2023-02-12", "yyyy-MM-dd"),
  });
  return (
    <div>
      <CalendarDateRangePicker
        selected={new Date("2020-01-01")}
        handleDateChange={(val) => {
          console.log(`muly:handleDateChange`, { val });
          setValue(val);
        }}
      />
      <p>
        {value && isValid(value) ? format(value, "dd-MM-yyyy") : "UNDEFINED"}
      </p>
    </div>
  );
};

export const date3InputValue = {
  render: () => <Date3InputTest />,
};

// export const Error = {
//   render: () => <CalendarDatePicker error="Error" />,
// };
