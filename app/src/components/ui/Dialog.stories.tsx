import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON>it<PERSON>,
  DialogTrigger,
} from "./dialog";

import { But<PERSON> } from "./button";

const Dialogs = () => (
  <div className="flex flex-col items-start gap-8">
    <Dialog>
      <DialogTrigger>
        <Button variant="primary">Dialog</Button>
      </DialogTrigger>
      <DialogContent className="bg-white">
        <DialogHeader>
          <h2 className="text-center text-2xl font-bold md:text-4xl">
            המכח הרוצב חוטיב תשיכר
          </h2>
        </DialogHeader>
        <DialogDescription>
          <div className="dir-ltr grid grid-cols-1 gap-4 sm:grid-cols-12">
            <div className="col-span-1 sm:col-span-4">
              <img src="/images/modal-image.svg" className="mx-auto" alt="" />
            </div>
            <div className="col-span-1 text-right sm:col-span-8">
              <p className="text-2xl font-medium text-black">
                ..דיתעב היהי המ עדוי אל דחא ףא
              </p>
              <p className="mt-5 text-2xl font-light">
                ,דיתעב בוט תוחפ תויהל לולע ונלש יתואירבה בצמה .תרחא חוטיב תרבחל
                רובעל ונתיאמ עונמל לוכיו םע חוטיבב רוחבל דואמ בושח ,וישכע רבכ
                ,ןכל התנכשמה תפוקת לכל רתויב לוזה רבטצמה םולשתה .דיתעב תועתפהמ
                ענמיהלו
              </p>
            </div>
          </div>
        </DialogDescription>
        <DialogFooter>
          <div className="mx-auto block w-fit justify-center gap-3 sm:flex sm:w-full sm:justify-center">
            <Button variant="primary">רת חוטיבל ךשמה</Button>
            <Button variant="secondary" className="mt-4 sm:mt-0">
              רת חוטיבל ךשמה
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
);

const meta = {
  component: Dialogs,
};

export default meta;

export const Primary = {
  render: (args: any) => {
    return (
      <div className="mt-4 flex">
        <Dialogs />
      </div>
    );
  },
};
