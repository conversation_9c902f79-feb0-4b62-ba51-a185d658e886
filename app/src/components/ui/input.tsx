import * as React from "react";
import { AlertTriangle } from "lucide-react";
import { cn } from "@/lib/utils";

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: any;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ error, className, ...props }, ref) => {
    return (
      <div className="flex flex-row items-center">
        <input
          className={cn(
            "flex h-10 w-full w-full max-w-[360px] rounded-[7px] border border-input bg-transparent px-4 py-7 text-lg tracking-wider file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-thin placeholder:text-gray-200 placeholder:text-muted-foreground focus:border-black focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50",
            { "border-red-500 pl-12": error },
            className
          )}
          placeholder=""
          ref={ref}
          {...props}
        />
        {error && (
          <label className="m-[-2.5rem]" htmlFor="toggle">
            <AlertTriangle color="red" />
          </label>
        )}
      </div>
    );
  }
);

Input.displayName = "Input";

export { Input };
