import { Dialog } from "@/components/common/dialog";
import { DialogClose } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import React from "react";
import { useTranslation } from "next-i18next";

interface Props {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
}

export const InsuranceCheckDialog = ({ isOpen, setIsOpen }: Props) => {
  const { t } = useTranslation("common");
  return (
    <Dialog
      isOpen={isOpen}
      setIsOpen={setIsOpen}
      title={
        t("customer.lifeInsurance.summary.radio.dontRememberPopUp.title") ?? ""
      }
      trigger={null}
    >
      <div className="text-center">
        <img src="/images/image-card/computer.svg" className="mx-auto" alt="" />
        <p className="text-2xl font-light text-[#6E798C]">
          {t("customer.lifeInsurance.summary.radio.dontRememberPopUp.text") ??
            ""}
        </p>
        <DialogClose>
          <Button variant="primary" className="mx-auto mb-4 mt-10 rounded-3xl">
            {t("customer.lifeInsurance.summary.radio.dontRememberPopUp.next") ??
              ""}
          </Button>
        </DialogClose>
      </div>
    </Dialog>
  );
};
