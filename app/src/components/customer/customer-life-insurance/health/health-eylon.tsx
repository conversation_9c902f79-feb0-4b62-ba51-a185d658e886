import { z } from "zod";
import { Test } from "@/utils/zod-dynamic-validation";
import {
  radio,
  optionalRadio,
  checkbox,
  zNumber,
  displayIf,
  getRadioWithCheckList,
} from "@/utils/zod-utils";
import type { WizardControlProps } from "@/components/common/wizard/useWizardFlow";
import { useTranslation } from "next-i18next";

const page1DisplayMap = {
  isXorHasSmoke: {
    _334_335: {
      true: displayIf({ trueFields: ["_334_335"], parentKey: "isXorHasSmoke" }),
      false: displayIf({
        falseFields: ["_334_335"],
        parentKey: "isXorHasSmoke",
      }),
    },
    _344_345: displayIf({
      trueFields: ["_344_345"],
      falseFields: ["_334_335"],
      parentKey: "isXorHasSmoke",
    }),
  },
  isXorHasDrugs: {
    _354_355: {
      false: displayIf({
        falseFields: ["_354_355"],
        parentKey: "isXorHasDrugs",
      }),
    },
    _past_354_355: displayIf({
      trueFields: ["_past_354_355"],
      falseFields: ["_354_355"],
      parentKey: "isXorHasDrugs",
    }),
  },
  isXorHasAlcohol: {
    _360_361: {
      false: displayIf({
        falseFields: ["_360_361"],
        parentKey: "isXorHasAlcohol",
      }),
    },
    _past_360_361: displayIf({
      trueFields: ["_past_360_361"],
      falseFields: ["_360_361"],
      parentKey: "isXorHasAlcohol",
    }),
  },
};

export const healthEylonPage1 = z.object({
  physicalData: z
    .object({
      height: zNumber.min(0).max(999).describe("Height"),
      weight: zNumber.min(0).max(999).describe("Weight"),
    })
    .describe("Height and weight"),
  medicalInstitution: z
    .string()
    .describe("medical institution")
    .extendMeta({
      className: { label: "hidden" },
      choices: [
        { id: 1, title: "macabi" },
        { id: 2, title: "clalit" },
        { id: 3, title: "meuhedet" },
        { id: 4, title: "leumit" },
      ],
    }),
  isXorHasSmoke: z
    .object({
      _334_335: radio.describe("Are you smoking today ?"),
      _338: zNumber
        .nullish()
        .describe("How many years ?")
        .extendMeta(page1DisplayMap.isXorHasSmoke._334_335.true),
      _339: zNumber
        .nullish()
        .describe("Daily cigarette amount")
        .extendMeta(page1DisplayMap.isXorHasSmoke._334_335.true),
      // TODO - add title of types
      _342_cigarettes: checkbox
        .describe("cigarettes")
        .extendMeta(page1DisplayMap.isXorHasSmoke._334_335.true),
      _342_cigars: checkbox
        .describe("cigars")
        .extendMeta(page1DisplayMap.isXorHasSmoke._334_335.true),
      _342_pipe: checkbox
        .describe("pipe")
        .extendMeta(page1DisplayMap.isXorHasSmoke._334_335.true),
      _342_narghile: checkbox
        .describe("narghile")
        .extendMeta(page1DisplayMap.isXorHasSmoke._334_335.true),
      _344_345: optionalRadio
        .describe("not today, but have you smoked in the past ?")
        .extendMeta(page1DisplayMap.isXorHasSmoke._334_335.false),
      _348: zNumber
        .nullish()
        .describe("How many years have you smoked ?")
        .extendMeta(page1DisplayMap.isXorHasSmoke._344_345),
      _350: z
        .string()
        .nullish()
        .describe("When did you stop smoking ?")
        .extendMeta(page1DisplayMap.isXorHasSmoke._344_345),
      _351: zNumber
        .nullish()
        .describe("Daily cigarette amount")
        .extendMeta(page1DisplayMap.isXorHasSmoke._344_345),
    })
    .superRefine(
      Test("_334_335")
        .every("_338", "_339")
        .some("_342_cigarettes", "_342_cigars", "_342_pipe", "_342_narghile")
        .negEvery("_344_345")
        .Test("_344_345")
        .every("_348", "_350", "_351").submit
    ),

  isXorHasDrugs: z
    .object({
      _354_355: radio.describe("Are you using drugs today ? (q.7)"),
      _past_354_355: optionalRadio
        .describe("not today, but have you used drugs ?")
        .extendMeta(page1DisplayMap.isXorHasDrugs._354_355.false),
      _358: z
        .string()
        .nullish()
        .describe("When did you stop ?")
        .extendMeta(page1DisplayMap.isXorHasDrugs._past_354_355),
    })
    .superRefine(
      Test("_354_355")
        .negEvery("_past_354_355")
        .Test("_past_354_355")
        .every("_358").submit
    ),

  isXorHasAlcohol: z
    .object({
      _360_361: radio.describe(
        "Are you using alcohol today in specified amount? (q.7)"
      ),
      _past_360_361: optionalRadio
        .describe("not today, but have you used alcohol in specified amount?")
        .extendMeta(page1DisplayMap.isXorHasAlcohol._360_361.false),
      _364: z
        .string()
        .nullish()
        .describe("When did you stop ?")
        .extendMeta(page1DisplayMap.isXorHasAlcohol._past_360_361),
    })
    .superRefine(
      Test("_360_361")
        .negEvery("_past_360_361")
        .Test("_past_360_361")
        .every("_364").submit
    ),
});

const page2DisplayMap = {
  hasMedicalExams: {
    _366_367: displayIf({
      trueFields: ["_366_367"],
      parentKey: "hasMedicalExams",
    }),
  },
  disabledStatus: {
    _390_391: displayIf({
      trueFields: ["_390_391"],
      parentKey: "disabledStatus",
    }),
  },
  rejectedInsuranceOffer: {
    _398_399: displayIf({
      trueFields: ["_398_399"],
      parentKey: "rejectedInsuranceOffer",
    }),
  },
  workAbsence: {
    _406_407: displayIf({ trueFields: ["_406_407"], parentKey: "workAbsence" }),
  },
};

export const healthEylonPage2 = z.object({
  // hasMedicalExams: getRadioWithCheckList(
  //   "hasMedicalExams",
  //   [
  //     "Catheterization",
  //     "TumorDetectionTest",
  //     "biopsy",
  //     "CT",
  //     "MRI",
  //     "Heart mapping",
  //     "Echocardiography",
  //     "other",
  //   ],
  //   { displayKey: "hasMedicalExams" }
  // ),
  hasMedicalExams: z
    .object({
      _366_367: radio.describe(
        "Have you undergone in the last 10 years one of the following:"
      ),
      _370: checkbox.describe("Catheterization"),
      _371: checkbox.describe("Tumor detection test"),
      _372: checkbox.describe("biopsy"),
      _373: checkbox.describe("CT"),
      _374: checkbox.describe("MRI"),
      _380: checkbox.describe("Heart mapping"),
      _381: checkbox.describe("Echocardiography"),
      _382: checkbox.describe("other"),
      // _366_367_details: z
      //   .string()
      //   .nullish()
      //   .describe("details")
      //   .extendMeta(page2DisplayMap.hasMedicalExams._366_367),
    })
    .superRefine(
      /*.every("_366_367_details")*/ Test("_366_367").some(
        "_370",
        "_371",
        "_372",
        "_373",
        "_374",
        "_380",
        "_381",
        "_382"
      ).submit
    ),
  _386_387: radio.describe("Have you been Hospitalized or will be soon ?"),
  disabledStatus: z
    .object({
      _390_391: radio.describe("Do you have / had disability ?"),
      _390_391_details: z
        .string()
        .nullish()
        .describe("details of disability")
        .extendMeta(page2DisplayMap.disabledStatus._390_391),
    })
    .superRefine(Test("_390_391").every("_390_391_details").submit),

  _394_395: radio.describe("Are you in the middle of getting disability pct ?"),

  rejectedInsuranceOffer: z
    .object({
      _398_399: radio.describe("Have you beem canceled for ins. ?"),
      _398_399_details: z
        .string()
        .nullish()
        .describe("details of cancelation")
        .extendMeta(page2DisplayMap.rejectedInsuranceOffer._398_399),
    })
    .superRefine(Test("_398_399").every("_398_399_details").submit),

  _402_403: radio.describe("Are you taking medicine ?"),

  workAbsence: z
    .object({
      _406_407: radio.describe(
        "Have you been absent more than 10 days from work last 5 yrs ?"
      ),
      _406_407_time: z
        .string()
        .nullish()
        .describe("when ?")
        .extendMeta(page2DisplayMap.workAbsence._406_407),
      _406_407_reason: z
        .string()
        .nullish()
        .describe("why ?")
        .extendMeta(page2DisplayMap.workAbsence._406_407),
    })
    .superRefine(
      Test("_406_407").every("_406_407_time", "_406_407_reason").submit
    ),
});

const Page3TopMessage = (wizard: WizardControlProps) => {
  const { t } = useTranslation("common");

  return <div>{t("lifeInsurance.healthDeclaration.eylon.page3_before")}</div>;
};

export const healthEylonPage3 = (() => {
  const obj: Record<string, any> = {
    _410_411: radio.describe("q_11"),
    _414_415: radio.describe("q_12"),
    _418_419: radio.describe("q_13"),

    _422_heart_issues: z
      .object({
        _422_423: radio.describe(
          "מחלות לב, הפרעות בלב (כאבים בחזה), יתר לחץ דם ושומנים בדם שאובחנו ב 5- שנים האחרונות, בעיות כלי דם, ורידים/ערקים, מפרצת"
        ),
        _422_1: checkbox.describe("מחלות לב, הפרעות בלב"),
        _422_2: checkbox.describe("יתר לחץ דם ושומנים בדם"),
        _422_3: checkbox.describe("בעיות כלי דם, ורידים/עורקים, מפרצת"),
      })
      .superRefine(
        /*.every("_366_367_details")*/ Test("_422_423").some(
          "_422_1",
          "_422_2",
          "_422_3"
        ).submit
      ),

    _426_427: radio.describe("q_15"),
    _430_431: radio.describe("q_16"),
    _434_435: radio.describe("q_17"),
    _438_439: radio.describe("q_18"),
    _442_443: radio.describe("q_19"),
    _446_447: radio.describe("q_20"),
    _450_451: radio.describe("q_21"),
    _454_455: radio.describe("q_22"),
    _458_459: radio.describe("q_23"),
  };

  const page = z.object(obj).meta({
    beforeElement: (wizard: WizardControlProps) => (
      <Page3TopMessage {...wizard} />
    ),
  });

  return page;
})();
