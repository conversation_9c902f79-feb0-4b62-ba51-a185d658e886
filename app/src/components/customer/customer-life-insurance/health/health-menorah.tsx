import { z } from "zod";
import { Test } from "@/utils/zod-dynamic-validation";
import { healthEylonPage1 } from "@/components/customer/customer-life-insurance/health/health-eylon";
import {
  radio,
  optionalRadio,
  checkbox,
  zNumber,
  displayIf,
  getRadioWithCheckList,
} from "@/utils/zod-utils";
import type { WizardControlProps } from "@/components/common/wizard/useWizardFlow";
import { useTranslation } from "next-i18next";

const page1DisplayMap = {
  isSmoking: displayIf({ trueFields: ["status"], parentKey: "isSmoking" }),
};

export const healthMenorahPage1 = z.object({
  physicalData: healthEylonPage1.shape.physicalData,
  medicalInstitution: healthEylonPage1.shape.medicalInstitution,
  hasDangerHobby: radio.describe("have danger hobbies ?"),
  hasFlightLicense: radio.describe("do you have flight liscense?"),
  isSmoking: z
    .object({
      status: radio.describe("are you smoking or smoked last 2 yrs ?"),
      cigarettes: checkbox
        .describe("cigarettes")
        .extendMeta(page1DisplayMap.isSmoking),
      cigars: checkbox.describe("cigars").extendMeta(page1DisplayMap.isSmoking),
      electonic_cigarette: checkbox
        .describe("electonic_cigarette")
        .extendMeta(page1DisplayMap.isSmoking),
      narghile: checkbox
        .describe("narghile")
        .extendMeta(page1DisplayMap.isSmoking),
      amount: zNumber
        .nullish()
        .describe("daily cig amt.")
        .extendMeta(page1DisplayMap.isSmoking),
    })
    .superRefine(
      Test("status")
        .some("cigarettes", "cigars", "electonic_cigarette", "narghile")
        .every("amount").submit
    ),

  isAlcohol: radio.describe("drinking alcohol ??"),
  isDrugs: radio.describe("drugs?"),
  medicalCheck: radio.describe("in mid of med check?"),
});

const Page2TopMessage = (wizard: WizardControlProps) => {
  return (
    <div>
      האם אובחנה אצלך כיום או בעבר מחלה, תופעה, הפרעה הקשורה באחד או יותר
      מהנושאים המפורטים להלן:
    </div>
  );
};

const Page3TopMessage = (wizard: WizardControlProps) => {
  return (
    <div>
      השאלות הבאות מתייחסות רק למצבים רפואיים עליהם לא נשאלת/הצהרת בשאלות קודמות
    </div>
  );
};

export const healthMenorahPage2 = z
  .object({
    nerveSystem: getRadioWithCheckList(
      "_244_245",
      [
        "nerve",
        "brain",
        "sclerosis",
        "daun",
        "neurofib",
        "gushe",
        "muscle",
        "epileps",
        "perkins",
        "memoryDeter",
      ],
      { displayKey: "nerveSystem" }
    ),
    bloodSystem: getRadioWithCheckList(
      "_255_256",
      [
        "heart",
        "bloodDesease",
        "lungOverflow",
        "avm",
        "coagulation",
        "dvt",
        "pvd",
        "blockage",
      ],
      { displayKey: "bloodSystem" }
    ),
    mental: radio.describe("mental issues?"),
    medicationLastTenYears: getRadioWithCheckList("_268_269", [
      "bloodPressure",
      "diabetesAny",
      "colesterol",
      "trigly",
    ]),
    cancer: getRadioWithCheckList(
      "_275_276",
      ["malignant", "benighn", "preCancer"],
      { displayKey: "cancer" }
    ),
    digestiveSystem: getRadioWithCheckList(
      "_289_290",
      [
        "stomach",
        "gut",
        "gullet",
        "spleen",
        "pancreas",
        "liverDesease",
        "jaundice",
        "oilyLiver",
        "pistola",
        "crohn",
      ],
      { displayKey: "digestiveSystem" }
    ),
    lungsSystem: getRadioWithCheckList(
      "_295_296",
      ["blockageDesese", "cisticFibro"],
      { displayKey: "lungsSystem" }
    ),
    kidneys: getRadioWithCheckList(
      "_303_304",
      ["kidney", "system", "urina", "gland"],
      { displayKey: "kidneys" }
    ),
    infects: getRadioWithCheckList(
      "_311_312",
      ["hiv", "tuberculosis", "sercoid", "seclarodma"],
      { displayKey: "infects" }
    ),
  })
  .meta({
    beforeElement: (wizard: WizardControlProps) => (
      <Page2TopMessage {...wizard} />
    ),
  });

const page3DisplayMap = {
  isCaredMedicines: {
    _324_325: displayIf({
      trueFields: ["_324_325"],
      parentKey: "isCaredMedicines",
    }),
  },
  womenQuestionnare: {
    _377_378: displayIf({
      trueFields: ["_377_378"],
      parentKey: "womenQuestionnare",
    }),
    _374_375: displayIf({
      trueFields: ["_374_375", "_377_378"],
      parentKey: "womenQuestionnare",
    }),
  },
};
export const healthMenorahPage3 = z
  .object({
    hadSurgery: radio.describe("surgery?"),
    hadHospitalized: radio.describe("hadHospitalized"),
    isCaredMedicines: z
      .object({
        _324_325: radio.describe("been taken care of w/ medicines last 5 yrs?"),
        medicineName: z
          .string()
          .nullish()
          .describe("name of medicine?")
          .extendMeta(page3DisplayMap.isCaredMedicines._324_325),
        diagnosis: z
          .string()
          .nullish()
          .describe("diagnosis?")
          .extendMeta(page3DisplayMap.isCaredMedicines._324_325),
      })
      .superRefine(Test("_324_325").every("medicineName", "diagnosis").submit),
    // eyes: getRadioWithCheckList(
    //   "_333_334",
    //   ["shortage", "desease", "obaitis", "blind"],
    //   { displayKey: "eyes" }
    // ),
    // noseEarThroat: getRadioWithCheckList(
    //   "_344_345",
    //   ["ears", "def", "tin", "manyer", "nose", "throat", "vocalCords"],
    //   { displayKey: "noseEarThroat" }
    // ),
    // autoImmune: getRadioWithCheckList(
    //   "_354_355",
    //   ["fmf", "fibromi", "tiredSyndrome", "lopus", "gaut"],
    //   { displayKey: "autoImmune" }
    // ),
    // orthopede: getRadioWithCheckList(
    //   "_366_367",
    //   [
    //     "spine",
    //     "density",
    //     "fractures",
    //     "knees",
    //     "elbow",
    //     "shoulder",
    //     "thighJoint",
    //     "ankles",
    //     "jointDesease",
    //   ],
    //   { displayKey: "orthopede" }
    // ),
    // womenQuestionnare: z
    //   .object({
    //     _377_378: radio.describe("a woman?"),
    //     womb: checkbox
    //       .describe("womb")
    //       .extendMeta(page3DisplayMap.womenQuestionnare._377_378),
    //     ovaries: checkbox
    //       .describe("ovaries")
    //       .extendMeta(page3DisplayMap.womenQuestionnare._377_378),
    //     tumor: checkbox
    //       .describe("tumor")
    //       .extendMeta(page3DisplayMap.womenQuestionnare._377_378),
    //     endomat: checkbox
    //       .describe("endomat")
    //       .extendMeta(page3DisplayMap.womenQuestionnare._377_378),
    //     _374_375: optionalRadio
    //       .describe("woman, also pregnant?")
    //       .extendMeta(page3DisplayMap.womenQuestionnare._377_378),
    //     weightPriorPregnancy: zNumber
    //       .min(0)
    //       .max(999)
    //       .nullish()
    //       .describe("weight before pregnancy?")
    //       .extendMeta(page3DisplayMap.womenQuestionnare._374_375),
    //   })
    //   .superRefine(
    //     Test("_377_378")
    //       .every("_374_375")
    //       .Test("_374_375")
    //       .every("weightPriorPregnancy").submit
    //   ),
    // _381_382: radio.describe("are you crippled?"),
  })
  .meta({
    beforeElement: (wizard: WizardControlProps) => (
      <Page3TopMessage {...wizard} />
    ),
  });
