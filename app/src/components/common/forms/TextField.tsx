import { useMetaEx, useTs<PERSON>ontroller } from "@/components/libs/react-ts-form";
import { RadioButtonGroup } from "./RadioButtonGroup";
import type { ChoiceType, ZodMetaDataItem } from "../../../utils/zod-meta";
import { maybeConvertChild } from "@/components/common/wizard/useWizardFlow";
import { FormControl } from "./FormControl";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Switch } from "@/components/ui/switch";

import { InputPassword } from "@/components/common/forms/InputPassword";
import { SelectField } from "@/components/common/forms/SelectField";
import type { ControlCallback } from "../../../utils/zod-meta";
import { FileUpload } from "@/components/common/forms/file-upload";
import { FileUploadWithSkip } from "@/components/common/forms/file-upload-with-skip";

export interface Props {
  choices?: ChoiceType[];
  // type?: string;
  // controlName?: ZodMetaDataItem["control"];
}

export const TextField = (
  // props: Omit<InputHTMLAttributes<HTMLInputElement>, "name" | "value"> & {
  //   // label?: string;
  //   enumValues?: string[];
  // }
  {}: /* merged to meta */ Props
) => {
  const { field, error, formContext } = useTsController<string>();
  const {
    type,
    choices,
    disabled,
    label,
    placeholder: _placeholder,
    control: controlName,
    classNames,
  } = useMetaEx();

  const placeholder = _placeholder
    ? String(maybeConvertChild(_placeholder, formContext.flowContext))
    : _placeholder;

  // let extraMeta = undefined;
  // if (preprocess) {
  //   // @ts-ignore
  //   extraMeta = preprocess({ formContext });
  // }

  // const options = parseOptionsString(placeholder);
  // if (options.choices && !choices) {
  //   choices = parseChoices(String(options.choices));
  // }

  // console.log(`muly:TextField ${label}`, {
  //   _placeholder,
  //   placeholder,
  //   choices,
  //   type,
  // });

  let showLabel = true;
  let control;
  if (typeof controlName === "function") {
    if (!formContext.flowContext) {
      throw new Error(
        `TextField: controlName is a function but flowContext is not defined`
      );
    }
    const controlFn: ControlCallback = controlName;
    control = controlFn(formContext.flowContext);
    showLabel = !!classNames.showLabel;
  } else if (!choices) {
    if (controlName === "Textarea") {
      control = (
        <Textarea
          data-testid={field.name}
          className={cn({ error: "border-red-500" })}
          name={field.name}
          placeholder={placeholder}
          value={field.value ? field.value + "" : ""}
          onChange={(e) => {
            field.onChange(e.target.value);
          }}
          disabled={disabled}
        />
      );
    } else if (controlName === "File") {
      control = <FileUpload />;
    } else if (controlName === "File-Upload-With-Skip") {
      control = <FileUploadWithSkip />;
    } else if (type === "password") {
      control = <InputPassword />;
    } else {
      // console.log(`muly:TextField`, { error });
      control = (
        <Input
          error={error}
          name={field.name}
          id={field.name}
          data-testid={field.name}
          type={type || undefined}
          placeholder={placeholder}
          value={field.value ? field.value + "" : ""}
          disabled={disabled}
          className={classNames?.input}
          onChange={(e) => {
            field.onChange(e.target.value || undefined);
          }}
        />
      );
    }
  } else if (controlName === "Checkbox") {
    const [valueFalse, valueTrue] =
      typeof choices[0] === "string" ? choices : [false, true];

    showLabel = false;

    control = (
      <div className="flex items-center space-x-2">
        <Checkbox
          // className={clsx(["checkbox", { error: "checkbox-error" }])}
          id={field.name}
          data-testid={field.name}
          name={field.name}
          checked={field.value == valueTrue}
          disabled={disabled}
          onCheckedChange={(checked) => {
            field.onChange(checked ? String(valueTrue) : String(valueFalse));
          }}
        />
        <label
          htmlFor={field.name}
          className="pr-2 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
        >
          {maybeConvertChild(label, formContext.flowContext)}
        </label>
      </div>
    );
  } else if (controlName === "Switch") {
    const [valueFalse, valueTrue] =
      typeof choices[0] === "string" ? choices : [false, true];

    showLabel = false;
    control = (
      <div className="flex items-center space-x-2">
        <Switch
          id={field.name}
          data-testid={field.name}
          name={field.name}
          checked={field.value == valueTrue}
          disabled={disabled}
          onCheckedChange={(checked) => {
            field.onChange(checked ? String(valueTrue) : String(valueFalse));
          }}
        />
        <label
          htmlFor={field.name}
          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
        >
          {maybeConvertChild(label, formContext.flowContext)}
        </label>
      </div>
    );
    // } else if (controlName === "CheckboxGroup") {
    //   control = (
    //     <CheckboxGroup
    //       value={(field.value || "").split(",")}
    //       choices={choices}
    //       onChange={(value) => {
    //         console.log(`muly:onCjange`, { value });
    //         field.onChange(value.join(","));
    //       }}
    //     />
    //   );
  } else if (controlName === "RadioGroup") {
    control = <RadioButtonGroup />;
  } else {
    // console.log(`muly:Select Field ${disabled}`, { props });
    control = <SelectField />;
  }

  // console.log(`muly:TextField ${field.name}`, {
  //   showLabel,
  //   formContext,
  //   classNames,
  // });

  return <FormControl showLabel={showLabel}>{control}</FormControl>;
};
