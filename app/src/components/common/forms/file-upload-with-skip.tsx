/* eslint-disable
      @typescript-eslint/no-unsafe-assignment
*/
import { useTranslation } from "next-i18next";
import React, { useEffect, useCallback } from "react";
import { useMetaEx, useTsController } from "@/components/libs/react-ts-form";
import { Button } from "@/components/ui/button";
import { getSignedUrl } from "@/utils/supabase-storage";
import { useSessionContext } from "@supabase/auth-helpers-react";
import { Trash, Download } from "lucide-react";
import { useDropzone } from "react-dropzone";
import { cn } from "@/lib/utils";
import { Check } from "lucide-react";
import { useFormContext } from "react-hook-form";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import { HelpCircle } from "lucide-react";

/*

Storage select policy, user & admin:
((bucket_id = 'customer_documents'::text) AND (((auth.uid())::text = (storage.foldername(name))[1]) OR ("right"((auth.jwt() ->> 'email'::text), 12) = '@smapy.co.il'::text)))

Storage INSERT policy:
((bucket_id = 'customer_documents'::text) AND ((auth.uid())::text = (storage.foldername(name))[1]))

 */

export const FileUploadWithSkip = () => {
  const { t } = useTranslation("common");

  const { field, error, formContext } = useTsController<string | File | null>();
  const { value, name, onChange } = field;
  const { disabled } = useMetaEx();
  const [imageUrl, setImageUrl] = React.useState<string | null>(null);
  const [isImage, setIsImage] = React.useState<boolean>(true);
  const [fileName, setFileName] = React.useState<string>("");
  const { supabaseClient } = useSessionContext();
  const { watch, getValues, setValue } = useFormContext();

  const checkIsImage = useCallback((filename: string) => {
    const ext = getExtension(filename);
    console.log(`muly:file-upload:`, { filename, ext });
    return ["jpg", "gif", "bmp", "png"].includes(ext?.toLowerCase() || "");
  }, []);

  useEffect(() => {
    void (async () => {
      if (value) {
        console.log(`muly:file-upload:`, { value });

        if (value instanceof File) {
          const url = URL.createObjectURL(value);
          setImageUrl(url);
        } else if (typeof value === "string") {
          if (value.startsWith("{")) {
            const { bucketName, path, name } = JSON.parse(value);
            const fulPath = await getSignedUrl(
              supabaseClient,
              bucketName,
              path
            );
            setImageUrl(fulPath);
            setIsImage(checkIsImage(name));
            setFileName(name);
            setValue("approval_file_later", false);
            console.log(`muly:file-upload:display image: getSignedUrl`, {
              bucketName,
              path,
              fulPath,
              name,
              value,
            });
          } else {
            setImageUrl(value);
          }
        }
      } else {
        setImageUrl(null);
      }
    })();
  }, [value, supabaseClient, checkIsImage, setValue]);

  function getExtension(filename: string) {
    const parts = filename.split(".");
    return parts[parts.length - 1];
  }

  const onDrop = useCallback(
    (acceptedFiles: any) => {
      // Do something with the files
      setIsImage(checkIsImage(acceptedFiles[0].path));
      setFileName(acceptedFiles[0].path);
      console.log(
        "acceptedFiles: " + JSON.stringify(checkIsImage(acceptedFiles[0].path))
      );
      field.onChange(acceptedFiles[0]);
      setValue("approval_file_later", false);
    },
    [checkIsImage, field, setValue]
  );
  const { getRootProps, getInputProps, isDragActive } = useDropzone({ onDrop });

  const downloadFile = () => {
    fetch(imageUrl as string)
      .then((response) => response.blob())
      .then((blob) => {
        // Create a link element
        const link = document.createElement("a");

        // Create a Blob URL for the image Blob
        const url = window.URL.createObjectURL(blob);

        // Set the href and download attributes of the link
        link.href = url;
        link.download = fileName;

        // Append the link to the document
        document.body.appendChild(link);

        // Programmatically click the link to trigger the download
        link.click();

        // Remove the link element from the document
        document.body.removeChild(link);

        // Release the Blob URL
        window.URL.revokeObjectURL(url);
      })
      .catch((error) => console.error("Error downloading image:", error));
  };

  // console.log("imageUrl: " + JSON.stringify(URL.revokeObjectURL(imageUrl)));
  console.log(`muly:file-upload:FileUpload`, {
    imageUrl,
    isImage,
  });

  const selectUploadLater = () => {
    setValue("approval_file_later", true);
    setIsImage(false);
    setFileName("");
    field.onChange(null);
  };
  const isSelectedUploadLater = watch("approval_file_later");
  console.log(`muly:file-upload:FileUpload`, {
    isSelectedUploadLater,
  });

  return (
    <>
      <div className="mx-auto flex w-fit flex-row gap-14">
        <div>
          <Popover>
            <PopoverTrigger tabIndex={-1} type="button">
              <HelpCircle className="-mb-5 -mr-5 text-[#212429]" />
            </PopoverTrigger>
            <PopoverContent className="bg-black text-white">
              <p>
                הזנה ידנית: מכניסים כל פרט בעצמכם. אידאלי למקרים מורכבים או
                כשאין אישור עקרוני.
              </p>
            </PopoverContent>
          </Popover>
          <button
            id="fill-manually"
            type="button"
            className={cn(
              "my-[10px] flex h-40 w-40 flex-col items-center justify-center rounded-[40px] bg-[#F0F1F5] shadow-xl hover:bg-[#e6e9f3] active:border active:border-[#495057]",
              {
                "btn-outline-secondary border border-[#495057]":
                  isSelectedUploadLater,
              }
            )}
            onClick={selectUploadLater}
          >
            <img
              src="/images/writing.svg"
              width={69}
              height={69}
              className="row-span-3"
              alt=""
            />
          </button>
          <p className="mx-auto mt-2 w-fit max-w-full">מילוי אישור עקרוני</p>
        </div>
        <div>
          <Popover>
            <PopoverTrigger tabIndex={-1} type="button">
              <HelpCircle className="-mb-5 -mr-5 text-[#212429]" />
            </PopoverTrigger>
            <PopoverContent className="bg-black text-white">
              <p>
                מעלים אישור עקרוני (PDF) ונותנים למערכת לנתח אותו אוטומטית. חוסך
                לך זמן ומדייק בנתונים.
              </p>
            </PopoverContent>
          </Popover>
          <div className="flex flex-col" id="upload-file">
            {!imageUrl && (
              <div className="mx-auto w-fit" {...getRootProps()}>
                <div className="my-[10px] flex h-40 w-40 flex-col items-center justify-center rounded-[40px] bg-[#F0F1F5] shadow-xl hover:bg-[#e6e9f3]">
                  <img
                    src="/images/upload.svg"
                    width={69}
                    height={69}
                    className="row-span-3"
                    alt=""
                  />
                </div>
                <p className="mx-auto mt-2 w-fit max-w-full">
                  העלאת אישור עקרוני
                </p>
                <input
                  // type="file"
                  name={field.name}
                  className="mt-2 hidden"
                  disabled={disabled}
                  // onChange={(e) => {
                  //   let file;

                  //   if (e.target.files) {
                  //     file = e.target.files[0];
                  //   }

                  //   // @ts-ignore
                  //   field.onChange(file);
                  // }}
                  {...getInputProps()}
                />
              </div>
            )}
            {!!imageUrl && (
              <div className="relative mx-auto" {...getRootProps()}>
                {!isImage && (
                  <div>
                    <div className="mx-auto my-[10px] flex h-40 w-40 flex-col items-center justify-center rounded-[40px] border border-[#495057] bg-[#F0F1F5] shadow-xl hover:bg-[#e6e9f3]">
                      <Check size={50} className="m-0" />
                    </div>
                    <p className="mx-auto mt-2 w-fit max-w-full">{fileName}</p>
                    <input
                      name={field.name}
                      className="mt-2 hidden"
                      // disabled={disabled}
                      {...getInputProps()}
                    />
                  </div>
                )}
                <div
                  className={cn("absolute right-0 top-0 !mt-5 flex gap-4", {
                    "relative mx-auto w-fit": !isImage,
                  })}
                >
                  <Button
                    variant="secondary-outline"
                    className="m-3  w-fit rounded-sm border-0 border-[#e2e8f0] bg-white bg-gradient-to-r from-[#ff5722] to-[#ff9800] !px-3 px-3 py-2 text-white hover:border-0 hover:bg-[#f1f5f9] hover:bg-gradient-to-r hover:from-[#ff5722] hover:to-[#ff9800]"
                    onClick={(event) => {
                      event.stopPropagation();
                      field.onChange(null);
                    }}
                  >
                    <Trash />
                  </Button>
                  {!isImage && (
                    <Button
                      variant="secondary-outline"
                      className="m-3 w-fit rounded-sm border-0 border-[#e2e8f0] bg-white bg-gradient-to-r from-[#ff5722] to-[#ff9800] !px-3 px-3 py-2 text-white hover:border-0 hover:bg-[#f1f5f9] hover:bg-gradient-to-r hover:from-[#ff5722] hover:to-[#ff9800]"
                      onClick={(event) => {
                        event.stopPropagation();
                        downloadFile();
                      }}
                    >
                      <Download />
                    </Button>
                  )}
                </div>
                {isImage && (
                  <img
                    src={imageUrl}
                    // width={400}
                    // height={100}
                    alt="uploaded image"
                    className="mt-4 w-40"
                  />
                )}
              </div>
            )}
          </div>
        </div>
      </div>
      {(isSelectedUploadLater || value) && (
        <Button
          id="form-submit-button"
          variant="primary"
          className="mx-auto mt-8"
          // isLoading={isLoading}
          type="submit"
        >
          המשך
        </Button>
      )}
    </>
  );
};
