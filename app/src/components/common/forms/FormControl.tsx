import { maybeConvertChild } from "@/components/common/wizard/useWizardFlow";
import { Label } from "@/components/ui/label";
import { clsx } from "clsx";
import React from "react";
import {
  useTsController,
  useMetaEx,
} from "@/components/libs/react-ts-form/FieldContext";
import { cn } from "@/lib/utils";
import { HelpCircle } from "lucide-react";
import {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from "@/components/ui/tooltip";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import { useTranslation } from "next-i18next";

interface Props {
  showLabel?: boolean;
  children: React.ReactNode;
}

export const FormControl = ({ children, showLabel }: Props) => {
  const { field, error, formContext } = useTsController<string>();
  const meta = useMetaEx();
  const { tooltip, label, classNames, optional, hint } = meta || {
    label: "",
    placeholder: "",
  };

  // console.log(`muly:FormControl:${field.name}`, { label, classNames, meta });

  return (
    <div
      className={cn([
        { "w-full": !classNames.main, hidden: meta.hidden },
        classNames.main,
      ])}
    >
      {showLabel !== false && (
        <Label htmlFor={field.name} className={cn("mb-8", classNames.label)}>
          {tooltip && (
            <span>
              <Popover>
                <PopoverTrigger tabIndex={-1} type="button">
                  <HelpCircle className="-mb-1.5 ml-2 text-[#212429]" />
                </PopoverTrigger>
                <PopoverContent className="bg-black text-white">
                  <p>{tooltip}</p>
                </PopoverContent>
              </Popover>
            </span>
          )}
          {maybeConvertChild(label, formContext.flowContext)}
          {!optional && <span className="px-1 text-base">*</span>}
        </Label>
      )}
      {!!hint && (
        <p className="mb-3 text-xs font-normal text-[#6E798C]">{hint}</p>
      )}
      {children}
      <label className={cn("label", classNames.error)}>
        {!error ? null : (
          <p className="mt-1.5 text-sm text-red-500">{error?.errorMessage}</p>
        )}

        {/*<span className="label-text-alt">Bottom Left label</span>*/}
        {/*<span className="label-text-alt">Bottom Right label</span>*/}
      </label>
    </div>
  );
};
