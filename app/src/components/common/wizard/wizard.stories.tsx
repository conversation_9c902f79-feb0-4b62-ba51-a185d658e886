import { FormHeader } from "@/components/common/wizard/FormHeader";
import React from "react";
import { WizardTest } from "@/components/common/wizard/wizard-test";
import { AdvisorUpdatePages } from "@/components/advisor/advisor-registration-flow/advisor-registration-flow-schema";
import { AdvisorLifeInsurancePages } from "@/components/advisor/advisor-life-insurance/advisor-life-insurance-schema";
import { TopWizardSteps } from "@/components/common/wizard/top-wizard-steps";

const meta = {
  component: WizardTest,
};

export default meta;

const wizardData = {
  stepsInfo: [
    {
      name: "howDoesItWork",
      code: "0-1",
      layout: "none",
    },
    {
      name: "numberOfCustomers",
      title: "פרטים ראשוניים",
      subTitle: "מבוטחים",
      code: "1-1",
      layout: "steps",
    },
    {
      name: "customer0_details1",
      title: "פרטים ראשוניים",
      subTitle: "פרטים אישיים",
      code: "1-2",
      layout: "steps",
    },
    {
      name: "customer1_details1",
      title: "פרטים ראשוניים",
      subTitle: "פרטים אישיים",
      code: "1-3",
      layout: "steps",
    },
    {
      name: "customer0_details2",
      title: "פרטים ראשוניים",
      subTitle: "פרטים כלליים",
      code: "1-4",
      layout: "steps",
    },
    {
      name: "customer1_details2",
      title: "פרטים ראשוניים",
      subTitle: "פרטים כלליים",
      code: "1-5",
      layout: "steps",
    },
    {
      name: "loanDetailsIntro",
      title: "פרטים ראשוניים",
      subTitle: "פרטי משכנתה",
      code: "1-6",
      layout: "steps",
    },
    {
      name: "loanTracksCount",
      title: "פרטים ראשוניים",
      subTitle: "פרטי משכנתה",
      code: "1-7",
      layout: "steps",
    },
    {
      name: "track0",
      title: "פרטים ראשוניים",
      subTitle: "פרטי משכנתה",
      code: "1-8",
      layout: "steps",
    },
    {
      name: "track1",
      title: "פרטים ראשוניים",
      subTitle: "פרטי משכנתה",
      code: "1-9",
      layout: "steps",
    },
    {
      name: "track2",
      title: "פרטים ראשוניים",
      subTitle: "פרטי משכנתה",
      code: "1-10",
      layout: "steps",
    },
    {
      name: "track3",
      title: "פרטים ראשוניים",
      subTitle: "פרטי משכנתה",
      code: "1-11",
      layout: "steps",
    },
    {
      name: "track4",
      title: "פרטים ראשוניים",
      subTitle: "פרטי משכנתה",
      code: "1-12",
      layout: "steps",
    },
    {
      name: "track5",
      title: "פרטים ראשוניים",
      subTitle: "פרטי משכנתה",
      code: "1-13",
      layout: "steps",
    },
    {
      name: "track6",
      title: "פרטים ראשוניים",
      subTitle: "פרטי משכנתה",
      code: "1-14",
      layout: "steps",
    },
    {
      name: "track7",
      title: "פרטים ראשוניים",
      subTitle: "פרטי משכנתה",
      code: "1-15",
      layout: "steps",
    },
    {
      name: "track8",
      title: "פרטים ראשוניים",
      subTitle: "פרטי משכנתה",
      code: "1-16",
      layout: "steps",
    },
    {
      name: "summarizeDetails",
      title: "קבלת הצעות",
      subTitle: "סיכום פרטי המשכנתה",
      code: "2-1",
      layout: "steps",
    },
    {
      name: "mortgageShowResults",
      title: "קבלת הצעות",
      subTitle: "קבלת הצעות",
      code: "2-2",
      layout: "steps",
    },
    {
      name: "customer0_moreDetails",
      title: "השלמת פרטים",
      subTitle: "השלמת פרטים אישיים",
      code: "3-1",
      layout: "steps",
    },
    {
      name: "customer1_moreDetails",
      title: "השלמת פרטים",
      subTitle: "השלמת פרטים אישיים",
      code: "3-2",
      layout: "steps",
    },
    {
      name: "mortgagePropertyAddress",
      title: "השלמת פרטים",
      subTitle: "כתובת הנכס",
      code: "3-3",
      layout: "steps",
    },
    {
      name: "insuranceDetails",
      title: "השלמת פרטים",
      subTitle: "פרטי ביטוח",
      code: "3-4",
      layout: "steps",
    },
    {
      name: "sendLinksToComplete",
      title: "סיום תהליך",
      subTitle: "אישור וסיום",
      code: "4-1",
      layout: "steps",
    },
    {
      name: "end_askForBuildingInsurance",
      title: "סיום תהליך",
      subTitle: "אישור וסיום",
      code: "4-2",
      layout: "none",
    },
    {
      name: "end",
      title: "סיום תהליך",
      subTitle: "אישור וסיום",
      code: "4-3",
      layout: "none",
    },
  ],
  stepCode: "1-5",
  step: {
    typeName: "ZodObject",
    type: {
      _def: {
        schema: {
          _def: {
            unknownKeys: "strip",
            catchall: {
              _def: {
                typeName: "ZodNever",
              },
              "_constructor-name_": "ZodNever",
            },
            typeName: "ZodObject",
          },
          _cached: null,
          "_constructor-name_": "ZodObject",
        },
        typeName: "ZodEffects",
        effect: {
          type: "refinement",
        },
        description: "General Details",
        meta: {
          text: {
            placeholder: "Customer",
          },
          props: {
            translationKey: "customerDetails2",
          },
          stepInfo: {
            sub: "General details",
          },
        },
      },
      "_constructor-name_": "ZodEffects",
    },
    name: "customer1_details2",
    meta: {
      optional: false,
      props: {
        translationKey: "customerDetails2",
      },
      label: "פרטים כלליים",
      text: {
        placeholder: "מבוטח",
      },
      stepInfo: {
        sub: "פרטים כלליים",
      },
    },
    children: {
      occupation: {
        typeName: "ZodString",
        type: {
          _def: {
            checks: [],
            typeName: "ZodString",
            coerce: false,
            description: "Customer occupation",
          },
        },
        name: "occupation",
        meta: {
          optional: false,
          label: "עיסוק",
        },
      },
      dangerous_hobby_has: {
        typeName: "ZodBoolean",
        type: {
          _def: {
            typeName: "ZodBoolean",
            coerce: true,
            description: "Customer has dangerous hobby?",
            meta: {
              control: "RadioGroup",
              choices: [
                {
                  id: 1,
                  title: "Yes",
                },
                {
                  id: 0,
                  title: "No",
                },
              ],
            },
          },
        },
        name: "dangerous_hobby_has",
        meta: {
          control: "RadioGroup",
          optional: false,
          choices: [
            {
              id: 1,
              title: "כן",
            },
            {
              id: 0,
              title: "לא",
            },
          ],
          label: "האם יש לך תחביב מסוכן?",
        },
      },
      dangerous_hobby: {
        typeName: "ZodString",
        type: {
          _def: {
            innerType: {
              _def: {
                innerType: {
                  _def: {
                    checks: [],
                    typeName: "ZodString",
                    coerce: false,
                  },
                },
                typeName: "ZodNullable",
              },
            },
            typeName: "ZodOptional",
            description: "Hobby",
            meta: {},
          },
        },
        name: "dangerous_hobby",
        meta: {
          optional: true,
          label: "סוג התחביב",
        },
      },
      dangerous_hobby_desc: {
        typeName: "ZodString",
        type: {
          _def: {
            innerType: {
              _def: {
                innerType: {
                  _def: {
                    checks: [],
                    typeName: "ZodString",
                    coerce: false,
                  },
                },
                typeName: "ZodNullable",
              },
            },
            typeName: "ZodOptional",
            description: "Hobby Description",
            meta: {},
          },
        },
        name: "dangerous_hobby_desc",
        meta: {
          optional: true,
          label: "תיאור",
        },
      },
    },
  },
  meta: {
    ns: "common",
    label: "ביטוח חיים למשכנתה ",
  },
};

export const Header = {
  ...WizardTest,
  args: { pages: AdvisorUpdatePages, step: "0-1", component: "FormHeader" },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/sAvmr55UwcNhZNAJ9XGpA6/smapy-landing-page?node-id=294-45114&t=Omiw4Kd4jROxXo3N-4",
    },
  },
};

export const WithTopStepper = {
  ...WizardTest,
  args: { pages: AdvisorLifeInsurancePages, step: "1-2" },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/file/sAvmr55UwcNhZNAJ9XGpA6/smapy-landing-page?node-id=294-45114&t=Omiw4Kd4jROxXo3N-4",
    },
  },
};

const TopWizardStepsTest = ({ step }: { step: string }) => {
  // @ts-ignore
  return <TopWizardSteps wizard={{ ...wizardData, stepCode: step }} />;
};

export const TopWizardAllSteps = {
  render: () => (
    <div className="bg-[#e6e7e3] max-[601px]:w-full max-[601px]:p-8 ">
      <TopWizardStepsTest step="0-1" />
      <TopWizardStepsTest step="1-1" />
      <TopWizardStepsTest step="1-2" />
      <TopWizardStepsTest step="1-3" />
      <TopWizardStepsTest step="1-4" />
      <TopWizardStepsTest step="1-5" />
      <TopWizardStepsTest step="1-6" />
      <TopWizardStepsTest step="1-7" />
      <TopWizardStepsTest step="1-8" />
      <TopWizardStepsTest step="1-9" />
      <TopWizardStepsTest step="1-10" />
      <TopWizardStepsTest step="1-11" />
      <TopWizardStepsTest step="1-12" />
      <TopWizardStepsTest step="1-13" />
      <TopWizardStepsTest step="1-14" />
      <TopWizardStepsTest step="1-15" />
      <TopWizardStepsTest step="1-16" />
      <TopWizardStepsTest step="2-1" />
      <TopWizardStepsTest step="2-2" />
      <TopWizardStepsTest step="3-1" />
      <TopWizardStepsTest step="3-2" />
      <TopWizardStepsTest step="3-3" />
      <TopWizardStepsTest step="3-4" />
      <TopWizardStepsTest step="4-1" />
      <TopWizardStepsTest step="4-2" />
      <TopWizardStepsTest step="4-3" />
    </div>
  ),
};

export const TopWizardSteps0 = {
  render: () => (
    <div className="bg-[#e6e7e3] max-[601px]:w-full max-[601px]:p-8 ">
      <TopWizardStepsTest step="1-1" />
      {/*<TopWizardStepsTest step="1-3" />*/}
      {/*<TopWizardStepsTest step="1-4" />*/}
      {/*<TopWizardStepsTest step="1-6" />*/}
    </div>
  ),
};

export const TopWizardSteps33 = {
  render: () => (
    <div className="bg-[#e6e7e3] max-[601px]:w-full max-[601px]:p-8 ">
      {/*<TopWizardStepsTest step="1-1" />*/}
      <TopWizardStepsTest step="1-3" />
      {/*<TopWizardStepsTest step="1-4" />*/}
      {/*<TopWizardStepsTest step="1-6" />*/}
    </div>
  ),
};

export const TopWizardSteps66 = {
  render: () => (
    <div className="bg-[#e6e7e3] max-[601px]:w-full max-[601px]:p-8 ">
      {/*<TopWizardStepsTest step="1-1" />*/}
      {/*<TopWizardStepsTest step="1-2" />*/}
      <TopWizardStepsTest step="1-4" />
      {/*<TopWizardStepsTest step="1-6" />*/}
    </div>
  ),
};

export const TopWizardSteps100 = {
  render: () => (
    <div className="bg-[#e6e7e3] max-[601px]:w-full max-[601px]:p-8 ">
      {/*<TopWizardStepsTest step="1-1" />*/}
      {/*<TopWizardStepsTest step="1-2" />*/}
      {/*<TopWizardStepsTest step="1-3" />*/}
      <TopWizardStepsTest step="1-6" />
    </div>
  ),
};
