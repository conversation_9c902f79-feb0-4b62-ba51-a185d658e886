export type nullish<T> = T | null | undefined;

export const trimStringSpaces = (record: any) => {
  if (record && typeof record === "object") {
    const keys = Object.keys(record);
    for (const key of keys) {
      if (typeof record[key] === "string") {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-call
        record[key] = record[key].trim();
      } else if (typeof record[key] === "object") {
        record[key] = trimStringSpaces(record[key]);
      }
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unsafe-return
  return record;
};
