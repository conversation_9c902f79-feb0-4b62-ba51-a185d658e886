import { Layout } from "react-admin";
import { AppBar } from "react-admin";
import { useSessionContext } from "@supabase/auth-helpers-react";
import { MenuItemLink } from "react-admin";
import ExitToAppIcon from "@mui/icons-material/ExitToApp";
import { UserMenu as RaUserMenu } from "react-admin";

export const MyAppBar = (props: any) => {
  const { supabaseClient } = useSessionContext();

  const logout = async () => {
    await supabaseClient.auth.signOut();
  };

  // console.log(`muly:MyAppBar`, { props });
  return <AppBar {...props} userMenu={<UserMenu />} />;
};

const UserMenu = (props: any) => {
  const { supabaseClient } = useSessionContext();

  const logout = async () => {
    await supabaseClient.auth.signOut();
  };

  return (
    <RaUserMenu {...props}>
      <MenuItemLink
        to="/"
        primaryText="Logout"
        leftIcon={<ExitToAppIcon />}
        onClick={logout}
      />
    </RaUserMenu>
  );
};

export const AdminCustomLayout = (props: any) => (
  <Layout {...props} appBar={MyAppBar} />
);
