import {
  EditButton,
  List,
  Datagrid,
  TextField,
  DateField,
  BooleanField,
  ReferenceField,
  FunctionField,
  useListContext,
  TextInput,
  SearchInput,
  ReferenceInput,
  SelectInput,
  AutocompleteInput,
  SimpleForm,
  required,
  Create,
  Edit,
  NumberField,
  SingleFieldList,
  DateInput,
  NumberInput,
  TabbedForm,
  FormTab,
  SelectArrayInput,
  useRecordContext,
  ReferenceArrayField,
  ReferenceManyField,
  ChipField,
  FormDataConsumer,
  ReferenceArrayInput,
  AutocompleteArrayInput,
  useCreatePath,
  SelectField,
  ArrayInput,
  SimpleFormIterator,
  BooleanInput,
} from "react-admin";
import { ListActions } from "../ListActions";
import React, { useState } from "react";
import { companiesByName } from "@/components/misc/companies";
import { Row } from "@/admin/components/Row";

const DiscountPeriod = [
  { id: "monthly", name: "monthly" },
  { id: "yearly", name: "yearly" },
];

const getCompanyChoices = () => {
  return Object.keys(companiesByName).map((e) => ({
    id: e,
    name: companiesByName[e]!.he,
  }));
};

const filters = [
  <TextInput source="name@ilike" alwaysOn key="name" label="Name" />,
  <SelectInput
    choices={getCompanyChoices()}
    source="company"
    alwaysOn
    label="Insurance Company"
    key="insurance_company"
  />,
  <TextInput source="id@ilike" alwaysOn key="id" label="ID" />,
];

export const discountsList = (props: any) => {
  return (
    <List {...props} actions={<ListActions />} filters={filters}>
      <Datagrid>
        <TextField source="name" label="Name" />
        <TextField source="description" />
        <SelectField source="company" choices={getCompanyChoices()} />
        <TextField source="minAge" />
        <TextField source="maxAge" />
        <TextField source="minValue" />
        <TextField source="maxValue" />
        <TextField source="minPremium" />
        <BooleanField source="active" />
        <BooleanField source="testActive" />
        <EditButton />
      </Datagrid>
    </List>
  );
};

const Form = () => {
  return (
    <SimpleForm>
      <BooleanInput source="active" />
      <BooleanInput source="testActive" />
      <TextInput fullWidth source="name" />
      <TextInput fullWidth source="description" />
      <SelectInput fullWidth source="company" choices={getCompanyChoices()} />
      <Row>
        <NumberInput fullWidth source="minAge" />
        <NumberInput fullWidth source="maxAge" />
      </Row>
      <Row>
        <NumberInput fullWidth source="minValue" />
        <NumberInput fullWidth source="maxValue" />
      </Row>
      <Row>
        <NumberInput fullWidth source="minPremium" />
      </Row>
      <Row>
        <NumberInput fullWidth source="minYears" />
        <NumberInput fullWidth source="maxYears" />
        <NumberInput fullWidth source="minCustomerCount" />
        <NumberInput fullWidth source="maxCustomerCount" />
      </Row>

      <ArrayInput source="discounts">
        <SimpleFormIterator inline>
          <NumberInput source="discount" label="Discount %" />
          <NumberInput source="yearStart" />
          <NumberInput source="monthStart" />
          <NumberInput source="yearEnd" />
          <NumberInput source="monthEnd" />
        </SimpleFormIterator>
      </ArrayInput>
    </SimpleForm>
  );
};

export const discountEdit = () => (
  <Edit>
    <Form />
  </Edit>
);

export const discountCreate = () => {
  return (
    <Create title="Discount Create">
      <Form />
    </Create>
  );
};
