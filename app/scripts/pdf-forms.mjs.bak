// yarn pdf-forms

import {
  PDFButton,
  PDFCheckBox,
  PDFDropdown,
  PDFOptionList,
  PDFTextField,
  PDFRadioGroup,
  PDFSignature,
  PDFAcroPushButton,
  PDFAcroCheckBox,
  PDFAcroComboBox,
  PDFAcroListBox,
  PDFAcroText,
  PDFAcroRadioButton,
  PDFAcroSignature,
  PDFRef,
  PDFAcroField,
  PDFDocument,
  PDFString,
  PDFName,
  PDFBool,
  rgb,
  createPDFAcroFields, drawObject, rotateInPlace,
} from "pdf-lib";
import PDFParser from "pdf2json";
import { readFileSync, writeFileSync } from "fs";
import path from "path";

const { db } = argv;
console.log(`muly:ARGV`, { db, argv });

async function getFormFieldsNames(pdfPath) {
  const pdfBytes = readFileSync(pdfPath);
  const pdfDoc = await PDFDocument.load(pdfBytes);
  const form = pdfDoc.getForm();
  const fields = form.getFields();
  const fieldNames = fields.map((field) => ({
    name: field.getName(),
    type: field.constructor.name,
  }));

  // console.log(`muly:getFormFieldsNames`, {
  //   ac: fields[0].acroField,
  // });

  return fieldNames;
}

async function getFormFieldsNames2(pdfPath) {
  let pdfParser = new PDFParser();

  pdfParser.on("pdfParser_dataError", (errData) =>
    console.error(errData.parserError)
  );
  pdfParser.on("pdfParser_dataReady", (pdfData) => {
    extractPDFObjects(pdfData);
    // console.log("DATA:", { pdfData });
    writeFileSync("../tmp/pdfData.json", JSON.stringify(pdfData, null, 2));
  });

  return pdfParser.loadPDF(pdfPath);
}

function decodeString(encodedString) {
  let decodedString = decodeURIComponent(encodedString);
  let reversedString = decodedString.split("").reverse().join("");
  return reversedString;
}

function extractPDFObjects(pdfData) {
  const scanObjects = (obj, pageIdx) => {
    if (typeof obj === "object") {
      Object.entries(obj).forEach(([key, value]) => {
        if (key === "R" && value[0].T) {
          pdfStrings.push({
            x: obj.x,
            y: obj.y,
            w: obj.w,
            R: decodeString(value[0].T),
            pageIdx,
          });
        } else if (key === "T" && obj.id?.Id) {
          pdfFields.push({
            x: obj.x,
            y: obj.y,
            w: obj.w,
            h: obj.h,
            id: obj.id.Id,
            type: obj.T.Name,
            pageIdx,
          });
        } else {
          scanObjects(value, pageIdx);
        }
      });
    }
  };

  // console.log(`muly:extractPDFObjects`, { pdfData });
  const pdfStrings = [];
  const pdfFields = [];
  pdfData.Pages.forEach((page, idx) => scanObjects(page, idx));

  pdfFields.forEach((field) => {
    const fieldStrings = pdfStrings
      .filter(({ pageIdx }) => pageIdx === field.pageIdx)
      .filter(({ x }) => x > field.x)
      .map(({ x, y, w, R }) => ({
        x: Math.abs(field.x - x),
        y: Math.abs(field.y - y),
        sx: x,
        sy: y,
        R,
      }))
      .sort((a, b) => (a.y - b.y) * 10000 + a.x - b.x)
      .slice(0, 10);

    field.strings = fieldStrings;
  });

  writeFileSync("../tmp/pdfString.json", JSON.stringify(pdfStrings, null, 2));
  writeFileSync("../tmp/pdfFields.json", JSON.stringify(pdfFields, null, 2));
}

// extractPDFObjects(JSON.parse(readFileSync("../tmp/pdfData.json", "utf8")));

function getRectsFromField(field, doc) {
  const widgets = field.acroField.getWidgets();

  if (doc) {
    return widgets.map((q) => {
      const rect = q.getRectangle();
      const pageNumber = doc.getPages().findIndex((x) => x.ref == q.P());
      rect.pageNumber = pageNumber;
      return rect;
    });
  } else return widgets.map((q) => q.getRectangle());
}

// https://github.com/Hopding/pdf-lib/blob/b8a44bd24b74f4f32456e9809dc4d2d9dc9bf176/src/api/form/PDFForm.ts#LL796C26-L796C26
const convertToPDFField = (field, ref, doc) => {
  if (field instanceof PDFAcroPushButton) return PDFButton.of(field, ref, doc);
  if (field instanceof PDFAcroCheckBox) return PDFCheckBox.of(field, ref, doc);
  if (field instanceof PDFAcroComboBox) return PDFDropdown.of(field, ref, doc);
  if (field instanceof PDFAcroListBox) return PDFOptionList.of(field, ref, doc);
  if (field instanceof PDFAcroText) return PDFTextField.of(field, ref, doc);
  if (field instanceof PDFAcroRadioButton) {
    return PDFRadioGroup.of(field, ref, doc);
  }
  if (field instanceof PDFAcroSignature) {
    return PDFSignature.of(field, ref, doc);
  }
  return undefined;
};

const convertFieldToWidget = (form, field) => {
   const translate = (xPos, yPos) =>
    concatTransformationMatrix(1, 0, 0, 1, xPos, yPos);

  const widgets = field.acroField.getWidgets();
  for (let j = 0, lenWidgets = widgets.length; j < lenWidgets; j++) {
    const widget = widgets[j];
    const page = form.findWidgetPage(widget);
    const widgetRef = form.findWidgetAppearanceRef(field, widget);

    const xObjectKey = page.node.newXObject("FlatWidget", widgetRef);

    const rectangle = widget.getRectangle();
    const operators = [
      pushGraphicsState(),
      translate(rectangle.x, rectangle.y),
      translate(0, 0),
      ...rotateInPlace({ ...rectangle, rotation: 0 }),
      drawObject(xObjectKey),
      popGraphicsState(),
    ].filter(Boolean);

    page.pushOperators(...operators);
  }
};

const writeFieldsLabels = async (pdfPath, pdfOutPath) => {
  let fileName = path.basename(pdfPath, path.extname(pdfPath));

  const pdf_form_name = fileName;
  const pdfBytes = readFileSync(pdfPath);
  const pdfDoc = await PDFDocument.load(pdfBytes);
  const form = pdfDoc.getForm();
  const fields = form.getFields();
  // .filter(
  //   (f) =>
  //     f.getName() === "PayerRelationLink" || f.getName() === "BankBranchCodeB"
  // );

  // Handle duplicate field with same name https://github.com/Hopding/pdf-lib/issues/1185
  // console.log(`muly:writeFieldsLabels`, {
  //   fields,
  //   k: fields[0].acroField.Kids(),
  // });

  const fieldsNamesDict = {};
  const expandedFields = [];
  for (const field of fields) {
    if (field.acroField.Kids() && field.acroField.Kids().asArray().length > 1) {
      //   if (field.getName().startsWith("FirstNameSpouse")) {
      //     console.log(`muly:writeFieldsLabels ${field.getName()}`);
      //
      //     const kids = createPDFAcroFields(field.acroField.Kids()).map(
      //       (_) => _[0]
      //     );
      //
      //     kids.forEach((kid) => {
      //       console.log(`muly:kid`, { kid });
      //       kid.setValue(PDFString.of("FILL"));
      //     });
      //   }

      const kids = createPDFAcroFields(field.acroField.Kids()).map((_) =>
        convertToPDFField(_[0], _[1], pdfDoc)
      );
      // console.log(`muly:writeFieldsLabels multiple kids ${field.getName()}`, {
      //   a: field.acroField.Kids().asArray(),
      //   kids,
      // });
      expandedFields.push(...kids);
      kids.forEach((field, idx) => {
        let name = field.getName().replace(".undefined", "");
        fieldsNamesDict[field.ref.objectNumber] = `${name}.${idx + 1}`;
      });
    } else {
      expandedFields.push(field);
      fieldsNamesDict[field.ref.objectNumber] = field.getName();
      // console.log(`muly:writeFieldsLabels one field ${field.getName()}`, {
      //   field,
      // });
    }
  }

  const pages = pdfDoc.getPages();
  const labelColor = rgb(1, 0, 0); // Red color
  const yTolerance = 4; // Tolerance for Y-coordinate comparisons

  expandedFields.sort((a, b) => {
    const arect = getRectsFromField(a, pdfDoc);
    const brect = getRectsFromField(b, pdfDoc);
    if (arect[0].pageNumber !== brect[0].pageNumber) {
      return arect[0].pageNumber - brect[0].pageNumber;
    }
    if (Math.abs(arect[0].y - brect[0].y) > yTolerance) {
      return brect[0].y - arect[0].y;
    }
    // reversed for hebrew
    return brect[0].x - arect[0].x;
  });

  const types = {
    PDFTextField: "text",
    PDFCheckBox: "checkbox",
    PDFRadioGroup: "radio",
    PDFDropdown: "dropdown",
    PDFSignature: "signature",
  };

  const info = [];
  let idx = 0;
  for (const field of expandedFields) {
    idx++;

    if (idx !== 33) {
      continue;
    }

    const rect = getRectsFromField(field, pdfDoc);
    const name = fieldsNamesDict[field.ref.objectNumber];
    if (!name.startsWith(field.getName().replace(".undefined", ""))) {
      throw new Error(`name mismatch ${name} ${field.getName()}`);
    }
    const type = field.constructor.name;

    const labelText = `${idx.toString().padStart(3, "0")}:${name}`;

    const { x, y, w, h, pageNumber } = rect[0];

    const labelX = x; // Adjust the label's X-coordinate as needed
    const labelY = y; // Adjust the label's Y-coordinate as needed

    const page = pages[pageNumber];

    if (field instanceof PDFTextField) {
      field.setText(
        labelText.substring(0, field.getMaxLength() || labelText.length)
      );
    }
    convertFieldToWidget(form, field);

    console.log(`muly:writeFieldsLabels`, {
      lenWidgets: widgets.length,
      widgets,
      field,
      rect,
      name,
      type,
      page,
      pageNumber,
      // d: field.acroField.dict,
      // j: field.acroField.dict.keys(),
    });

    // field.acroField.dict.set(PDFName.of("NeedAppearances"), PDFBool.True);
    // const superheroField = form.createTextField("debug");
    // superheroField.setText("DEBUG");
    // // superheroField.setFontSize(4);
    // superheroField.addToPage(page, { x, y, width: w, height: h });

    const label = page.drawText(labelText, {
      x: labelX,
      y: labelY,
      color: labelColor,
      size: 6,
    });

    if (!types[type]) {
      throw new Error(`unknown type ${type}`);
    }

    info.push({
      pdf_form_name,
      pdf_page_number: pageNumber,
      pdf_field_object_number: field.ref.objectNumber,
      pdf_sort_idx: idx,
      pdf_field_name: name,
      pdf_field_type: types[type],
    });
  }

  // pdfDoc
  //   .getForm()
  //   .acroForm.dict.set(PDFName.of("NeedAppearances"), PDFBool.True);

  // form.flatten({ updateFieldAppearances: true });
  const modifiedPdfBytes = await pdfDoc.save();
  writeFileSync(pdfOutPath, modifiedPdfBytes);
  writeFileSync("../tmp/field-info.json", JSON.stringify(info, null, 2));
  console.log(
    `wrote pdf with ${expandedFields.length} fields to ${pdfOutPath}`
  );

  if (db) {
    await upsertToDB(info);
  }
};

const upsertToDB = async (fieldList) => {
  const checkUniqueness = async (fieldName) => {
    const query = `
    SELECT pdf_form_name, ${fieldName}, COUNT(*)
    FROM "pdfForm"
    WHERE "deletedAt" IS NULL
    GROUP BY pdf_form_name, ${fieldName}
    HAVING COUNT(*) > 1;
  `;

    const res = await client.query(query);

    for (let row of res.rows) {
      console.log(
        `Non-unique combination: pdf_form_name = ${row.pdf_form_name}, pdf_sort_idx = ${row.pdf_sort_idx}`
      );
    }

    if (res.rows.length > 0) {
      throw new Error(
        `Non-unique combination: pdf_form_name = ${res.rows[0].pdf_form_name}, pdf_sort_idx = ${res.rows[0].pdf_sort_idx}`
      );
    }
  };

  const { Client } = require("pg");

  const connectionString = process.env.DATABASE_URL;
  const client = new Client({
    connectionString,
  });
  client.connect();

  const query = `
    INSERT INTO "pdfForm" (
      pdf_form_name,
      pdf_page_number,
      pdf_field_object_number,
      pdf_sort_idx,
      pdf_field_name,
      pdf_field_type
    ) VALUES (
      $1,
      $2,
      $3,
      $4,
      $5,
      $6
    )
    ON CONFLICT (pdf_form_name, pdf_field_name)
    DO UPDATE SET
      pdf_page_number = EXCLUDED.pdf_page_number,
      pdf_sort_idx = EXCLUDED.pdf_sort_idx,
      pdf_field_type = EXCLUDED.pdf_field_type,
      pdf_field_object_number = EXCLUDED.pdf_field_object_number
    RETURNING *;
  `;

  let count = 0;
  for (let data of fieldList) {
    const values = [
      data.pdf_form_name,
      data.pdf_page_number,
      data.pdf_field_object_number,
      data.pdf_sort_idx,
      data.pdf_field_name,
      data.pdf_field_type,
    ];
    await client.query(query, values);
    count++;
  }

  // Get all records from the pdfForm table
  const result = await client.query(
    'SELECT pdf_form_name, pdf_field_name FROM "pdfForm" WHERE "deletedAt" IS NULL'
  );
  const currentRecords = result.rows.map(
    (row) => `${row.pdf_form_name},${row.pdf_field_name}`
  );

  // Generate a set of unique pdf_form_name, pdf_field_name pairs from the fieldList
  const fieldSet = new Set(
    fieldList.map((data) => `${data.pdf_form_name},${data.pdf_field_name}`)
  );

  // Find records that are in currentRecords but not in fieldSet
  const recordsToDelete = currentRecords.filter(
    (record) => !fieldSet.has(record)
  );

  // Mark these records as deleted in the database
  for (let record of recordsToDelete) {
    const [pdf_form_name, pdf_field_name] = record.split(",");
    await client.query(
      `
      UPDATE "pdfForm"
      SET "deletedAt" = NOW()
      WHERE pdf_form_name = $1 AND pdf_field_name = $2
    `,
      [pdf_form_name, pdf_field_name]
    );
  }

  await checkUniqueness("pdf_sort_idx");
  await checkUniqueness("pdf_field_object_number");

  client.end();

  console.log(
    `muly:upsertToDB ${count} records, deleted ${recordsToDelete.length}`
  );
};

const outputFolderPath = "../tmp/";

async function processPDFs(directory) {
  const files = await fs.readdir(directory);

  for (const file of files) {
    const filePath = path.join(directory, file);
    const fileStat = await fs.stat(filePath);

    // If the file is a directory, skip it
    if (fileStat.isDirectory()) continue;

    // If the file is not a PDF, skip it
    if (path.extname(file) !== ".pdf") continue;

    // Assuming the writeFieldsLabels function takes input file path and output file path
    const outputPath = path.join(outputFolderPath, file);
    await writeFieldsLabels(filePath, outputPath);
  }
}

const pdfPath = "./server/process/pdf-forms/eylon.pdf";
const answer = await getFormFieldsNames2(pdfPath);

await writeFieldsLabels(pdfPath, "../tmp/out.pdf"); // One

// const pdfFolderPath = "./server/process/pdf-forms/";
// await processPDFs(pdfFolderPath); // All
