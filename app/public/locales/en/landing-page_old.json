{"about": {"about_us": {"header": "about.about_us.header"}, "aboutus": {"header": "about.aboutus.header"}, "buy": "about.buy", "header": "about.header", "profile": {"1": {"name": "about.profile.1.name", "role_en": "about.profile.1.role_en", "role_he": "about.profile.1.role_he", "text": "about.profile.1.text"}, "2": {"name": "about.profile.2.name", "role_en": "about.profile.2.role_en", "role_he": "about.profile.2.role_he", "text": "about.profile.2.text"}, "3": {"name": "about.profile.3.name", "role_en": "about.profile.3.role_en", "role_he": "about.profile.3.role_he", "text": "about.profile.3.text"}}, "tech": {"header": "about.tech.header", "text": "about.tech.text"}, "us": "about.us", "vision": {"header": "about.vision.header", "header_2": "about.vision.header_2", "more_info": "about.vision.more_info", "text": "about.vision.text"}, "vison": {"text": "about.vison.text"}}, "contact": {"header": "contact.header"}, "Error sending sms otp: vonage error: Quota Exceeded - rejected (status: 9)": "Error sending sms otp: vonage error: <PERSON><PERSON><PERSON> Exceeded - rejected (status: 9)", "faqData": {"contact": {"header": "faqData.contact.header", "text": "faqData.contact.text"}, "header": "faqData.header", "questions": {"header": "faqData.questions.header"}, "text": "faqData.text"}, "floating": {"1": "floating.1", "2": "Mortgage insurance", "3": "Property insurance", "link_text": "compare & buy"}, "header": "english - header", "header_2": "header_2", "index": {"floating": {"1": "index.floating.1", "2": "Mortgage insurance", "3": "Property insurance", "link_text": "compare & buy"}, "header": "index.header", "header_2": "index.header_2", "sections": {"1": {"header": "index.sections.1.header", "text": {"1": "index.sections.1.text.1", "2": "index.sections.1.text.2"}}, "2": {"header": "index.sections.2.header", "steps": {"1": {"text": "index.sections.2.steps.1.text", "title": "index.sections.2.steps.1.title"}, "2": {"text": "index.sections.2.steps.2.text", "title": "index.sections.2.steps.2.title"}, "3": {"text": "index.sections.2.steps.3.text", "title": "index.sections.2.steps.3.title"}, "4": {"text": "index.sections.2.steps.4.text", "title": "index.sections.2.steps.4.title"}}}, "3": {"header": "index.sections.3.header"}, "4": {"header": "index.sections.4.header", "reasons": {"1": {"text": "index.sections.4.reasons.1.text", "title": "index.sections.4.reasons.1.title"}, "2": {"text": "index.sections.4.reasons.2.text", "title": "index.sections.4.reasons.2.title"}, "3": {"text": "index.sections.4.reasons.3.text", "title": "index.sections.4.reasons.3.title"}, "4": {"text": "index.sections.4.reasons.4.text", "title": "index.sections.4.reasons.4.title"}}}, "5": {"cards": {"1": {"date": "index.sections.5.cards.1.date", "name": "index.sections.5.cards.1.name", "text": "index.sections.5.cards.1.text"}, "2": {"date": "index.sections.5.cards.2.date", "name": "index.sections.5.cards.2.name", "text": "index.sections.5.cards.2.text"}, "3": {"date": "index.sections.5.cards.3.date", "name": "index.sections.5.cards.3.name", "text": "index.sections.5.cards.3.text"}, "4": {"date": "index.sections.5.cards.4.date", "name": "index.sections.5.cards.4.name", "text": "index.sections.5.cards.4.text"}}, "header": "index.sections.5.header"}, "6": {"faqs": {"1": {"a": "index.sections.6.faqs.1.a", "q": "index.sections.6.faqs.1.q"}, "2": {"a": "index.sections.6.faqs.2.a", "q": "index.sections.6.faqs.2.q"}, "3": {"a": "index.sections.6.faqs.3.a", "q": "index.sections.6.faqs.3.q"}}, "header": "index.sections.6.header", "more_faqs": "index.sections.6.more_faqs", "questions": {"1": "index.sections.6.questions.1", "2": "index.sections.6.questions.2", "3": "index.sections.6.questions.3"}}, "7": {"header": "index.sections.7.header", "text": "index.sections.7.text"}}}, "login": {"entry": "", "entry_advisor": "Advisor entry", "entry_customer": "Customer entry", "error_mismatch": "Phone is registered with different id", "id_card_number": {"label": "ID Card Number"}, "label": "Happy to see you", "phone": {"label": "Phone Number"}, "placeholder": "Happy to see you", "welcome_continue": "Continue"}, "nav": {"about": "nav.about", "agent_entry": "nav.agent_entry", "customer_entry": "nav.customer_entry", "faqData": "nav.faqData", "price": "nav.price", "security": "nav.security"}, "Not a valid phone number": "Not a valid phone number", "Not valid israeli id number": "Not valid israeli id number", "otp": {"entry_advisor": "Advisor entry", "entry_customer": "Customer entry", "label": "Confirmation and continue", "next": "otp.next", "pin": {"label": "What is your code"}, "placeholder": "Sent you confirmation code in SMS message"}, "price": {"0": {"1": "price.0.1", "2": "price.0.2", "3": "price.0.3", "4": "price.0.4", "5": "price.0.5"}, "1": {"name": "price.1.name", "price": "price.1.price"}, "2": {"name": "price.2.name", "price": "price.2.price"}, "3": {"extra_line": "price.3.extra_line", "name": "price.3.name", "price": "price.3.price"}, "button": {"buy": "price.button.buy"}, "cost": "price.cost", "cost_service": "price.cost_service", "footer": "price.footer", "footer2": "price.footer2", "free": "price.free", "header": "price.header", "header2": "price.header2", "text": "price.text"}, "reason": {"text": {"1": "reason.text.1", "2": "reason.text.2", "3": "reason.text.3", "4": "reason.text.4"}, "title": {"1": "reason.title.1", "2": "reason.title.2", "3": "reason.title.3", "4": "reason.title.4"}}, "Required": "Required", "section": "Advance platform", "security": {"header": "security.header", "header2": "security.header2", "logo": {"1": "security.logo.1", "2": "security.logo.2", "3": "security.logo.3"}, "text": "security.text", "text1": "security.text1", "text2": "security.text2"}, "String must contain at least 6 character(s)": "String must contain at least 6 character(s)", "Token has expired or is invalid": "<PERSON><PERSON> has expired or is invalid", "user_id": {"next": "user_id.next"}}