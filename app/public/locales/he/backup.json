{"about": {"about_us": {"header": "מי אנחנו"}, "buy": "לרכישת ביטוח", "header": "קליק אחד ותוכלו לחסוך עד עשרות אלפי שקלים בביטוח.", "profile": {"1": {"name": "ג'ורדן אלבז", "role_en": "Co-Founder & CEO", "role_he": "מייסד ומנכ’’ל משותף", "text": "יזם, מומ<PERSON><PERSON> בתכנון פיננסי, פנ<PERSON><PERSON><PERSON><PERSON><PERSON> וניהול סיכונים. בעל רישיון ממשרד האוצר, מנהל הון, ביטו<PERSON> וחסכונות למאות משפחות בישראל. מעל 10 שנות ניסיון בביטוח, חסכון ושוק ההון. בוגר קורסים בתחום משכנתאות, נדל''ן, שוק ההון פנסיה וביטוח."}, "2": {"name": "גיל שלסקי", "role_en": "Chairman and founder Co", "role_he": "מיי<PERSON><PERSON> ויושב ראש", "text": "מומחה בביטוח חיים וביטוח פנסיוני. כיהן במגוון תפקידי ניהול בכירים בחברות הביטוח הגדולות במשק. מתמחה בבניית מוצרי ביטוח לחברות הגדולות במשק."}, "3": {"name": "מו<PERSON>י שמואל עובד", "role_en": "Co-Founder & CTO", "role_he": "מייסד וסמנכ’’ל טכנולוגיות.", "text": "יזם, מנהל פיתוח וארכיטרקט תוכנה, מעל 20 שנות ניסיון בניהול ופיתוח טכנולוגיות, הקים וניהל קבוצות פיתוח והוביל פיתוח מוצרים מורכבים במספר חברות."}}, "tech": {"header": "טכנולוגיה, ח<PERSON><PERSON><PERSON>ות ושקיפות", "text": "לשמחת כולנו, אנו חיים בעידן בו הטכנולוגיה יוצאת מן הכלל ועוזרת למין האנושי באינספור תחומים כדי להפוך את החיים לטובים יותר. מהיום, גם בביטוחים שלכם, סמאפי תוכל לבצע לכם אופטימיזציה באמצעות טכנולוגיה ייחודית שפיתחנו, ובכך לחסוך לכם כאבי ראש, זמן יקר והמון כסף, תוך כדי שקיפות מלאה, פשטות וחווית שירות שטרם הכרתם. רוב האנשים נאלצים לסמוך על מה שנציגי המכירות אומרים להם, אבל עם Smapy זה אחרת - סמאפי מציגה לכם את הצעות הביטוח המשתלמות ביותר ותמליץ לכם על הביטוח הטוב ביותר. איך אנחנו עושים זאת? פשוט מאוד, יצרנו מודל עסקי מול חברות הביטוח בו אנו מקבלים עמלה אחידה וזהה, כך אנו יכולים להבטיח לכם שההצעות וההמלצות שתקבלו מאיתנו הן שקופות, אובייקטיביות ונטולות אינטרס. סמאפי מחפשת את ההצעות הטובות ביותר בין חברות הביטוח ובין עשרות מסלולים שהן מציעות עד שאתם מקבלים את ההצעה המשתלמת ביותר. אם תמצאו הצעה טובה יותר – תקבלו מאיתנו שירות ללא כל עלות! חשוב לציין שלצד הטכנולוגיה, תוכלו לקבל שירות ממומחי ביטוח ופיננסים שישמחו לעזור ולתת לכם את המענה הטוב ביותר לכל שאלה או בקשה."}, "vision": {"header": "החזון של סמאפי", "header_2": "לשנות את עולם הביטוח לפשוט, שקוף ומשתלם לכולם!", "more_info": "עוד על השירות שלנו", "text": "סמאפי הוקמה כדי להיות הפתרון הטוב ביותר לרכישת הביטוח הטוב והזול ביותר, לכל אחד ואחת. חרטנו על דיגלנו את נושא השקיפות, החדשנות והשירות כדי לאפשר לכל אדם בכל גיל לקבל את הביטוח הטוב והזול ביותר עבורו, מבלי שיצטרך לעבור מלחמת התשה, או להתבאס ששומע את המילה ביטוח. הכל בפשטות, שקיפות ונוחות מירבית וזאת באמצעות הטכנולוגיה שפיתחנו, יחד עם הליווי האנושי, מאפשרים לנו להיות הפתרון הטוב ביותר עבורכם."}}, "advisor": {"EndHeader": {"line1": "<PERSON><PERSON><PERSON> תודה שבחרת", "line2": "הזמנתך התקבלה בהצלחה!", "line3": "הקישור להשלמת התהליך נשלך ללקוחות, ברגע שיסיימו נתחיל בתהליך הפקת הפוליסה!", "line4": "אישור ההזמנה והעתק פוליסה ישלחו אליך ואל הלקוח."}}, "auth": {"advisor": {"subtitle": "שמחים לראותך שוב!", "title": "כניסת יועצים/סוכנים"}, "customer": {"subtitle": "כמה קליקים וגם לך יהיה ביטוח חכם שמתעדכן עם הזמן, לשם כך נצטרך את הפרטים הבאים:", "title": "שמחים שהגעת, נעים להכיר"}}, "Bank name is required": "Bank name is required", "blog": {"action": "blog.action"}, "branch name is required": "branch name is required", "customer": {"EndHeader": {"line1": "תודה שרכשת ביטוח חיים למשכנתה ב- Smapy", "line2": "הזמנתך התקבלה בהצלחה!", "line3": "לצפייה והדפסת תנאי הביטוח המלאים", "line4": "אישור ההזמנה ועיקרי הביטוח נשלחו ל- <EMAIL>"}, "form": {"header": {"birth_date": "תאריך לידה", "dangerous_hobby": "תחביב", "dangerous_hobby_desc": "תיא<PERSON><PERSON> התחביב", "family_status": "מצב משפחתי", "first_name": "שם פרטי", "gender": "<PERSON><PERSON>ן", "last_name": "שם משפחה", "occupation": "עיסוק", "smoking_stop_month": {"value_one": "ל<PERSON><PERSON><PERSON> חודש", "value_two": "לפני חודשיים", "value_other": "לפני {{count}} חודשים", "title_male": "מתי הפסיק?", "title_female": "מתי הפסיקה?"}, "smoking_male": "האם מעשן?", "smoking_female": "האם מעשנת?"}}, "lifeInsurance": {"summary": {"bank_name": "<PERSON><PERSON><PERSON> מלווה", "ins_start_date": "תאריך תחילת הביטוח", "max_date": "תאריך סיום הביטוח", "mortgage_years": {"title": "תקופת הביטוח הכוללת"}, "payment": {"average": "תשלום חודשי ממוצע", "first_year": "תשלום חודשי בשנה הראשונה", "label": "סה''כ לתשלום עבור כלל המבוטחים", "note": "*הפרמיה משתנה כל שנה על פי גילו הביטוחי של המבוטח וצמודה למדד בהתאם ליתרת ההלוואה", "total_payment": "תשלום מצטבר עד סיום המשכנתא"}, "save": "בעדכון ביטוח זה חסכת כ- {{total}} ₪ במצטבר ו- {{monthly}} ₪ בכל חודש. נתראה בעדכון הבא!", "subtitle": "פרטי הביטוח שבחרת", "total": "סכום הביטוח למשכנתא", "tracks_label": "סה״כ מסלולים"}}}, "dangerous_hobby_validation": "dangerous_hobby_validation", "dashboard": {"newInsurance": "dashboard.newInsurance"}, "dashboard_advisor": {"columns": {"birthDate": "dashboard_advisor.columns.birthDate", "catd_id": "dashboard_advisor.columns.catd_id", "createdAt": "dashboard_advisor.columns.createdAt", "email": "dashboard_advisor.columns.email", "first_name": "dashboard_advisor.columns.first_name", "last_name": "dashboard_advisor.columns.last_name", "phone": "dashboard_advisor.columns.phone"}}, "end": {"doing_good": {"line1": "עושים טוב", "line2": "אנו תורמים מהרווחים שלנו בשמך לאחד מהגופים הבאים, למי היית רוצה שנתרום?", "line3": "תודה."}}, "family_status": {"Divorced_male": "גרוש", "Divorced_female": "גרושה", "Married_male": "נשוי", "Married_female": "נשואה", "Seperated_male": "פרוד", "Seperated_female": "פרודה", "Single_male": "רווק", "Single_female": "רווקה", "Widowed_male": "אל<PERSON><PERSON>", "Widowed_female": "אלמנה"}, "faqData": {"contact": {"header": "צריכים מידע נוסף?", "text": "אנו זמינים לכל שאלה או בקשה!"}, "header": "שאלות ותשובות נפוצות", "questions": {"header": "שאלות נפוצות"}, "text": "משהו לא ברור? ריכזנו עבורכם את השאלות הנפוצות ביותר"}, "footer": {"copyright": "כל הזכויות שמורות ל-Smapy © סמאפי סוכנות לביטוח בע\"מ (רישיון: 516248580)", "help": {"header": "עזרה", "list": {"1": "<PERSON><PERSON><PERSON>", "2": "FAQ"}}, "info": {"header": "מידע", "list": {"1": "איך זה עובד", "2": "אודות", "3": "תנ<PERSON>י שימוש"}}, "life_ins": {"header": "ביטו<PERSON> חיים", "list": {"1": "חשוב לדעת", "2": "השוואת ביטוח חיים", "3": "ביטו<PERSON> חיים"}}, "map": {"header": "מפת אתר", "list": {"1": "מאמרים", "2": "פרופיל", "3": "הרשמה", "4": "כניסה"}}, "mortgage_life_ins": {"header": "ביטו<PERSON> חיים למשכנתה", "list": {"1": "חשוב לדעת", "2": "השוואת ביטוח חיים למשכנתה", "3": "ביטו<PERSON> חיים למשכנתה"}}, "mortgage_struct_ins": {"header": "ביטוח מבנה למשכנתה", "list": {"1": "תכולה ומבנה", "2": "חשוב לדעת", "3": "השוואת ביטוח מבנה", "4": "ביטו<PERSON> מבנה", "5": "ביט<PERSON><PERSON> תכולה"}}, "structure_ins": {"header": "ביטו<PERSON> מבנה", "list": {"1": "תכולה ומבנה", "2": "חשוב לדעת", "3": "השוואת ביטוח מבנה למשכנתה", "4": "ביטו<PERSON> מבנה", "5": "ביט<PERSON><PERSON> תכולה"}}}, "form_wizard": {"next": "המשך"}, "gender": {"female": "נקבה", "male": "<PERSON><PERSON><PERSON>", "na": "<PERSON><PERSON><PERSON>"}, "health_declaration": {"warning": "health_declaration.warning"}, "how_does_it_work": {"process_end": {"step1": "מוודאים שהפרטים שלך נכונים", "step2": "בוחרים בהצעה המשתלמת ביותר עבורך", "step3": "משלימים פרטים ומסיימים", "step4": {"mortgage": "יש לכם כבר ביטוח משכנתא? העברת הביטוח עלינו"}, "title": {"mortgage": "סיום תהליך רכישת ביטוח משכנתה"}}, "title": {"mortgage": "ביטו<PERSON> חיים למשכנתא"}, "value": "איך זה עובד?"}, "index": {"contact_us": {"email": "אימייל", "phone": "טלפון", "whatsapp": "WhatsApp"}, "floating": {"1": "ביטו<PERSON> חיים", "2": "ביטוח משכנתה", "3": "ביט<PERSON><PERSON> דירה", "link_text": "להשוואה ורכישה"}, "header": "בוא<PERSON> לבדו<PERSON> את מחירי הביטוח שלנו!", "header_2": "סמאפי משיגה לכם את הביטוח המשתלם ביותר, זה בדוק!", "sections": {"1": {"header": "פלטפורמת הביטוח המתקדמת ביותר בישראל", "text": {"1": "<PERSON><PERSON><PERSON> הי<PERSON> פלט<PERSON><PERSON>ר<PERSON>ת ביטוח חדשנית המשלבת טכנולוגיה ומומחי ביטוח ופיננסים אנושיים", "2": "פלטפורמה מבוססת AI, שתעזור לכם לרכוש את הביטוח הנכון והמשתלם ביותר בקליק אחד. ב - Smapy תהנו משקיפות וחדשנות, שתדאג שיהיה לכם את הביטוח הטוב ביותר תמיד באמצעות עדכון הפוליסה והפרטים האישיים שלכם באופן אוטומטי, כך שתוכלו לחסוך זמן יקר ועד מאות אלפי שקלים בעלויות הביטוח."}}, "2": {"header": "איך זה עובד", "steps": {"1": {"text": "כדי שנוכל לאתר עבורכם את ההצעות הטובות ביותר, תצטרכו למלא מס' פרטים, ומיד אחר כך אנו מתחילים בחיפוש ההצעה המשתלמת ביותר.", "title": "היכרות"}, "2": {"text": "<PERSON><PERSON><PERSON> תציג לכם את הצעות הביטוח הטובות והמשתלמות ביותר עבורכם ותעזור לכם לרכוש ביטוח בצורה חכמה שתמנע הפתעות בעתיד.", "title": "קבלת הצעות"}, "3": {"text": "אחרי שתב<PERSON><PERSON>ו בהצעה הטובה ביותר, תוכ<PERSON><PERSON> לקנות את הביטוח און ליין ב- 5 דק' ולחסוך המון כאבי ראש וזמן יקר.", "title": "רכי<PERSON>ה"}, "4": {"text": "מעכשיו תוכלו להיות בראש שקט. סמא<PERSON>י תהיה במעקב אחרי הביטוח שלכם לאורך כל חייו ותדאג לעדכן אתכם בהצעה המשתלמת ביותר תמיד.", "title": "איתכם לאורך כל הדרך"}}}, "3": {"header": "מגוון חברות לבחירך"}, "4": {"header": "ארבע סיבות לבחור ב-", "reasons": {"1": {"text": "בעזרת הטכנולוגיה שפיתחנו, אנו יודעים להתאים לכם את הביטוח הנכון והטוב ביותר, התואם לנתונים האישיים שלכם ממש כמו כפפה ליד.", "title": "ביטוח מותאם אישית"}, "2": {"text": "אחוז העמלה שאנו מקבלים מחברות הביטוח זהה בין כולן ולכן אתם מקבלים את הצעות המחיר הטובות ביותר וללא שום אינטרס.", "title": "השוואת מחירים שקופה"}, "3": {"text": "האלגוריתם שלנו עוקב אחרי מחיר ותנאי הביטוח שלכם ומבצע השוואת מחירים אוטומטית ועדכון להצעה הטובה ביותר שתהיה.", "title": "חדשנות ושקט נפשי"}, "4": {"text": "בכ<PERSON> רכישת ביטוח אנו תורמים חלק מהרווח שלנו לאחד מהארגונים איתם אנו עובדים כאשר התרומה תהיה בשם הלקוח ולא על חשבונו.", "title": "עושים טוב"}}}, "5": {"header": "מה אומרים עלינו"}, "6": {"header": "שאלות נפוצות", "more_faqs": "לשאלות נוספות >"}, "7": {"header": "נשמח לעזור!", "text": "בכל דרך שתבחרו.."}}}, "lifeInsurance": {"customer": {"summary": {"customer_num": "מבוטחים", "mortgage_yrs": "תקופת הביטוח בשנים", "total_sum": "משכנתה כוללת על סך", "track_cnt": "מסלולי הלוואה"}}, "results": {"average": "תשלום חודשי ממוצע", "customers_one": "מבוט<PERSON> אחד", "customers_two": "שני מבוטחים", "customers_other": "מבוטחים {{count}}", "first_year": "תשלום חודשי בשנה הראשונה", "footnote": "*ההצעות תקפות ללקוחות חדשים בלבד", "insurance_company": "חברת הביטוח", "more_info": "מידע נוסף", "sort": {"label": "משך המשכנתה:", "placeholder": "ב<PERSON><PERSON>"}, "total": "סכום הביטוח למשכנתא {{formatted_total}} ₪", "tracks_one": "מסלול אחד", "tracks_two": "שני מסלולים", "tracks_other": "מסלולים {{count}}", "until_end": "תשלום מצטבר עד סיום המשכנתה", "until_sort": "lifeInsurance.results.until_sort", "label": "תוצאות", "step_sub": "קבלת הצעות"}, "label": "ביטו<PERSON> חיים למשכנתה", "customerDetails1": {"label": "פרטים אישיים", "placeholder": "מבוטח", "step_name": "פרטים ראשוניים", "step_sub": "פרטים אישיים", "gender": {"choices": {"male": "<PERSON><PERSON><PERSON>", "female": "נקבה"}, "label": "מהו המין שלך?"}, "smoking": {"choices": {"yes": "כן", "no": "לא", "stop": "הפסקתי בשנתיים האחרונות"}, "label": "האם הינך מעשן/ת כיום?", "hint": "(כולל כל מוצרי הטבק וסיגריה אלקטרונית)"}, "smoking_stop_month": {"label": "לפני כמה חודשים ?"}, "first_name": {"label": "שם פרטי"}, "last_name": {"label": "שם משפחה"}, "family_status": {"choices": {"Single": "רווק/ה", "Married": "נשוי/אה", "Divorced": "גרוש/ה", "Separated": "פרוד/ה", "Widowed": "אלמן/נה"}, "label": "מצב משפחתי"}, "birthDate": {"label": "תאריך לידה"}}, "customerDetails2": {"label": "פרטים כלליים", "placeholder": "מבוטח", "step_sub": "פרטים כלליים", "occupation": {"label": "עיסוק"}, "dangerous_hobby_has": {"choices": {"true": "Yes", "false": "No"}, "label": "האם יש לך תחביב מסוכן?"}, "dangerous_hobby": {"label": "סוג התחביב"}, "dangerous_hobby_desc": {"label": "תיאור"}}, "summarizeDetails": {"label": "סיכום פרטי המשכנתה", "step_name": "קבלת הצעות", "step_sub": "סיכום פרטי המשכנתה"}, "mortgageSelectedResult": {"label": "Results"}, "customerMoreDetails": {"label": "השלמת פרטים אישיים", "placeholder": "מבוטח", "step_name": "השלמת פרטים", "step_sub": "השלמת פרטים אישיים", "first_name": {"label": "שם פרטי"}, "last_name": {"label": "שם משפחה"}, "card_id": {"label": "תעודת זהות"}, "phone": {"label": "מס<PERSON>ר טלפון"}, "email": {"label": "כתובת מייל"}, "city": {"label": "עיר מגורים"}, "street": {"label": "<PERSON>ח<PERSON><PERSON>"}, "street_number": {"label": "מספר בית"}, "apartment_number": {"label": "דירה"}, "same_address_mortgage": {"choices": {"true": "Yes", "false": "No"}, "label": "האם זו<PERSON>י הכתובת של הנכנס הממושכן?"}}, "healthDeclaration": {"eylon": {"step_sub": "Personal details", "physicalData": {"label": "גו<PERSON>ה ומשקל", "height": {"label": "גובה"}, "weight": {"label": "משקל"}}, "isXorHasSmoke": {"_334_335": {"choices": {"true": "כן", "false": "לא"}, "label": "האם אתה מעשן כיום?"}, "_338": {"label": "כמה שנים?"}, "_339": {"label": "כמות סיגריות ליום"}, "_342_cigarettes": {"label": "סיגריות"}, "_342_cigars": {"label": "סיגרים"}, "_342_pipe": {"label": "מקטרת"}, "_342_narghile": {"label": "נרגילה"}, "_344_345": {"choices": {"true": "כן", "false": "לא"}, "label": "האם עישנת בעבר?"}, "_348": {"label": "במשך כמה שנים עישנת?"}, "_350": {"label": "מתי הפסקת לעשן?"}, "_351": {"label": "כמות סיגריות ליום"}}, "isXorHasDrugs": {"_354_355": {"choices": {"true": "כן", "false": "לא"}, "label": "האם הינך צורך סמים?"}, "_past_354_355": {"choices": {"true": "כן", "false": "לא"}, "label": "האם צרכת סמים בעבר?"}, "_358": {"label": "מתי הופסק? (שאלון מספר 7)"}}, "isXorHasAlcohol": {"_360_361": {"choices": {"true": "כן", "false": "לא"}, "label": "האם הינך צורך אלכוה<PERSON>ל כעת יותר מכוס אחת ביום של בירה\\יין או משקה אלכוהולי אחר?"}, "_past_360_361": {"choices": {"true": "כן", "false": "לא"}, "label": "האם צרכת בעבר את הכמות הנ\"ל?"}, "_364": {"label": "מתי הופסק? (שאלון מספר 7)"}}, "hasMedicalExams": {"_366_367": {"choices": {"true": "כן", "false": "לא"}, "label": "האם ב- 10 השנים האחרונות עברת או הומלץ לך לבצע בדיקות כגון:"}, "_370": {"label": "צינתור"}, "_371": {"label": "בדי<PERSON><PERSON> לגילוי גידול"}, "_372": {"label": "ביופסיה"}, "_373": {"label": "CT"}, "_374": {"label": "MRI"}, "_380": {"label": "מי<PERSON><PERSON>י לב"}, "_381": {"label": "אקו-לב"}, "_382": {"label": "בדיקה מיוחדת אחרת"}, "_366_367_details": {"label": "פרט את סוג הבדיקה, מועד, תוצאות הבדיקה וסיבה לביצוע."}}, "_386_387": {"choices": {"true": "כן", "false": "לא"}, "label": "האם אושפזת ב- 5 השנים האחרונות או ידוע לך על הצורך באישפוז או בדיקה פולשנית במהלך השנה הקרובה? (שא<PERSON><PERSON><PERSON> מס' 9)"}, "disabledStatus": {"_390_391": {"choices": {"true": "כן", "false": "לא"}, "label": "אחוזי נכות ומום מולד: האם יש או הייתה לך נכות בגין: מום מולד או נרכש, פציעה, סיבה רפואית כל שהיא?"}, "_390_391_details": {"label": "פרט אחוזי הנכות וסיבת הנכות (שאלון מס' 16)"}}, "_394_395": {"choices": {"true": "כן", "false": "לא"}, "label": "האם הנך נמצא בתהליך לקביעת אחוזי נכות מ: ביטוח לאומי / ממשרד הביטחון ו/או נמצא ב-אי כושר עבודה כעת?"}, "rejectedInsuranceOffer": {"_398_399": {"choices": {"true": "כן", "false": "לא"}, "label": "האם ב- 5 שנים אחרונות הצעתך לביטוח נדחתה על ידי חברת הביטוח או קרן פנסיה? התקבלה בתוספת פרמיה או החרגה?"}, "_398_399_details": {"label": "נא לפרט"}}, "_402_403": {"choices": {"true": "כן", "false": "לא"}, "label": "תרופות: האם אתה נוטל תרופות באו<PERSON>ן קבוע או מצוי כעת בבירור רפואי כלשהו?"}, "workAbsence": {"_406_407": {"choices": {"true": "כן", "false": "לא"}, "label": "האם נעדרת מהעבודה או היית בחופשת מחלה יותר מ- 10 ימים רצופים, בחמש השנים האחרונות?"}, "_406_407_time": {"label": "מתי?"}, "_406_407_reason": {"label": "מדוע?"}}, "_410_411": {"choices": {"true": "כן", "false": "לא"}, "label": "מערכת העצבים והמוח כמו טרשת נפוצה, ניוון שרירים, אפילפסיה, פרקינסון, אירוע מוחי. (שאלון מס' 3)"}, "_414_415": {"choices": {"true": "כן", "false": "לא"}, "label": "בעיה, ה<PERSON>רעה ו/או מחלה נפשית מאובחנת או אישפוז פסיכיאטרי. (שאלון מס' 17)"}, "_418_419": {"choices": {"true": "כן", "false": "לא"}, "label": "מחלות דרכי הנשימה והריאות. (שאלון מס' 2)"}, "_422_423": {"choices": {"true": "כן", "false": "לא"}, "label": "מחלות לב, ה<PERSON>ר<PERSON>ות בלב (שא<PERSON>ון מס' 11). הפרעות ביתר לחץ דם ושומנים בדם שאובחנו ב- 5 השנים האחרונות. (שאלון מס' 12)"}, "_426_427": {"choices": {"true": "כן", "false": "לא"}, "label": "מחלות/ בעיות בדם, קרישה, אנמיה, המופיליה."}, "_430_431": {"choices": {"true": "כן", "false": "לא"}, "label": "הפרעות בד<PERSON><PERSON><PERSON> העיכול, כ<PERSON><PERSON> המרה, הטחול והלבלב (שאלון מס' 6), כב<PERSON> (שאלון מס' 19)."}, "_434_435": {"choices": {"true": "כן", "false": "לא"}, "label": "מחלות כליות ודרכי השתן, דלק<PERSON><PERSON> ואבנים בדרכי השתן ו/או בכליות, דם או חלבון בדרכי השתן, מחלות הערמונית (פרוסטטה). (שאלון מספר 13)"}, "_438_439": {"choices": {"true": "כן", "false": "לא"}, "label": "מחלות חילוף חומרים, מחלות אנדוקריניות, סכרת (שאלון מס' 15), מחלות בלוטת המגן. (שאלון מס' 4)"}, "_442_443": {"choices": {"true": "כן", "false": "לא"}, "label": ".מחלות ובעיות עור (שאלון מס' 18), גידול, מלנומה, מחלות מין, אלרגיות"}, "_446_447": {"choices": {"true": "כן", "false": "לא"}, "label": "מחלות פרקים ורקמות חיבור, דלק<PERSON> פרקים, אריטריטיס, לופוס/ זאבת, פיברומיאלגיה, F.M.F, גאוט (שאלון מס' 1)."}, "_450_451": {"choices": {"true": "כן", "false": "לא"}, "label": "סרטן, גידולים, לרבות שפירים או מחלות ממאירות."}, "_454_455": {"choices": {"true": "כן", "false": "לא"}, "label": "מחלות זיהומיות, איידס, נשא HIV."}, "_458_459": {"choices": {"true": "כן", "false": "לא"}, "label": "מחלות נשים: בעיו<PERSON> גניקולוגיות, בעיות שדיים.(שאלון מס' 14)"}}, "migdal": {"step_sub": "Personal details", "physicalData": {"label": "גו<PERSON>ה ומשקל", "height": {"label": "גובה"}, "weight": {"label": "משקל"}}, "isXorHasSmoke": {"_239_240": {"choices": {"true": "כן", "false": "לא"}, "label": "האם הנך מעשן כיום? (שים לב: השאלה מתייחסת לכל מוצרי הטבק לרבות סיגריה אלקטרונית)"}, "_243": {"label": "ציין מס' סיגריות \\ פעמים שמעשן ביום:"}, "_244": {"label": "ציין מס' סיגריות \\ פעמים שבן \\ בת הזוג מעשנ\\ת ביום:"}, "_245_246": {"choices": {"true": "כן", "false": "לא"}, "label": "? האחרונות בשנתיים עישנת האם"}}, "q_1": {"q_1_rad": {"choices": {"true": "כן", "false": "לא"}, "label": "האם הנך שותה באופן קבוע יותר מ- 2 כוסות אלכוהול ליום? (22)"}, "q_1_details": {"label": "פרט:"}}, "q_2": {"q_2_rad": {"choices": {"true": "כן", "false": "לא"}, "label": "האם הנך צורך או צרכת סמים כעת או בעבר? (22)"}, "q_2_details": {"label": "פרט:"}}, "q_3": {"q_3_rad": {"choices": {"true": "כן", "false": "לא"}, "label": "מחלות ממאירות, גידולים: לרבו<PERSON> גידול שפיר, סרטני וטרום סרטני (42)"}, "q_3_details": {"label": "פרט:"}}, "q_4": {"q_4_rad": {"choices": {"true": "כן", "false": "לא"}, "label": "מערכת העצבים והמוח"}, "q_4_details": {"label": "פרט:"}}, "q_5": {"q_5_rad": {"choices": {"true": "כן", "false": "לא"}, "label": "נפש: לר<PERSON><PERSON><PERSON> דיכאון, תסמונת פוסט טראומטית, הפרעה נפשית אחרת (13)"}, "q_5_details": {"label": "פרט:"}}, "q_6": {"q_6_rad": {"choices": {"true": "כן", "false": "לא"}, "label": "מערכת ריאות ודרכי נשימה (1)"}, "q_6_details": {"label": "פרט:"}}, "q_7": {"q_7_radio": {"choices": {"true": "כן", "false": "לא"}, "label": "מערכת הדם:"}, "_285": {"label": "מערכת לב (2)"}, "_286": {"label": "כלי דם (14)"}, "_287": {"label": "יתר לחץ דם (28)"}, "q_7_details": {"label": "פרט:"}}, "q_8": {"q_8_rad": {"choices": {"true": "כן", "false": "לא"}, "label": "מערכת כליות ודרכי שתן (51)"}, "q_8_details": {"label": "פרט:"}}, "q_9": {"q_9_radio": {"choices": {"true": "כן", "false": "לא"}, "label": "מערכת העיכול (6), לרבות:"}, "_300": {"label": "מעיים"}, "_301": {"label": "<PERSON><PERSON><PERSON> (29)"}, "_302": {"label": "לבלב"}, "q_9_details": {"label": "פרט:"}}, "q_10": {"q_10_rad": {"choices": {"true": "כן", "false": "לא"}, "label": "סוכרת (7)"}, "q_10_details": {"label": "פרט:"}}, "q_11": {"q_11_rad": {"choices": {"true": "כן", "false": "לא"}, "label": "מחלות של מערכת החיסון לרבות: איידס/נשאות, מחלת חום ממושכת מעל ל- 3 חודשים (25)"}, "q_11_details": {"label": "פרט:"}}, "q_12": {"q_12_rad": {"choices": {"true": "כן", "false": "לא"}, "label": "האם נקבעה לך נכות כלשהי ו/או האם הנך נמצא בתהליך לקבלת אחוזי נכות (23)"}, "q_12_details": {"label": "פרט:"}}, "q_13": {"q_13_rad": {"choices": {"true": "כן", "false": "לא"}, "label": "האם במהלך 5 השנים האחרונות אושפזת כתוצאה ממחלה או מתאונה? (3)"}, "q_13_details": {"label": "פרט:"}}, "q_14": {"q_14_rad": {"choices": {"true": "כן", "false": "לא"}, "label": "האם ב- 5 השנים האחרונות עברת בדיקה רפואית אבחנתית ו/או פולשנית ו/או בדיקת הדמיה ונמצא בה ממצא שהצריך ברור/מעקב רפואי?"}, "q_14_details": {"label": "צרף את תוצאות הבדיק/ות ופרט:"}}, "q_15": {"q_15_rad": {"choices": {"true": "כן", "false": "לא"}, "label": "האם יש לך עיסוקים/תחביבים/אורח חיים עם סיכון מיוחד? ברכישת כיסוי לאובדן כושר עבודה או נכות מתאונה, אנו מבקשים להסב את תשומת לבך לרשימת פעילויות של ספורט אתגרי אשר מוחרגות מתכנית הביטוח ואינן מכוסות. הרשימה מופיעה באתר החברה בדף מוצר הביטוח ומתעדכנת מעת לעת. ההחרגה תהיה נכונה למועד קרות מקרה הביטוח. הננו ממליצים לבדוק את רשימת ההחרגות לספורט אתגרי מעת לעת."}, "q_15_details": {"label": "צרף שאלון תחביבים/מקצועי:"}}, "remarks": {"label": "הערות:"}, "medicalInstitution": {"choices": {"1": "<PERSON><PERSON><PERSON><PERSON>", "2": "כללית", "3": "מאוחדת", "4": "לאומית"}, "label": "קופת החולים"}}, "menorah": {"step_sub": "Personal details", "hasDangerHobby": {"choices": {"true": "כן", "false": "לא"}, "label": "האם הנך עוסק בפעילות ספורטיבית אתגרית ו/או תחביב מסוכן? לרשימת פעילויות הספורט/תחביבים אתגריים/מסוכנים יש לפנות לאתר מנורה. במידה והמענה חיובי יש למלא שאלון תחביבים מסוכנים."}, "hasFlightLicense": {"choices": {"true": "כן", "false": "לא"}, "label": "האם הנך בעל רישיון טיס או איש צוות אוויר ? במידה והמענה חיובי יש למלא שאלון טיס."}, "physicalData": {"label": "גו<PERSON>ה ומשקל", "height": {"label": "גובה"}, "weight": {"label": "משקל"}}, "isSmoking": {"status": {"choices": {"true": "כן", "false": "לא"}, "label": "האם הינך מעשן או עישנת במהלך השנתיים האחרונות?"}, "cigarettes": {"label": "סיגריות"}, "cigars": {"label": "סיגרים"}, "electonic_cigarette": {"label": "סיגריה אלקטרונית"}, "narghile": {"label": "נרגילה"}, "amount": {"label": "מספר סיגריות ביום:"}}, "isAlcohol": {"choices": {"true": "כן", "false": "לא"}, "label": "האם הינך שותה כיום או שתית בעבר יותר מ- 2 מנות ביום של משקה אלכוהולי ? (1)"}, "isDrugs": {"choices": {"true": "כן", "false": "לא"}, "label": "האם הינך צורך כעת או צרכת בעבר סמים מסוג כלשהו (אין להצהיר על שימוש חד פעמי) (2)"}, "medicalCheck": {"choices": {"true": "כן", "false": "לא"}, "label": "מהלך בירור תופעה או מחלה שטרם הסתיים: האם הופנית במהלך השנתיים האחרונות ו/או נמצא כיום במהלך הבדיקות הרפואיות ו/או האבחנתיות הבאות שטרם הסתיימו וטרם נקבעה בגינן אבחנה סופית? צינתור,מיפוי, אקו לב, MRI ,CT, אולטראסאונד (שלא כחלק ממעקב הריון), ביופסיה, דם סמוי, גסטרוסקופיה? במידה וכן יש להעביר מסמכים רפואיים בסיום הבירור וקבלת אבחנה חד משמעית."}, "nerveSystem": {"_244_245": {"choices": {"true": "כן", "false": "לא"}, "label": "מוח ומע' עצבים:"}, "nerve": {"label": "מערכת העצבים"}, "brain": {"label": "מוח"}, "sclerosis": {"label": "טרשת נפוצה"}, "daun": {"label": "תסמונת דאון"}, "neurofib": {"label": "נוירו<PERSON><PERSON><PERSON><PERSON>ומטוזיס"}, "gushe": {"label": "גושה"}, "muscle": {"label": "ניוון שרירים"}, "epileps": {"label": "אפילפסיה (3)"}, "perkins": {"label": "פרקינ<PERSON><PERSON>ן"}, "memoryDeter": {"label": "האם פנית לרופא בשל תלונות על ירידה בזיכרון ב-3 השנים האחרונות?"}}, "bloodSystem": {"_255_256": {"choices": {"true": "כן", "false": "לא"}, "label": "לב, מחלות דם וכלי דם:"}, "heart": {"label": "לב (4)"}, "bloodDesease": {"label": "מחלת דם"}, "lungOverflow": {"label": "תסחיף ריאתי"}, "avm": {"label": "מפרצת/AVM"}, "coagulation": {"label": "הפרעות קרישה (5)"}, "dvt": {"label": "DVT (5)"}, "pvd": {"label": "PVD"}, "blockage": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> בעו<PERSON><PERSON><PERSON> צוואר (קרוטיס)"}}, "mental": {"choices": {"true": "כן", "false": "לא"}, "label": "בעיה/הפרעה/מחלה נפשית לרבות דיכאון וחרדה (7)"}, "medicationLastTenYears": {"_268_269": {"choices": {"true": "כן", "false": "לא"}, "label": "המלצה לטיפול תרופתי או דיאטטי ב 10 השנים האחרונות בשל הבעיות הבאות:"}, "bloodPressure": {"label": "לחץ דם (6)"}, "diabetesAny": {"label": "סוכרת מכל סוג לרבות סוכרת הריון (8)"}, "colesterol": {"label": "כולסטרול (9)"}, "trigly": {"label": "טריגליצרידים (9)"}}, "cancer": {"_275_276": {"choices": {"true": "כן", "false": "לא"}, "label": "סרטן וגידולים שפירים:"}, "malignant": {"label": "מחלה ממארת (סר<PERSON><PERSON>)/גידולים ממאירים"}, "benighn": {"label": "גידולים שפירים (11)"}, "preCancer": {"label": "גידולים טרום סרטניים"}}, "digestiveSystem": {"_289_290": {"choices": {"true": "כן", "false": "לא"}, "label": "מע' עיכול:"}, "stomach": {"label": "קיבה(12)"}, "gut": {"label": "מעיים (12)"}, "gullet": {"label": "ושט (12)"}, "spleen": {"label": "טחול"}, "pancreas": {"label": "לב<PERSON><PERSON> (12)"}, "liverDesease": {"label": "מחל<PERSON> כבד (13)"}, "jaundice": {"label": "צהבת (13)"}, "oilyLiver": {"label": "<PERSON><PERSON><PERSON> שומני (13)"}, "pistola": {"label": "פיסטולה"}, "crohn": {"label": "קרוהן/קוליטיס/פרוקטיטיס (12)"}}, "lungsSystem": {"_295_296": {"choices": {"true": "כן", "false": "לא"}, "label": "ריאות ונשימה:"}, "blockageDesese": {"label": "מחלת ריאות חסימתית (COPD/אמפיזמה)"}, "cisticFibro": {"label": "סיסטיק פיברוזיס"}}, "kidneys": {"_303_304": {"choices": {"true": "כן", "false": "לא"}, "label": "כליות ודרכי שתן:"}, "kidney": {"label": "כליות (15)"}, "system": {"label": "מערכת/דרכי השתן (15)"}, "urina": {"label": "שלפוחית שתן (15)"}, "gland": {"label": "בלוטת ערמונית (23)"}}, "infects": {"_311_312": {"choices": {"true": "כן", "false": "לא"}, "label": "מחלות זיהומיות/דלקתית/ מע' חיסון:"}, "hiv": {"label": "איידס/נשאות HIV"}, "tuberculosis": {"label": "שחפת"}, "sercoid": {"label": "סרקואידוזיס"}, "seclarodma": {"label": "סקלרודרמה"}}, "hadSurgery": {"choices": {"true": "כן", "false": "לא"}, "label": "האם עברת ניתוח או יעצו לך לעבור ניתוח במהלך 5 השנים האחרונות ? (25) אין לענות בחיוב על שאלה זו אם הניתוח בוצע בשל בעיה רפואית עליה השבת בשאלות קודמות."}, "hadHospitalized": {"choices": {"true": "כן", "false": "לא"}, "label": "האם אושפזת מעל ל 3 ימים ב 5 השנים האחרונות? (25) אין לענות בחיוב על שאלה זו אם האשפוז בוצע בשל בעיה רפואית עליה השבת בשאלות קודמות."}, "isCaredMedicines": {"_324_325": {"choices": {"true": "כן", "false": "לא"}, "label": "האם טופלת ו/או מטופל כעת בתרופות קבועות או הומלץ לך על טיפול תרופתי קבוע במהלך 5 השנים האחרונות? אין לענות בחיוב על שאלה זו אם הטיפול התרופתי נלקח בשל בעיה רפואית עליה השבת בשאלות קודמות."}, "medicineName": {"label": "שם התרופה:"}, "diagnosis": {"label": "האבחנה בגינה מטופל/הומלץ טיפול:"}}, "eyes": {"_333_334": {"choices": {"true": "כן", "false": "לא"}, "label": "עיניים:"}, "shortage": {"label": "קוצר ראיה (עדשות 8 ומעלה)"}, "desease": {"label": "מחלה/בעיה בעיניים (21)"}, "obaitis": {"label": "אובאיטיס"}, "blind": {"label": "עיוורון (21)"}}, "noseEarThroat": {"_344_345": {"choices": {"true": "כן", "false": "לא"}, "label": "אף/אוזן/גרון:"}, "ears": {"label": "אוזניים (22)"}, "def": {"label": "לי<PERSON><PERSON><PERSON> שמיעה/חירשות (22)"}, "tin": {"label": "טינ<PERSON><PERSON>ן"}, "manyer": {"label": "מחלת מנייר"}, "nose": {"label": "אף (22)"}, "throat": {"label": "גרון (22)"}, "vocalCords": {"label": "מית<PERSON><PERSON>ול(22)"}}, "autoImmune": {"_354_355": {"choices": {"true": "כן", "false": "לא"}, "label": "ראומטו<PERSON>ו<PERSON><PERSON>ה ומחלות אוטואימוניות:"}, "fmf": {"label": "FMF קדחת ים תיכונית משפחתית (16)"}, "fibromi": {"label": "פיברומיאלגיה"}, "tiredSyndrome": {"label": "תסמונת העייפות הכרונית"}, "lopus": {"label": "לופוס (זאבת)"}, "gaut": {"label": "גאוט/היפראוריצמיה"}}, "orthopede": {"_366_367": {"choices": {"true": "כן", "false": "לא"}, "label": "אורטופדיה:"}, "spine": {"label": "גב ועמוד שדרה (17)"}, "density": {"label": "ירידה בצפיפות העצם"}, "fractures": {"label": "שברים (19)"}, "knees": {"label": "ברכיים (18)"}, "elbow": {"label": "מרפ<PERSON>ים (18)"}, "shoulder": {"label": "כתפיים (18)"}, "thighJoint": {"label": "מפרקי ירכיים (18)"}, "ankles": {"label": "קרסוליים (18)"}, "jointDesease": {"label": "מחלת מפרקים/דלקת פרקים"}}, "womenQuestionnare": {"_377_378": {"choices": {"true": "כן", "false": "לא"}, "label": "האם הינך אישה?"}, "womb": {"label": "רחם (24)"}, "ovaries": {"label": "שחלות/חצוצרות (24)"}, "tumor": {"label": "גידולים בשדיים (24)"}, "endomat": {"label": "אנדומטריוזיס"}, "_374_375": {"choices": {"true": "כן", "false": "לא"}, "label": "האם את בהריון כעת?"}, "weightPriorPregnancy": {"label": "משקלך לפני ההריון:"}}, "_381_382": {"choices": {"true": "כן", "false": "לא"}, "label": "האם הינך מוגבל באחת או יותר מהפעולות הבאות: לקום ולשכב, להתלבש ולהתפשט, להתרחץ, לאכל ולשתות, לשלוט על הסוגרים, ניידות/הליכה ו/או הינך משתמש באביזר עזר או בעזרת אדם נוסף לביצוע אחת או יותר מהפעולות המפורטות?"}}, "harel": {"step_sub": "Personal details"}}, "insuranceDetails": {"label": "פרטי ביטוח", "step_sub": "פרטי ביטוח", "insurance_start_date": {"label": "בחירת תאריך תחילת הביטוח"}, "bank_name": {"label": "שם הבנק ממנו לקחת את המשכנתה?", "placeholder": "שם הבנק..."}, "bank_branch_number": {"label": "מספר הסניף", "placeholder": "מספר הסניף..."}}, "summary": {"label": "סיכום פרטי ביטוח", "line1": "באפשרותך לבדוק את מוצרי הביטוח שלך באתר הר הביטוח", "line2": "אם ברצונך שנבצע עבורך את הבדיקה יש", "link1": "אתר הביטוח", "link2": "ללחוץ כאן.", "terms": "אני מסכים ל", "linkTerms": "תנאי השימוש", "step_name": "סיום תהליך", "step_sub": "none", "has_other_life_insurance": {"choices": {"yes": "כן", "no": "לא", "dontRemember": "לא זו<PERSON>ר"}, "label": "האם יש לך פוליסת ביטוח חיים יעודית למשכנתא בחברה אחרת?"}, "other_insurance_want_cancel": {"choices": {"true": "כן", "false": "לא"}, "label": "האם ברצונך לבטל אותה?"}, "other_insurance_send_cancel": {"choices": {"true": "כן", "false": "לא"}, "label": "לשלוח בשמך את בקשת הביטול לחברה האחרת?"}, "other_insurance_cancel_company": {"label": "האם ישנה פוליסת ביטוח חיים יעודית למשכנתא נוספת שברצונך שנבטל?"}, "other_insurance_cancel_number": {"label": "מס׳ הפוליסה"}, "other_insurance_need_help": {"label": "אני לא זוכר/ת - אשמח לעזרה מנציג"}, "request_insurance_check": {"label": "ברצוני לרכוש ללא ביצוע הליך התאמה וכניסה להר הביטוח ע''י נציג החברה"}}, "customerSignature": {"label": "Signature", "step_sub": "Signature"}, "customerEndStep": {"text2": "בעוד כשנתיים, האלגוריתם שלנו יבצע עבורך השוואת מחירים באופן אוטומטי ויעדכן אותך בהצעה הטובה ביותר.", "text3": "לך נותר להינות מהדירה ולהסיר דאגה מליבך.", "question": "האם יש לך צורך גם בביטוח דירה למשכנתה?", "question2": "כשהבנק מבקש לעשות ביטוח משכנתה, בדרך כלל הוא מתכוון לביטוח חיים וביטוח דירה. כרגע רכשת ביטוח חיים למשכנתה, האם יש לך צורך גם בביטוח דירה למשכנתה?", "yesMessage": "עד שתהליך רכישת ביטוח דירה און ליין יהיה מוכן, מומחה מטעמנו יצור איתך קשר לרכישה טלפונית וידאג לך לביטוח הטוב ביותר.", "yes": "כן", "no": "לא", "end": "סיום", "doing_good": {"choices": {"good4": "", "good5": "", "good6": ""}}, "need_build_insurance": {"label": "Need building insurance?"}}, "howDoesItWork": {"label": "איך זה עובד?", "text_1": "ממלאים פרטי לקוח ופרטי משכנתה", "text_2": "מקבלים את ההצעות המשתלמות ביותר", "text_3": "שולחים ללקוח קישור לאישור וסיום רכישה", "text_life4": "קיים ללקוח ביטוח משכנתה? העברת הביטוח עלינו!", "text_mortgage4": "text_mortgage4", "text_property4": "text_property4", "next": "המשך"}, "numberOfCustomers": {"label": "בחירת מספר מבוטחים", "step_name": "פרטים ראשוניים", "step_sub": "מבוטחים", "number_of_customers": {"choices": {"1": "אחד", "2": "שניים"}, "label": "על כמה אנשים רשומה המשכנתה?"}}, "loanDetailsIntro": {"label": "פרטי המשכנתה", "step_sub": "פרטי משכנתה"}, "loanTracksCount": {"label": "פרטי המשכנתה", "step_sub": "פרטי משכנתה", "loan_tracks_count": {"choices": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8"}, "label": "כמה מסלולי הלוואה יש במשכנתה?"}}, "loanTracks": {"label": "פרטי המשכנתה", "step_sub": "פרטי משכנתה", "balance": {"label": "סכום היתרה בהלוואה"}, "endDate": {"label": "תאריך סיום הלוואה"}, "interest_rate": {"label": "<PERSON><PERSON><PERSON><PERSON> הריבית"}, "interest_type": {"choices": {"change": "משתנה", "fixed": "קבועה"}, "label": "סוג ריבית"}, "linkage_type": {"choices": {"fixed": "לא צמוד", "cpi": "צמוד למדד", "dollar": "צ<PERSON>ו<PERSON> לדולר"}, "label": "סוג הצמדה"}, "loan_type": {"choices": {"ballon": "בל<PERSON>ן", "shpizer": "ש<PERSON><PERSON><PERSON><PERSON>", "equal": "קרן שווה"}, "label": "סוג הלוואה"}}, "mortgagePropertyAddress": {"label": "כתובת נכס ממושכן", "step_sub": "כתובת הנכס", "property_city": {"label": "עיר מגורים"}, "property_street": {"label": "<PERSON>ח<PERSON><PERSON>"}, "property_street_number": {"label": "מספר בית"}, "property_apartment_number": {"label": "דירה"}}, "sendLinksToComplete": {"subTitle1": "שליחת קישור להשלמת רכישה", "subTitle2": "נותרו שני שלבים פשוטים ללקוח כדי שנוכל לסיים את התהליך", "subTitle3": "הבקשה תישלח לאישור המבוטחים באמצעות:", "imageTitle1": "בחירת הביטוח", "imageTitle2": "הצהרת בריאות ואישור", "terms": "I confirm all details are correct and complete", "linkTerms": "Terms and Conditions", "step_name": "סיום תהליך", "step_sub": "אישור וסיום", "email_customer1": {"label": "כתובת מייל מבוטח 1"}, "phone_customer1": {"label": "מספר טלפון מבוטח 1"}, "email_customer2": {"label": "כתובת מייל מבוטח 2"}, "phone_customer2": {"label": "מספר טלפון מבוטח 2"}}, "endStep": {"text1": "תודה שבחרת <PERSON>", "text2": "בעוד כשנתיים האלגוריתם שלנו יבצע עבור הלקוח שלך השוואת מחירים באופן אוטומטי ויעדכן אתכם בהצעה הטובה ביותר שתהיה.", "question": "האם יש ללקוח שלך צורך בביטוח מבנה?", "yesMessage": "מעולה! אנו נחייג אליו ונדאג לו לביטוח הטוב ביותר ונעדכן אותך כשנסיים להפיק לו פוליסה.", "yes": "כן", "no": "לא", "end": "סיום", "need_build_insurance": {"label": "Need building insurance?"}}}, "loan_details_intro": {"fill_details": "הזנת פרטי המשכנתה", "subtitle": "המידע מופיע במסמכי הצעת המשכנתה שהלקוח קיבל מהבנק."}, "loan_tracks": {"total_count_one": "בסה\"כ מסלול הלוואה אחד", "total_count_two": "בסה\"כ שני מסלולי הלוואה", "total_count_other": "בסה\"כ {{count}} מסלולי הלוואה"}, "loanTracks": {"index": "מסלול {{page}}"}, "login": {"continue": "login.continue", "label": "שמחים לראותך שוב!", "error_mismatch": "Phone is registered with different id, please check if information is correct", "welcome_continue": "Continue", "id_card_number": {"label": "תעודת זהות"}, "phone": {"label": "מס<PERSON>ר טלפון"}}, "misc": {"input": {"date": {"day": {"placeholder": "יום"}, "month": {"placeholder": "חו<PERSON><PERSON>"}, "year": {"placeholder": "שנה"}}}}, "msw": {"sample-translated-text": "hebrew: MSW Test"}, "nav": {"about": "קצת עלינו", "agent_entry": "כניסת יועצים/סוכנים", "customer_entry": "כניסת לקוחות", "faqData": "שאלות נפוצות", "price": "מ<PERSON><PERSON><PERSON><PERSON>ן", "security": "אבט<PERSON><PERSON> ופרטיות"}, "Not a valid phone number": "מספר טלפון בלתי חוקי", "number_of_customers": {"one": "אחד", "two": "שניים"}, "otp": {"next": "המשך", "placeholder": "שלחנו לך קוד אימות בהודעת SMS למספר {{phone}}"}, "page": {"header": {"hello": "שלום {{name}}!", "logout": "התנתק/י", "my_insurance_portfolio": "התיק הביט<PERSON><PERSON>י שלי"}}, "price": {"0": {"1": "השוואה בין כל חברות הביטוח", "2": "ההצעות המשתלמות ביותר", "3": "המלצה על רכישת ביטוח בצורה חכמה", "4": "השוואת ביטוח קיים להצעה טובה יותר", "5": "מעקב אחרי מח<PERSON>ר ותנאי הביטוח לתמיד"}, "1": {"name": "ביטו<PERSON> חיים למשכנתה", "price": "החל מ- 10 ₪ לחודש"}, "2": {"name": "ביטוח מבנה למשכנתה", "price": "החל מ- 30 ₪ לחודש"}, "3": {"extra_line": "התא<PERSON>ת הביטוח לסטטוס האישי והכלכלי", "name": "ביטוח חיים למשפחה", "price": "החל מ- 7 ₪ לחודש"}, "button": {"buy": "לרכישת ביטוח"}, "cost": "עלות הביטוח", "cost_service": "עלות השירות", "footer": "מצאתם הצעה טובה יותר? קבלו מאיתנו שירות מעקב אחרי הביטוח ללא עלות!", "footer2": "בהתאם למדיניות השירות באתר", "free": "חינם", "header": "המחיר זול, השיר<PERSON><PERSON> יוקרתי", "header2": "מ<PERSON><PERSON><PERSON><PERSON>ן", "text": "מגוון ביטוחים לכל שלב בחיים שיחסכו לכם כמויות של כסף"}, "register": {"id_card_number": {"validation": "register.id_card_number.validation"}, "phone": {"validation": "register.phone.validation"}}, "sample": {"translation_v2": "sample.translation_v2", "translation_v6": "sample.translation_v6"}, "security": {"header": "הפרטיות שלכם היא הכל בשבילנו.", "header2": "אבט<PERSON><PERSON> ופרטיות", "logo": {"1": "security.logo.1", "2": "security.logo.2", "3": "security.logo.3"}, "text1": "אבטחת מידע הוא אחד מהנושאים החשובים ביותר ברכישה און ליין, בטח שמדובר במידע אישי כמו ביטוח ופיננסים, אנחנ<PERSON> בסמאפי עושים הכל כדי לשמור על המידע הפרטי הזה ומחוייבים לא לעשות בו שום שימוש ולא להעביר אותו לאף צד שלישי שלא במסגרת השירות. עם סמאפי אתם יכולים להיות בטוחים ורגועים.", "text2": "גאה להיות מפוקחת על ידי רגולטורים פיננסים: בנק ישראל, רשות שוק ההון, ביטוח וחיסכון והרשות לניירות ערך. לפי הוראותיהם, כל המידע הפיננסי מוצפן ומופרד באופן הרמטי ממערכות עובדי החברה, כך שרק לכם ולמערכת הטכנולוגית יש גישה אליו. אף אחד מאיתנו לא יכול לגשת למידע בלי אישורכם!"}, "signature": {"clear": "signature.clear", "save": "signature.save"}, "smoking": {"no": "לא", "yes": "כן", "stop_male": "הפ<PERSON>י<PERSON>", "stop_female": "הפסיקה"}, "smoking_stop_month_validation": "smoking_stop_month_validation", "successorInfo": {"sameSuccessor": {"label": "Same as customer {{name}}"}}, "summary": {"confirm_details": {"validation": "summary.confirm_details.validation"}}, "test": {"layer1": {"layer2": "default test"}}, "test3": "Test 3 - default test", "uploadIdPicture": "גר<PERSON><PERSON>ה ושחרור", "wizard": {"back": "חזרה", "end": {"button": "סיום"}, "next": "המשך"}, "years_one": "שנה", "years_two": "שנתיים", "years_other": "{{count}} שנים", "contactUsPhone": {"contactDetailsPhone": {"label": "צרו איתי קשר", "header": "מלאו פרטים ונחזור אליכם בהקדם", "product": {"choices": {"life": "ביטו<PERSON> חיים", "life-mortgage": "ביטו<PERSON> חיים למשכנתה", "property": "ביטו<PERSON> מבנה", "property-mortgage": "ביטוח מבנה משכנתה"}, "label": "באיזה ביטוח היית רוצה שנעזור לך?"}, "name": {"label": "שם מלא"}, "contact_info": {"label": "מס<PERSON>ר טלפון"}, "subject": {"label": "נושא"}}, "end": {"label": "<PERSON><PERSON><PERSON> תודה שבחרת", "placeholder": "Thanks", "text1": "Will contact you soon", "text2": "You will enjoy great service"}}, "contactUsEmail": {"contactDetailsEmail": {"label": "צרו איתי קשר", "header": "מלאו פרטים ונחזור אליכם בהקדם", "product": {"choices": {"life": "ביטו<PERSON> חיים", "life-mortgage": "ביטו<PERSON> חיים למשכנתה", "property": "ביטו<PERSON> מבנה", "property-mortgage": "ביטוח מבנה משכנתה"}, "label": "באיזה ביטוח היית רוצה שנעזור לך?"}, "name": {"label": "שם מלא"}, "contact_info": {"label": "מייל"}, "subject": {"label": "הודעה"}}, "end": {"label": "תודה שבחרת <PERSON>", "placeholder": "זה ממש מחמיא ולא מובן מאליו שנרשמת!", "text1": "ניצור קשר בהקדם האפשרי כדי לסייע.", "text2": "נעשה הכל כדי לתת לך חווית שירות שירות שטרם הכרת."}}, "customerCompareAndBuy": {"title_life": "השוואה ורכישת ביטוח חיים", "title_mortgage": "השוואה ורכישת ביטוח משכנתה", "title_property": "השוואה ורכישת ביטוח דירה", "howDoesItWork": {"label": "איך זה עובד?", "text_1": "מתאימים את הביטוח לנתונים האישיים שלכם, ממש כמו כפפה ליד", "text_2": "משווים לכם מחירים ללא אינטרס, ואתם בוחרים בהצעה המשתלמת ביותר", "text_3": "אנו עוקבים אחרי מחיר הביטוח שלכם, כך נדאג שתחסכו עד עשרות אלפי ₪", "text_life4": "יש לכם כבר ביטוח חיים? העברת הביטוח עלינו.", "text_mortgage4": "יש לכם כבר ביטוח חיים למשכנתה? העברת הביטוח עלינו.", "text_property4": "יש לכם כבר ביטוח דירה? העברת הביטוח עלינו.", "next": "להשוואה"}, "end": {"label": "תודה שהתעניינת ב- S<PERSON><PERSON>", "placeholder": "כרגע השירות שלנו ניתן דרך יועצי משכנתאות וסוכני ביטוח.", "text1": "ב<PERSON><PERSON><PERSON>ב יהיה ניתן לרכוש ביטוח סמאפי מהאתר.", "text2": "בסמא<PERSON>י תהנו מחווית שירות שירות שטרם הכרתם."}}, "registrationFlow": {"label": "הרשמה", "userNames": {"label": "שמחים שהגעת, נעים להכיר..", "first_name": {"label": "שם פרטי"}, "last_name": {"label": "שם משפחה"}}, "knowTheAgent": {"label": "שותפות מוצלחת מתחילה בהיכרות טובה!", "placeholder": "קצת על הרקע והניסיון שיש לך בתחום..", "step_sub": "היכרות", "years_experience": {"label": "כמה זמן הנך יועץ/ת משכנתאות?", "placeholder": "שנים..."}, "worker_type": {"choices": {"freelancer": "עצמאי/ת", "hired": "שכיר/ה"}, "label": "האם את/ה:"}, "workplace_name": {"label": "שם מקום העבודה", "placeholder": "מקום עבודה"}, "number_of_files": {"label": "מספר תיקים חדשים בחודש? (ממוצע)", "placeholder": "לא חובה"}}, "uploadIdPicture": {"label": "הזדהות", "step_sub": "הזדהות", "certificate_id_picture": {"label": "צילום תעודת זהות"}, "certificate_id_picture_later": {"label": "אעלה צילום מאוחר יותר"}}, "bankDetails": {"label": "לאן להפקיד לך את הכסף?", "step_sub": "פרטי חשבון", "bank_name": {"label": "בנ<PERSON>", "placeholder": "ב<PERSON><PERSON>"}, "bank_number": {"label": "מס<PERSON>ר בנק", "placeholder": "ב<PERSON><PERSON>"}, "bank_branch": {"label": "שם הסניף", "placeholder": "שם סניף..."}, "bank_branch_number": {"label": "מספר סניף", "placeholder": "ב<PERSON><PERSON>"}, "bank_account": {"label": "מס<PERSON><PERSON> חשבון", "placeholder": "מספר חשבון..."}, "bank_details_later": {"label": "אמלא מאוחר יותר"}}, "agreeToTerms": {"label": "תנאים ואישור", "save": "שמור", "clear": "נקה", "step_sub": "סיום", "signed_terms": {"label": "אני מאשר את התנאים"}}, "end": {"label": "תודה שבחרת להצטרף למשפחת Smapy", "placeholder": "אנחנו ממש נרגשים! ומצפים להתחיל לעבוד יחד ולתת לך כלים שיאיצו את ההתפתחות הפיננסית והמקצועית שלך.", "text2": "נעשה הכל כדי לתת לך וללקוחותיך שירות שטרם הכרתם."}}}