from pathlib import Path
from util.parse_util import fmt_bi
import json


from colorama import Fore
import pandas as pd
from pandas.api.types import is_scalar

from util.logger import get_basic_logger


logger = get_basic_logger(__name__)


def reload_files(*paths: str):
    "Re-create given filepath(s)"
    for item in paths:
        with Path(item).open(mode="w"):
            pass


def content_to_fmt(src: str, target: str):
    """Read text contents then send formatted output to a file."""
    src_path = Path(src)
    target_path = Path(target)
    with src_path.open(encoding="utf8") as fp:
        s = fp.read()
    with target_path.open(mode="w", encoding="utf8") as fp:
        fp.write(fmt_bi(s, direct=True))


def json_to_sheet(src: str, target: str):
    """Reload Excel sheet with current json entries."""
    src_path = Path(src)
    target_path = Path(target)

    def json_to_df(main_obj):
        """Return a DataFrame from given main_obj."""

        def fill_data(obj: dict, prev_keys: tuple = ()):
            """Fill `data` dictionary with json data."""
            for key in obj:
                current_value = obj[key]
                current_loc = prev_keys + (key,)
                if is_scalar(current_value):
                    current_id = ".".join(current_loc)
                    data["key"].append(current_id)
                    data["value"].append(current_value)
                else:
                    fill_data(current_value, prev_keys=current_loc)

        data = dict(key=[], value=[])
        fill_data(main_obj)
        df = pd.DataFrame(data)
        df["format_flag"] = False
        return df

    with src_path.open(encoding="utf-8") as fp:
        json_obj = json.load(fp)

    result = json_to_df(json_obj)
    result["value"] = result.value.str.replace("\n", "\\n", regex=False)
    result.to_excel(target_path, index=False, engine="xlsxwriter")


def sheet_to_json(src: str, target: str):
    """Update json entries with sheet data."""
    src_path = Path(src)
    target_path = Path(target)

    def row_to_json_obj(row: pd.Series, *, obj):
        """Update given json by dataframe row value."""
        steps = row["key"].split(".")
        try:
            for step in steps[:-1]:
                obj = obj[step]
            node = obj[steps[-1]]
            if is_scalar(node):
                res = row["value"]
                if pd.isna(res):
                    res = ""
                if row["format_flag"] and not pd.isna(row["format_flag"]):
                    res = fmt_bi(res, direct=True)
                else:
                    res = fmt_bi(res, direct=False)
                if res != node:
                    obj[steps[-1]] = res
                    logger.info(
                        f"{Fore.LIGHTBLUE_EX}{row['key']!r}{Fore.RESET}"
                        f" {Fore.LIGHTYELLOW_EX}->{Fore.RESET} "
                        f"{Fore.LIGHTBLUE_EX}{res!r}{Fore.RESET}"
                    )
            else:
                logger.warning(
                    f'{Fore.LIGHTYELLOW_EX}ignored {row["key"]!r}, too broad json entry.{Fore.RESET}'
                )
        except KeyError:
            logger.error(
                f'{Fore.LIGHTRED_EX}ignored {row["key"]!r}, non-existent json entry.{Fore.RESET}'
            )

    df = pd.read_excel(src_path).dropna(subset="key")
    df["value"] = df.value.str.replace("\\n", "\n", regex=False)

    with target_path.open(encoding="utf-8") as fp:
        json_obj = json.load(fp)

    for _, row_data in df.iterrows():
        row_to_json_obj(row_data, obj=json_obj)

    with target_path.open(mode="w", encoding="utf-8") as fp:
        json.dump(json_obj, fp, ensure_ascii=False)
