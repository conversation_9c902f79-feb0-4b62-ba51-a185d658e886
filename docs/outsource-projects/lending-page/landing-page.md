# Convert Figma design to Tailwind/NextJS/React

## General
NextJS/React/Tailwind project
Most of the site content already in place, 
need to apply Tailwind style so content will match the Figma design
Site include many forms, they are written in a generic way so no need to style each one of them manually
Work is Frontend only, no backend is needed 

## Current Stack
- NextJS
- React
- Tailwind
- TypeScript
- i18next already configured
- Backend, TRPC, Prisma, Supabase (mostly not relevant, this project is mainly UI)

## Scope of work
- Configure tailwind, fonts and such
- Apply tailwind classes to the current code to fit Figma design
- Add storybook stories to controls to demonstrate the design in a testing environment  

## Notes
- Also the site is in Hebrew all code should be in English, all text should be using t(key) from i18next, keys should be in english   

