from flask import Flask, request, jsonify
import os
import requests
import tempfile
import json
from parse_pdf_extract_tables import parse_pdf_extract_tables

app = Flask(__name__)

@app.route('/extract_table', methods=['POST'])
def extract_table():
    # Get the request data
    data = request.get_json()
    url = data.get('url')
    apikey = data.get('apikey')
    expected_api_key = os.environ.get('API_KEY')

    # Check if the required parameters are provided
    if not url or not apikey:
        return jsonify({"error": "Missing required parameters"}), 400

    if apikey != expected_api_key:
        return jsonify({"error": "Invalid API key"}), 401

    # Download the PDF
    response = requests.get(url)
    
    if response.status_code != 200:
        return json.dumps({"error": "Failed to download PDF"})

    # Save the PDF to a temporary file
    with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as temp_file:
        temp_file.write(response.content)
        temp_file_path = temp_file.name

    try:
        result = parse_pdf_extract_tables(temp_file_path)
        print(f"Proceessed {url} Found {len(result['data']['tables'])} tables in the PDF.")
        return jsonify(result)
    except Exception as e:
        print(e)
        return json.dumps({"error": str(e)})

    finally:
        try:
            os.unlink(temp_file_path)
        except:
            pass

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
