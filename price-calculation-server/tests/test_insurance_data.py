"""Unit tests for insurance data module components."""
import logging


import pandas as pd
import pytest

from containers.insurance_data import Customer, Loan
from input_data import CustomerData, LoanData

logger = logging.getLogger(__name__)


def test_age():
    """Test if valid ages are as inputted."""
    loan = Loan(LoanData(balance=10 ** 6, loanTrackYears=1,
                         interest_rate=1).to_record())
    for age in range(18, 85 + 1):
        customer = Customer(
            CustomerData(age=age, smoking='yes', gender='female').to_record(),
            loan)
        assert int(customer.start_ages.squeeze()) == age
    for age in (list(range(0, 17 + 1)) + list(range(85 + 1, 120 + 1))):
        with pytest.raises(Customer.InvalidCustomerError):
            Customer(
                CustomerData(age=age, smoking='yes',
                             gender='female').to_record(),
                loan)


def test_invalid_age_for_loan():
    loan = Loan(LoanData(balance=10 ** 6, loanTrackYears=2,
                         interest_rate=1).to_record())
    valid_age_but_not_for_loan = CustomerData(age=85, smoking='yes',
                                              gender='female').to_record()

    with pytest.raises(Customer.InvalidCustomerError):
        Customer(valid_age_but_not_for_loan, loan)


def test_net_customer():
    loan = Loan(LoanData(balance=10 ** 6, loanTrackYears=10,
                         interest_rate=1).to_record())
    c1_data = CustomerData(age=24, smoking='yes', gender='female').to_record()
    c2_data = CustomerData(age=75, smoking='yes', gender='male').to_record()
    c1, c2 = Customer(c1_data, loan), Customer(c2_data, loan)
    sm = c1 + c2
    cols = sm._base_tariff.columns
    assert sm.available_companies == (
                c1.available_companies & c2.available_companies), "net companies are not intersection"
    assert c1.loan is c2.loan is sm.loan
    assert sm.age_frame.equals(
        pd.concat([c1.age_frame, c2.age_frame], axis='columns',
                  ignore_index=True))
    assert sm._base_tariff.equals(
        c1._base_tariff.loc[:, cols] + c2._base_tariff.loc[:, cols]), f"base incorrect"
    assert sm.adjusted_tariff.equals(c1.adjusted_tariff.loc[:, cols] + c2.adjusted_tariff.loc[:, cols]), \
        "adjusted not correct!"


def test_customer_iterable_sum():
    loan = Loan(LoanData(balance=10 ** 6, loanTrackYears=11,interest_rate=1).to_record())
    c1 = Customer(CustomerData(age=24, smoking='yes', gender='female').to_record(), loan)
    c2 = Customer(CustomerData(age=75, smoking='yes', gender='male').to_record(), loan)
    c3 = Customer(CustomerData(age=20, smoking='yes', gender='male').to_record(), loan)
    lst = [c1] * 5 + [c2, c3] * 4
    sm =Customer.sum(lst)
    common_companies = lst[0].available_companies
    for c in lst:
        common_companies = (common_companies & c.available_companies)
    assert sm.age_frame.equals(pd.concat([c.age_frame for c in lst], axis='columns',ignore_index=True))
    assert sm.available_companies == common_companies, "error in common companies!"
    expected_tariff = sum(c.adjusted_tariff.loc[:, list(common_companies)] for c in lst)
    assert set(sm.adjusted_tariff.columns)== set(expected_tariff.columns)
    assert sm.adjusted_tariff.loc[:,expected_tariff.columns].equals(expected_tariff)

def test_loan_sum():
    min_loan = Loan(LoanData(balance=10 ** 6, loanTrackYears=2,interest_rate=2).to_record())
    mid_loan = Loan(LoanData(balance=10 ** 6, loanTrackYears=10,interest_rate=2).to_record())
    max_loan = Loan(LoanData(balance=10 ** 6, loanTrackYears=20,interest_rate=5).to_record())
    lst = [min_loan] * 6 + [max_loan] + [mid_loan]
    sm = Loan.sum(lst)
    assert sm.year.equals(max_loan.year)
    assert sm.year_count == max_loan.year_count
    assert sm.month_count_per_year.equals(max_loan.month_count_per_year)
    assert sm.month_count == max_loan.month_count
    assert sm.balance.equals(sum(item.balance.reindex_like(max_loan.balance).fillna(0) for item in lst))


def test_valid_interest():
    with pytest.raises(ValueError):
        Loan(LoanData(balance=10 ** 6, loanTrackYears=2,interest_rate=10.1).to_record())



